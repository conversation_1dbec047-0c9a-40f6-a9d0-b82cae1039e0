<template>
  <div
    v-show="visible"
    ref="publicPopoverRef"
    :class="['public-popover', placement]"
    :style="popoverStyle"
    class="w-[83px]"
  >
    <div class="arrow" :style="arrowStyle"></div>
    <div class="content flex text-[12px] w-fit">
      <div class="flex-shrink-0" @click="onSelect(1)">复制</div>
      <div
        class="mx-4px flex-shrink-0 border-l-[1px] border-[#999] scale-[0.5]"
      ></div>
      <div class="flex-shrink-0" @click="onSelect(2)">加生词</div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  targetElement: HTMLElement,
  content: String,
  visible: Boolean,
})

let placement = $ref('bottom')
const publicPopoverRef: any = $ref(null)
let popoverStyle: any = $ref({})
let arrowStyle: any = $ref({})

// 同步 visible 控制
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      calculatePosition()
    }
  },
)

// 计算气泡位置
function calculatePosition() {
  if (!props.targetElement) return
  arrowStyle = {}
  const appElement: any = document.querySelector('#app')
  const rect = props.targetElement.getBoundingClientRect()
  const viewportHeight = window.innerHeight
  const spaceTop = rect.top
  const spaceBottom = viewportHeight - rect.bottom

  // 判断最佳展示位置（优先 bottom，其次 top）
  if (spaceBottom < 40 && spaceTop > spaceBottom) {
    placement = 'top'
  } else {
    placement = 'bottom'
  }

  // 计算气泡位置
  let top = 0
  if (placement === 'bottom') {
    top = rect.bottom + appElement.scrollTop + 6 //6是气泡尖角高度
  } else {
    top = rect.top + appElement.scrollTop - publicPopoverRef.offsetHeight - 6 // 6是气泡尖角高度
  }
  const popoverWidth = publicPopoverRef.offsetWidth || 83
  const maxLeft = Math.floor(window.innerWidth - popoverWidth / 2)
  let left = rect.left + rect.width / 2
  //超出边界处理
  if (left - popoverWidth / 2 < 0) {
    arrowStyle = { left: `${left}px` }
    left = popoverWidth / 2
  }
  if (left > maxLeft) {
    arrowStyle = { left: `${left - (window.innerWidth - popoverWidth)}px` }
    left = maxLeft
  }
  popoverStyle = {
    left: `${left}px`,
    top: `${top}px`,
    transform: 'translateX(-50%)',
  }
}

function onSelect(action) {
  if (action.id == 1) copy(props.content)
  if (action.id == 2) console.log(`加入生词本`)
}

async function copy(content) {
  await $g.flutter('setClipboard', {
    content,
    tipMsg: '已复制下载链接',
  })
}

defineExpose({ calculatePosition })
</script>

<style scoped lang="scss">
.public-popover {
  position: absolute;
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 2000;
  font-size: 14px;
  width: fit-content;
  padding: 6px;
  white-space: pre-wrap;
}

.content {
  position: relative;
  z-index: 1;
}

.arrow {
  width: 0;
  height: 0;
  border-style: solid;
  position: absolute;
  z-index: 0;
}

/* 下方显示 */
.public-popover.bottom .arrow {
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 6px 6px;
  border-color: transparent transparent white transparent;
}

/* 上方显示 */
.public-popover.top .arrow {
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 6px 6px 0;
  border-color: white transparent transparent transparent;
}
</style>
