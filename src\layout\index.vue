<template>
  <router-view v-slot="{ Component }">
    <transition :name="transitionName" mode="out-in">
      <keep-alive :include="keepAliveArr">
        <component :is="Component" :key="$route.name" />
      </keep-alive>
    </transition>
  </router-view>
</template>

<script lang="ts" setup>
import { useRouterStore } from '@/stores/modules/router'
import { useRoute } from 'vue-router'
import { computed } from 'vue'
const routerStore = useRouterStore()
const { keepAliveArr } = storeToRefs(routerStore)
const route = useRoute()

// 默认使用缩放动画
const DEFAULT_ANIMATION = 'scale'

const transitionName = computed(() => {
  // 从meta获取动画类型，如果没有指定则使用默认动画
  const animationType = route.meta.animationType || DEFAULT_ANIMATION
  return animationType
})
</script>

<style>
/* 轻微放大缩小动画配置代码 */
.scale-enter-from,
.scale-leave-to {
  transform: scale(0.95);
  opacity: 0;
}

.scale-enter-to,
.scale-leave-from {
  transform: scale(1);
  opacity: 1;
}

.scale-enter-active {
  transition: all 0.1s ease-out;
}

.scale-leave-active {
  transition: all 0.1s ease-in;
}

/* 右到左滑入动画 */
.slide-right-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-enter-to,
.slide-right-leave-from {
  transform: translateX(0);
  opacity: 1;
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease;
}

/* 左到右滑入动画 */
.slide-left-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.slide-left-enter-to,
.slide-left-leave-from {
  transform: translateX(0);
  opacity: 1;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s ease;
}

/* 淡入淡出动画 */
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
</style>
