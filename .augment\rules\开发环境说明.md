# 开发环境配置说明

## 系统环境

- **操作系统**: Windows 11 (Build 26100)
- **命令行工具**: PowerShell 7
- **用户规则目录**: E:/work/cursor-mcp-rule

## 开发工具链

### 编辑器配置

- **编辑器**: Cursor1.2 (最新版本)
- **AI 模型**: Claude 4 (claude-4-sonnet)
- **MCP 支持**: 已启用

### 版本控制

- **Git**: 最新版本
- **提交规范**: 使用 yarn git:push 命令
- **MCP 工具**: @cyanheads/git-mcp-server

### Node.js 环境

- **Node.js**: 20.19.3
- **包管理器**: yarn@1.22.22
- **配置文件**: package.json

## MCP 服务配置

已启用的 MCP 服务器:

```json
{
  "mcpServers": {
    "git-mcp-server": "@cyanheads/git-mcp-server",
    "feedback-enhanced": "mcp-feedback-enhanced",
    "sequential-thinking": "mcp-sequential-thinking",
    "browser-tools": "mcp-browser-tools",
    "context7": "mcp-context7"
  }
}
```

## 项目规则文件

- **MCP 智能调用规则.txt** - MCP 工具使用规范 (P0 级)
- **开发环境说明.md** - 本文件，环境配置记录
- **开发规范规则.txt** - Vue3 项目开发规范

## 注意事项

- 所有 AI 交互必须遵循 MCP 智能调用规则
- Git 操作强制使用 git-mcp-server 工具
- 复杂问题优先使用 sequential-thinking 分析
- 文件改动前后必须调用 feedback-enhanced 确认

---

**环境版本**: Win11 + PowerShell7 + Cursor  
**使用场景**: 个人开发环境
