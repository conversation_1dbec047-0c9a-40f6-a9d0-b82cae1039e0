import config from '@/config/index'
import request from '@/utils/request/index'
const { baseURL4, baseURL5 } = config

/* 获取语音token */
export function getAliSpeechToken(data) {
  return request.get(baseURL5 + '/aienglish/aliyun/speech/token', data)
}

// 获取AI英语oss签名
export function getAIEnOssSignature(data) {
  return request.put(baseURL5 + '/aienglish/oss/policy', data)
}

// ----------------主题对话接口开始----------------//
//查询指定年级下主题列表
export function getChatThemeList() {
  return request.get(baseURL5 + '/aienglish/user/theme/getChatThemeList')
}

//查询指定主题下考点列表
export function getChatThemeKnowledgeList(data) {
  return request.get(
    baseURL5 + '/aienglish/user/theme/getChatThemeKnowledgeList',
    data,
  )
}
// ----------------主题对话接口结束----------------//

// 自由对话列表
export function getFreeChatList() {
  return request.get(baseURL5 + '/aienglish/user/free/conversation/list')
}

// 主题对话列表
export function getThemeChatList() {
  return request.get(baseURL5 + '/aienglish/user/theme/conversation/list')
}

// 自由对话详情
export function getFreeChatDetail(data) {
  return request.get(
    baseURL5 + '/aienglish/user/chat/conversation/detail',
    data,
  )
}

// 创建自由对话
export function createFreeChat() {
  return request.post(baseURL5 + '/aienglish/user/free/conversation/create')
}

// 创建主题对话
export function createThemeChat(data) {
  return request.post(
    baseURL5 + '/aienglish/user/theme/conversation/create',
    data,
  )
}

// 获取自由对话欢迎语
export function getFreeChatWelcome(data) {
  return request.get(baseURL5 + '/aienglish/user/chat/welcome/get', data)
}

// 获取主题对话欢迎语
export function getThemeChatWelcome(data) {
  return request.get(baseURL5 + '/aienglish/user/theme/welcome/get', data)
}

/* 点击润色接口 */
export function getImproveInfo(data) {
  return request.post(baseURL5 + '/aienglish/user/chat/message/improve', data)
}

/* 教我回答 */
export function getTeachMeAnswer(data) {
  return request.post(
    baseURL5 + '/aienglish/user/chat/message/suggestion',
    data,
    {
      timeout: 60000,
    },
  )
}

// 获取用户信息
export function getUserInfoApi() {
  return request.get(baseURL5 + '/aienglish/user/settings/info')
}
// 获取语速、是否自动播放等配置信息
export function getConfigApi(data) {
  return request.get(baseURL5 + '/aienglish/user/settings/config', data)
}
// 修改配置信息
export function editConfigApi(data) {
  return request.post(baseURL5 + '/aienglish/user/settings/config', data)
}

// 获取消息反馈项
export function getMessageFeedbackApi(data) {
  return request.get(
    baseURL5 + '/aienglish/user/chat/message/feedback/item',
    data,
  )
}

// 提交消息反馈
export function submitMessageFeedback(data) {
  return request.post(baseURL5 + '/aienglish/user/chat/message/feedback', data)
}
// 获取教材、版本信息
export function getStudyApi() {
  return request.get(baseURL5 + '/aienglish/user/settings/study')
}
// 获取协议列表
export function getSecretListApi(data) {
  return request.get(
    baseURL4 + '/tutoring/common/clientProtocol/enabled/list',
    data,
  )
}

// 获取发言反馈口语发音检测
export function getSpeakTestApi(data) {
  return request.post(
    baseURL5 + '/aienglish/user/chat/message/spoken/assess',
    data,
  )
}

// 音频保存
export function savePcmWav(data) {
  return request.post(baseURL5 + '/aienglish/user/chat/message/audio', data)
}

// 文本翻译接口
export function getTextTranslate(data) {
  return request.post(baseURL5 + '/aienglish/user/chat/message/translate', data)
}

// 获取语法检测及评价
export function getGrammarTestApi(data) {
  return request.post(
    baseURL5 + '/aienglish/user/chat/message/grammar/assess',
    data,
  )
}
