import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/layout/index.vue'

const aiEnRoutes: RouteRecordRaw[] = [
  {
    path: '/ai-en',
    name: 'AiEn',
    component: Layout,
    redirect: '/ai-en/home',
    meta: {
      title: 'AI英语',
    },
    children: [
      {
        path: 'demo',
        name: 'AiEnDemo',
        component: () => import('@/views/demo/aiEnDemo.vue'),
        meta: {
          title: 'demo',
          animationType: 'slide-right',
        },
      },
      {
        path: 'home',
        name: 'AIEnHome',
        component: () => import('@/views/aiEn/home/<USER>'),
        meta: {
          title: '首页',
          keepAliveArr: ['AIEnMineMain', 'AiEnFeedback'],
        },
      },
      {
        path: 'chat',
        name: 'Chat',
        meta: {
          title: '对话',
        },
        children: [
          {
            path: 'theme',
            name: 'AiEnThemeChat',
            component: () => import('@/views/aiEn/chat/theme/index.vue'),
            meta: {
              title: '主题对话',
              keepAliveArr: ['AiEnFeedback'],
            },
          },
          {
            path: 'feedback',
            name: 'AiEnFeedback',
            component: () => import('@/views/aiEn/chat/feedback/index.vue'),
            meta: {
              title: '发言反馈',
            },
          },
        ],
      },
      {
        path: 'game',
        name: 'AIEnGame',
        redirect: '/ai-en/game/wordRelayGameIntroduction',
        meta: {
          title: '游戏',
        },
        children: [
          {
            path: 'wordRelayGameIntroduction',
            name: 'WordRelayGameIntroduction',
            component: () =>
              import('@/views/aiEn/game/wordRelay/gameIntroduction/index.vue'),
            meta: {
              title: '游戏介绍',
            },
          },
          {
            path: 'wordRelayGameMatching',
            name: 'WordRelayGameMatching',
            component: () =>
              import('@/views/aiEn/game/wordRelay/gameMatching/index.vue'),
            meta: {
              title: '游戏匹配',
            },
          },
          {
            path: 'wordRelay',
            name: 'AIEnGameWordRelay',
            component: () => import('@/views/aiEn/game/wordRelay/index.vue'),
            meta: {
              title: '单词接龙',
            },
          },
        ],
      },
      {
        path: 'mine',
        name: 'Mine',
        redirect: '/ai-en/mine/main',
        meta: {
          title: '我的',
        },
        children: [
          {
            path: 'main',
            name: 'AIEnMineMain',
            component: () => import('@/views/aiEn/mine/index.vue'),
            meta: {
              title: '主页',
              animationType: 'slide-right',
            },
          },
          {
            path: 'secret',
            name: 'SecretMain',
            component: () => import('@/views/aiEn/secret/index.vue'),
            meta: {
              title: '隐私协议',
            },
          },
          {
            path: 'secretDetail',
            name: 'SecretDetail',
            component: () => import('@/views/aiEn/secretDetail/index.vue'),
            meta: {
              title: '隐私协议详情',
            },
          },
        ],
      },
    ],
  },
]

export default aiEnRoutes
