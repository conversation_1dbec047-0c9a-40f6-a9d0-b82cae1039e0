{
  "log增强ㅤ🌟🌈⚡": {
    "scope": "javascript,typescript",
    "prefix": "log", // 片段缩写
    "body": [
      "console.log(`⚡[ ${1:$CLIPBOARD} ] >`, ${1:$CLIPBOARD})"
    ],
    "description": "log增强ㅤ🌟🌈⚡",
  },
  "vue3基础片段ㅤ🌟🌈⚡": {
    "scope": "vue",
    "prefix": "v3base",
    "body": [
      "<template>",
      "  <div>",
      "    $1",
      "  </div>",
      "</template>",
      "",
      "<script setup lang=\"ts\"> ",
      "",
      "</script>",
      "",
      "<style lang=\"scss\" scoped>",
      "",
      "</style>",
    ],
    "description": "vue3基础片段ㅤ🌟🌈⚡",
  },
  "g-empty模板ㅤ🌟🌈⚡": {
    "scope": "html",
    "prefix": "g-empty",
    "body": [
      "<g-empty v-show=\"showEmpty\"></g-empty>"
    ],
    "description": "g-empty模板ㅤ🌟🌈⚡",
  },
  "g-chart模板ㅤ🌟🌈⚡": {
    "scope": "html",
    "prefix": "g-chart",
    "body": [
      "  <g-chart class=\"w-350px h-250px\" :option=\"chartOption\" />"
    ],
    "description": "g-chart模板ㅤ🌟🌈⚡",
  },
  "g-chart数据ㅤ🌟🌈⚡": {
    "scope": "javascript,typescript",
    "prefix": "g-chart",
    "body": [
      "const chartOption = reactive({",
      "  xAxis: {",
      "    type: 'category',",
      "    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],",
      "  },",
      "  yAxis: {",
      "    type: 'value',",
      "  },",
      "  series: [",
      "    {",
      "      data: [120, 200, 150, 80, 70, 110, 130],",
      "      type: 'bar',",
      "      showBackground: true,",
      "      backgroundStyle: {",
      "        color: 'rgba(180, 180, 180, 0.2)',",
      "      },",
      "    },",
      "  ],",
      "})",
    ],
    "description": "g-chart数据ㅤ🌟🌈⚡",
  },
  "g-icon模板ㅤ🌟🌈⚡": {
    "scope": "html",
    "prefix": "g-icon",
    "body": [
      "<g-icon name=\"icon-sousuo\" size=\"14\" color=''/>"
    ],
    "description": "g-icon模板ㅤ🌟🌈⚡",
  },
  "g-list模板ㅤ🌟🌈⚡": {
    "scope": "html",
    "prefix": "g-list",
    "body": [
      "<g-list",
      "    ref=\"scrollRef\"",
      "    @pulldown=\"pulldown\"",
      "    @pullup=\"pullup\"",
      "    v-model:data=\"dataList\"",
      "    :pageOption=\"pageOption\"",
      "    url=\"/v2/family/exam/student/dataList\"",
      "  >",
      "    <ListItem",
      "      class=\"active:bg-blue-lightest\"",
      "      :key=\"index\"",
      "      v-for=\"(item, index) in dataList\"",
      "      :info=\"item\"",
      "    ></ListItem>",
      "  </g-list>",
    ],
    "description": "g-list模板ㅤ🌟🌈⚡",
  },
  "g-list数据ㅤ🌟🌈⚡": {
    "scope": "javascript,typescript",
    "prefix": "g-list",
    "body": [
      "let dataList: any = $$ref([])",
      "const pageOption = reactive({",
      "  page: 1,",
      "  page_size: 10,",
      "  total: 0,",
      "})",
      "async function pulldown() {",
      "  pageOption.page = 1",
      "  await getDataListApi()",
      "}",
      "async function pullup() {",
      "  pageOption.page += 1",
      "  await getDataListApi(true)",
      "}",
      "/* 获取列表 */",
      "async function getDataListApi(up?) {",
      "  await getDataList({ ...pageOption }).then((res) => {",
      "    if (res?.list?.length) {",
      "      dataList = up ? dataList.concat(res.list) : res.list",
      "      pageOption.total = res.total",
      "    } else {",
      "      dataList = []",
      "    }",
      "  })",
      "}",
    ],
    "description": "g-list数据ㅤ🌟🌈⚡",
  },
  "g-loading模板ㅤ🌟🌈⚡": {
    "scope": "html",
    "prefix": "g-loading",
    "body": [
      "<g-loading class=\"h-200px\" v-show=\"showLoading\"></g-loading>",
      "",
    ],
    "description": "g-loading模板ㅤ🌟🌈⚡",
  },
  "g-lottie模板ㅤ🌟🌈⚡": {
    "scope": "html",
    "prefix": "g-lottie ",
    "body": [
      "<g-lottie :options=\"lottieOptions\" @animCreated=\"animCreated\"> </g-lottie>",
    ],
    "description": "g-lottie模板ㅤ🌟🌈⚡",
  },
  "g-lottie数据ㅤ🌟🌈⚡": {
    "scope": "javascript,typescript",
    "prefix": "g-lottie ",
    "body": [
      "const lottieOptions = {",
      "  path: 'https://frontend-cdn.qimingdaren.com/cloud-school/exam/feiji.json',",
      "  loop: true,",
      "  renderer: 'svg',",
      "  autoplay: true,",
      "}",
      "",
      "function animCreated(anim) {",
      "  anim.setSpeed(1.4)",
      "}",
    ],
    "description": "g-lottie数据ㅤ🌟🌈⚡",
  },
  "g-table模板ㅤ🌟🌈⚡": {
    "scope": "html",
    "prefix": "g-table",
    "body": [
      "<g-table :tableOption=\"tableOption\">",
      "    <template #name=\"{ row, index }\"> </template>",
      "  </g-table>",
    ],
    "description": "g-table模板ㅤ🌟🌈⚡",
  },
  "g-table数据ㅤ🌟🌈⚡": {
    "scope": "javascript,typescript",
    "prefix": "g-table",
    "body": [
      "const tableOption = reactive({",
      "  column: [",
      "    { prop: 'levelName', label: 'levelName', width: '120px' },",
      "    { prop: 'lowestScore', label: 'lowestScore' }, ",
      "    { prop: 'studentNum', label: 'studentNum' },",
      "  ],",
      "  data: [],",
      "})",
    ],
    "description": "g-table数据ㅤ🌟🌈⚡",
  },
  "g-page模板ㅤ🌕": {
    "scope": "html",
    "prefix": "g-page",
    "body": [
      "<g-page :pageOptions=\"pageOptions\" @change=\"getList\">",
      "  </g-page>",
    ],
    "description": "g-page模板ㅤ🌕",
  },
  "g-preview-media模板ㅤ🌕": {
    "scope": "html",
    "prefix": "g-preview-media",
    "body": [
      "<g-preview-media :list=\"list\" :single-size=\"{width: '26.67vw', height: 'auto'}\" />",
    ],
    "description": "g-preview-media模板ㅤ🌕",
  },
  "$refㅤ🌟🌈⚡": {
    "scope": "javascript,typescript",
    "prefix": "ref",
    "body": [
      "let $1= \\$ref($2)"
    ],
    "description": "$refㅤ🌟🌈⚡",
  },
  "$ref<type>ㅤ🌟🌈⚡": {
    "scope": "javascript,typescript",
    "prefix": "ref",
    "body": [
      "let $1= \\$ref<${2|any,number,string,boolean,object,array|}>($3)",
    ],
    "description": "$refㅤ with <>🌟🌈⚡",
  },
  "$computedㅤ🌟🌈⚡": {
    "scope": "javascript,typescript",
    "prefix": "computed",
    "body": [
      "const $1 = \\$computed(() => {",
      "  return $2",
      "})"
    ],
    "description": "$computedㅤ🌟🌈⚡",
  },
  "van-haptics-feedback": {
    "prefix": "feedback",
    "scope": "html,css",
    "body": [
      "van-haptics-feedback"
    ],
    "description": "为元素添加触碰反馈效果",
  },
  "g-navbar模板ㅤ🌟🌈⚡": {
    "scope": "html",
    "prefix": "g-navbar",
    "body": [
      "<g-navbar />"
    ],
    "description": "g-navbar模板ㅤ🌟🌈⚡",
  },
  "g-virtual-list模板ㅤ🌕": {
    "scope": "html",
    "prefix": "g-virtual-list",
    "body": [
      "<g-virtual-list",
      "  :list-data=\"list\"",
      "  item-key=\"questionId\"",
      "  :page-option=\"pageOption\"",
      "  :size-dependencies=\"['questionTitle']\"",
      "  :min-item-size=\"155\"",
      "  @pullup=\"pullup\">",
      "  <template #default=\"{ item, index }\"></template>",
      "</g-virtual-list>",
    ],
    "description": "g-virtual-list模板ㅤ🌕",
  },
  "g-markdown模板ㅤ🌕": {
    "scope": "html",
    "prefix": "g-markdown",
    "body": [
      "<g-markdown",
      "  :text=\"content\"",
      "  mode=\"stream\">",
      "</g-markdown>",
    ],
    "description": "g-markdown模板ㅤ🌕",
  },
}