/**
 * 表格触摸处理Hook
 * 用于处理表格滚动容器的触摸事件，避免与外层tab切换产生冲突
 */

/** Hook返回值类型 */
interface ITableTouchHandler {
  /** 绑定表格容器触摸事件 */
  bindTableTouchEvents: (container: HTMLElement) => void
  /** 清理所有事件监听器 */
  cleanupEvents: () => void
}

/**
 * 表格触摸处理Hook
 * @returns 触摸事件处理相关方法
 */
export function useTableTouchHandler(): ITableTouchHandler {
  // 触摸起始位置
  let touchStartX = $ref(0)
  let touchStartY = $ref(0)

  // 已绑定事件的元素列表
  let boundElements = $ref<Element[]>([])

  /**
   * 处理触摸开始事件
   * @param event 触摸事件
   */
  function handleTouchStart(event: Event): void {
    const touchEvent = event as TouchEvent
    touchStartX = touchEvent.touches[0].clientX
    touchStartY = touchEvent.touches[0].clientY
  }

  /**
   * 处理触摸移动事件
   * @param event 触摸事件
   */
  function handleTouchMove(event: Event): void {
    const touchEvent = event as TouchEvent
    if (!touchEvent.touches.length) return

    const touchCurrentX = touchEvent.touches[0].clientX
    const touchCurrentY = touchEvent.touches[0].clientY
    const deltaX = Math.abs(touchCurrentX - touchStartX)
    const deltaY = Math.abs(touchCurrentY - touchStartY)

    // 检测是否为水平滑动（水平移动距离大于垂直移动距离）
    if (deltaX > deltaY && deltaX > 10) {
      const target = event.target as HTMLElement
      const tableWrapper = target.closest('.table-wrapper')

      if (tableWrapper) {
        const hasHorizontalScroll =
          tableWrapper.scrollWidth > tableWrapper.clientWidth

        // 只有当表格真的可以水平滚动时才阻止事件冒泡
        if (hasHorizontalScroll) {
          event.stopPropagation()
        }
      }
    }
  }

  /**
   * 绑定表格容器的触摸事件
   * @param container 容器元素
   */
  function bindTableTouchEvents(container: HTMLElement): void {
    // 先清理之前绑定的事件
    cleanupEvents()

    // 查找所有表格包装器
    const tableWrappers = container.querySelectorAll('.table-wrapper')

    tableWrappers.forEach((wrapper) => {
      // 添加事件监听器
      wrapper.addEventListener('touchstart', handleTouchStart, {
        passive: true,
      })
      wrapper.addEventListener('touchmove', handleTouchMove, { passive: false })

      // 记录已绑定的元素
      boundElements.push(wrapper)
    })
  }

  /**
   * 清理所有事件监听器
   */
  function cleanupEvents(): void {
    boundElements.forEach((element) => {
      element.removeEventListener('touchstart', handleTouchStart)
      element.removeEventListener('touchmove', handleTouchMove)
    })
    boundElements = []
  }

  // 组件卸载时自动清理
  onUnmounted(() => {
    cleanupEvents()
  })

  return {
    bindTableTouchEvents,
    cleanupEvents,
  }
}
