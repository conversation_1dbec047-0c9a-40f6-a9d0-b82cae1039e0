<template>
  <van-popup
    v-model:show="show"
    position="bottom"
    round
    teleport="#app"
    closeable
    :style="{ minHeight: '50%', maxHeight: '80%' }"
    @open="open"
    @close="resetAll"
  >
    <div class="translate-dialog h-full pt-48px">
      <!-- 初始化状态 -->
      <div v-if="currentState == 'initial'" class="px-16px mb-24px pt-13px">
        <div class="text-center">
          <div class="text-20px font-500 mb-6px">请说中文</div>
          <span class="text-14px text-gray-dark">用中文表达你的意图</span>
        </div>
      </div>
      <template v-if="dialogType === 'onlyEn'">
        <div class="text-center text-16px text-[#5C5F66] pt-47px -mb-30px">
          朗读你要发出的单词
        </div>
      </template>
      <!-- 实时显示区域 -->
      <template v-if="currentState !== 'initial' && dialogType != 'onlyEn'">
        <div
          class="border-b border-dashed border-[#E6EAF2] pb-32px pl-12px pr-20px"
        >
          <span class="text-14px">{{ zhText }}</span>
          <!-- 打字机光标效果 -->
          <span
            v-if="currentState === 'recording'"
            class="text-14px text-blue-500 animate-pulse"
            >...</span
          >
        </div>
      </template>

      <!-- 英文翻译显示区 -->
      <div v-if="currentState == 'completed'" class="pl-12px pr-20px mb-34px">
        <div
          v-if="dialogType != 'onlyEn'"
          class="text-[#7A8499] mt-16px mb-10px"
        >
          如符合你的意图，可跟读英文并发送哦～
        </div>
        <div class="text-16px">{{ englishText }}</div>
      </div>

      <template v-if="currentState == 'completed'">
        <div class="flex items-center justify-end text-12px pr-20px">
          <div
            v-if="dialogType == 'translate'"
            class="flex items-center justify-center bg-[#F2F5FF] px-4px py-6px rounded-[4px] mr-10px"
            @click="resetAll('nodelay')"
          >
            <g-icon name="ri-restart-line" size="14" color="#5C5F66" />
            <span class="text-gray-dark pl-2px">重录中文</span>
          </div>
          <div
            v-if="dialogType != 'onlyEn'"
            class="w-30px h-30px bg-[#F2F5FF] rounded-[4px] flex items-center justify-center"
            @click="handlePlay"
          >
            <img
              class="w-16px h-16px"
              :src="
                isPlaying
                  ? $g.tool.getFileUrl('aiEn/home/<USER>')
                  : $g.tool.getFileUrl('aiEn/home/<USER>')
              "
              alt=""
            />
          </div>
          <slot name="otheruse"></slot>
        </div>
      </template>

      <div class="flex flex-col items-center justify-center mt-88px">
        <div
          class="rounded-full w-60px h-60px flex items-center justify-center mb-8px relative"
          :style="buttonStyle"
          @touchstart.stop="handlePressStart"
          @mousedown.stop="handlePressStart"
          @touchend.stop.prevent="handlePressEnd"
          @touchcancel.stop.prevent="handlePressEnd"
          @mouseup.stop.prevent="handleTouchEnd"
        >
          <!-- 麦克风图标 - 初始状态和完成状态 -->
          <g-icon
            v-if="
              currentState === 'initial' ||
              (currentState === 'completed' && !isEnglishRecording)
            "
            name="ri-mic-line"
            size="30"
            color="#fff"
            class="pointer-events-none"
          />

          <!-- 录音中状态 - 波形动画 -->
          <WaveAnimation
            v-if="currentState === 'recording' || isEnglishRecording"
            :barCount="5"
            :maxHeight="6"
            :minHeight="3"
            class="h-16px"
          />

          <!-- 翻译中状态 - 翻译图标 -->
          <div
            v-if="currentState === 'translating'"
            class="flex items-center justify-center relative pointer-events-none"
          >
            <img
              class="w-[24px] h-[24px] animate-spin"
              :src="$g.tool.getFileUrl('aiEn/translate_2.png')"
            />
            <span
              class="text-15px text-[#6A83FF] absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 font-600 pl-1px"
              >A</span
            >
          </div>
        </div>

        <!-- 状态文字 -->
        <span class="text-13px text-[#7A8499] mb-16px">{{ buttonText }}</span>
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { useAliASR } from '../hooks/useAliAudio'
import WaveAnimation from './WaveAnimation.vue'
import { useStreamTTSPlayer } from '@/views/aiEn/home/<USER>/hooks/useStreamTTSPlayer'

const props = withDefaults(
  defineProps<{
    dialogType: string // 场景：中译英translate、教我回答teachMeAnswer、仅发送英文onlyEn
    teachMeInfo?: any // 教我回答场景下数据回显
    messageId?: number | string
    conversationId?: number | string
  }>(),
  {
    dialogType: 'translate',
  },
)
// 状态管理
let currentState = $ref<'initial' | 'recording' | 'translating' | 'completed'>(
  'initial',
)

const asrOptions = reactive({
  from: 'zh',
  to: 'en',
  needUpload: false,
  conversationId: null,
  messageId: null,
  isCancel: false,
}) as any

// 阿里云实时识别
const { deviceMicMsg, startAliRealTimeRecording, stopAliRealTimeRecording } =
  useAliASR(
    {
      onRecordComplete,
      onmessage,
      onerror,
    },
    asrOptions,
  )
const show = defineModel<boolean>('show')
const emits = defineEmits(['sendAudio', 'resetDialogType'])

let isMultiplePress = false
let zhText = $ref('')
let typewriterTimer: any = $ref(null)
let englishText = $ref('')
let isPlaying = $ref(false) // 音频播放
let isEnglishRecording = $ref(false) // 是否为英文录音模式

// 长按标识
let longPressTimer: any = $ref(null)
let isLongPress = $ref(false)

// 调试日志总开关 (与 AiChat/index.vue 保持一致)
const showDebugLog = false

const ttsPlayer = useStreamTTSPlayer({
  showDebugLog,
  onTaskStart: (taskId) => {
    isPlaying = true
  },
  onTaskComplete: (taskId, summary) => {
    isPlaying = false
  },
  onTaskPlayStateChange: (taskId, playing) => {
    isPlaying = playing
  },
})

const buttonText = $computed(() => {
  if (isEnglishRecording) {
    return '松开发送英文'
  }

  switch (currentState) {
    case 'initial':
      return '按住说话'
    case 'recording':
      return '松开结束'
    case 'translating':
      return '翻译中，请稍后...'
    case 'completed':
      if (props.dialogType == 'onlyEn') {
        return '按住朗读'
      }
      return '按住跟读'
    default:
      return '按住说话'
  }
})

const buttonStyle = $computed(() => {
  switch (currentState) {
    case 'translating':
      return 'background: #E2E6FE'
    default:
      return 'background: linear-gradient(to right, #4a84ff, #8283ff)'
  }
})

/**
 * 打开弹窗
 */
function open() {
  asrOptions.conversationId = props.conversationId
  asrOptions.messageId = props.messageId
  if (props.dialogType == 'onlyEn') {
    currentState = 'completed'
  } else if (props.dialogType == 'teachMeAnswer') {
    currentState = 'completed'
    zhText = ''
    englishText = props.teachMeInfo?.content || ''
  }
}

// 打字机效果函数
function typewriterEffect(targetText, type = 'zh') {
  if (typewriterTimer) {
    clearTimeout(typewriterTimer)
  }

  const newText = targetText
  const currentLength = type === 'zh' ? zhText.length : englishText.length
  const newLength = newText?.length

  let index = currentLength
  function typeNextChar() {
    if (index < newLength) {
      if (type === 'zh') {
        zhText = newText.slice(0, index + 1)
      } else {
        englishText = newText.slice(0, index + 1)
      }
      index++
      typewriterTimer = setTimeout(typeNextChar, 50)
    }
  }

  typeNextChar()
}

// 中间结果回调
function onmessage({ code, data = {} }: { code: any; data: any }) {
  if (code == 5) {
    resetAll('nodelay')
    return
  }
  if (currentState == 'completed') return
  if (code == 2 && data && data.fromContent) {
    typewriterEffect(data.fromContent, 'zh')
  }
}

function onRecordComplete(data: any) {
  // 第二步英文
  if (currentState == 'completed') {
    if (data?.audioUrl && data?.sentences?.length) {
      emits('sendAudio', {
        convertType: 2,
        content: data?.sentences?.[0]?.fromContent,
        audio: data.audioUrl,
      })
      show.value = false
      nextTick(resetAll)
    }
    return
  }

  // 第一步中文
  if ($g.tool.isTrue(data?.sentences)) {
    currentState = 'translating'
    // 翻译回显
    setTimeout(() => {
      currentState = 'completed'
      typewriterEffect(data?.sentences[0].toContent, 'en')
    }, 1500) // 模拟1.5秒翻译时间
  } else {
    currentState = 'initial'
  }
}

// 错误处理
function onerror() {
  currentState = 'initial'
  isMultiplePress = false
  zhText = ''
  $g.showToast('录音出错，请重试')
}

/**
 * 开始录音
 */
function handlePressStart(event) {
  if (event && event.cancelable) event.preventDefault()
  isLongPress = false
  longPressTimer = setTimeout(() => {
    isLongPress = true
    handleTouchStart()
  }, 400) // 400ms为长按阈值，可根据体验调整
}
function handleTouchStart() {
  // 只有在翻译中状态才阻止多次按压
  if (currentState === 'translating') {
    isMultiplePress = true
    return
  }

  if (!deviceMicMsg.value) {
    isMultiplePress = false

    if (currentState === 'initial') {
      // 中文录音模式 - 实时识别
      currentState = 'recording'
      zhText = ''
      asrOptions.from = 'zh'
      asrOptions.to = 'en'
      asrOptions.needUpload = false
    } else if (currentState === 'completed') {
      // 英文录音模式 - 录制发送
      isEnglishRecording = true
      asrOptions.from = 'en'
      asrOptions.to = 'zh'
      asrOptions.needUpload = true
    }
    startAliRealTimeRecording()
    window.addEventListener('blur', handleTouchEnd)
  } else {
    $g.showToast(deviceMicMsg.value)
  }
}

/**
 * 结束录音
 */
function handlePressEnd() {
  clearTimeout(longPressTimer)
  if (isLongPress) {
    handleTouchEnd()
  } else {
    $g.showToast('请长按发言')
  }
}

// 触摸结束
function handleTouchEnd() {
  if (isMultiplePress) return

  if (isEnglishRecording) {
    // 英文录音结束
    isEnglishRecording = false
  }
  stopAliRealTimeRecording()

  window.removeEventListener('blur', handleTouchEnd)
}

// 重置所有状态
function resetAll(type = 'delay') {
  zhText = ''
  englishText = ''
  isMultiplePress = false
  isEnglishRecording = false
  if (typewriterTimer) {
    clearTimeout(typewriterTimer)
  }
  if (type == 'delay') {
    setTimeout(() => {
      currentState = 'initial'
    }, 500)
  } else {
    currentState = 'initial'
  }

  ttsPlayer.stopAllPlayback()
  ttsPlayer.clearAll()
  emits('resetDialogType')
}

// 播放音频
function handlePlay() {
  isPlaying = !isPlaying
  if (isPlaying) {
    ttsPlayer.playWithSmartSwitch('task_' + Date.now(), englishText)
  } else {
    ttsPlayer.stopAllPlayback()
    ttsPlayer.clearAll()
  }
}

// 组件卸载时清理
onUnmounted(() => {
  resetAll()
})
</script>

<style scoped>
@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

.animate-pulse {
  animation: blink 1s infinite;
}

.animate-spin {
  animation: spin 2s linear infinite;
}
</style>
