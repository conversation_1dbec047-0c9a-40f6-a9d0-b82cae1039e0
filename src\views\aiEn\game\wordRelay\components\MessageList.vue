<template>
  <div class="conversation relative">
    <div ref="messageListRef" class="h-full flex flex-col overflow-auto no-bar">
      <TransitionGroup :name="!isRecord ? 'slide' : ''" tag="div">
        <div
          v-if="topWhiteBoardHeight > 0 && !isRecord"
          class="w-full transition-height"
          :style="`height:${topWhiteBoardHeight}px;`"
        ></div>
        <div
          v-for="(msg, index) in conversation"
          :id="msg.text"
          :key="index"
          class="message"
          :class="[
            msg.sender == 'user' ? 'pl-[60%]' : 'pr-[60%] flex justify-end',
          ]"
          @click="wordClick(msg, $event)"
        >
          <div :class="['message', msg.sender]" class="w-fit px-10px">
            <span :class="{ 'text-[red]': index }">{{ msg.text[0] }}</span>
            <span>{{ msg.text.slice(1, -1) }}</span>
            <span class="text-[red]">{{ msg.text[msg.text.length - 1] }}</span>
          </div>
        </div>
        <div class="w-full" :style="`height:${bottomWhiteBoardHeight}px`"></div>
      </TransitionGroup>
    </div>
    <div
      v-if="!isRecord"
      class="w-[1px] bg-[#999] h-[100%] absolute top-0 left-[50%] translate-x-[-50%]"
    ></div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  conversation: {
    type: Object,
    default: () => {},
  },
})

let isRecord = $ref(false)
const emit = defineEmits(['showBubble'])
const messageListRef: any = $ref(null)
let topWhiteBoardHeight: any = $ref('')
let bottomWhiteBoardHeight: any = $ref(0)

function wordClick(msg, event) {
  //加生词逻辑，暂不使用
  // emit('showBubble', { item: msg, event })
}

// 滚动到最底部(延迟400ms等待动画执行完成)
function scrollToBottom() {
  setTimeout(() => {
    const lastMessage = document.getElementById(
      props.conversation[props.conversation.length - 1].text,
    )
    if (lastMessage) {
      if (topWhiteBoardHeight > 0) {
        const messageHeight = Math.floor(lastMessage.offsetHeight)
        //滑动结束后，重新计算顶部留白高度,当留白高度小于消息项高度时，留白高度为1（防止留白突然消失带来的视觉突变），当留白高度为1时，留白高度为0
        if (topWhiteBoardHeight - messageHeight <= 0) {
          lastMessage.scrollIntoView({ behavior: 'smooth', block: 'center' })
          setTimeout(() => {
            topWhiteBoardHeight = 0
          }, 200)
        } else {
          topWhiteBoardHeight = topWhiteBoardHeight - messageHeight
        }
      } else {
        lastMessage.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
    }
  }, 400)
}

function getWhiteHeight() {
  //计算前后留白高度，减去25px是消息项的一半
  topWhiteBoardHeight = Math.floor(messageListRef.offsetHeight / 2)
  bottomWhiteBoardHeight = topWhiteBoardHeight
  if (props.conversation.length > 0) {
    const lastMessage: any = document.getElementById(
      props.conversation[props.conversation.length - 1].text,
    )
    topWhiteBoardHeight =
      Math.floor(messageListRef.offsetHeight / 2) -
      Math.floor(lastMessage.offsetHeight) * props.conversation.length
  }
}

$g.bus.on('wordListBottom', scrollToBottom)

onMounted(() => {
  nextTick(() => {
    getWhiteHeight()
  })
})
</script>
<style scoped lang="scss">
.message {
  padding: 5px;
  border-radius: 5px;
}
.user {
  background-color: #e3f2fd;
}
.system {
  background-color: #f5f5f5;
}
/* 动画样式 */
.slide-enter-active {
  animation: slideIn 0.5s ease-in-out;
}

@keyframes slideIn {
  from {
    transform: translateY(700%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.transition-height {
  transition: height 0.3s ease;
}
</style>
