# MCP 智能调用规则

## 🚀 快速参考卡

| 场景          | 工具                | 触发条件           |
| ------------- | ------------------- | ------------------ |
| 任何疑问      | feedback-enhanced   | 立即调用           |
| 复杂问题分析  | sequential-thinking | 多步骤/系统性/设计 |
| Git 操作      | git-mcp-server      | 版本控制相关       |
| 前端 Bug 调试 | browser-tools       | 错误/性能/调试     |
| 网页内容抓取  | fetcher             | 抓取/URL/网页      |
| 文件改动      | feedback-enhanced   | 改动前后必须确认   |
| 查询文档      | Context7            | 技术库/API/教程    |
| 专业知识      | wikipedia           | 百科/历史/科学     |

## ⚡ 核心宗旨 (P0 级规则)

- **feedback-enhanced**: 沟通桥梁，确保每个关键节点都与用户确认
- **sequential-thinking**: 分析引擎，确保复杂问题得到系统性分解
- **git-mcp-server**: 版本控制枢纽，提供完整的 Git 操作能力
- **browser-tools**: 前端诊断专家，自动化复杂 Bug 调试
- **fetcher**: 网页内容抓取专家，高效获取和处理网页数据

## 🔄 决策流程

```
用户请求 → 复杂问题? → sequential-thinking → 前端Bug? → browser-tools
                                        ↓
网页抓取? → fetcher → Git操作? → git-mcp-server → 查文档? → Context7
                                                    ↓
疑问确认? → feedback-enhanced → 文件改动? → 改动前确认 → 执行 → 改动后总结
```

## 🛠️ 工具调用规则 (P0 级)

| 工具                    | 触发关键词                      | 核心功能           | 调用示例                          |
| ----------------------- | ------------------------------- | ------------------ | --------------------------------- |
| **sequential-thinking** | 复杂/分析/设计/方案/架构/系统性 | 系统性分析复杂问题 | "设计用户系统" ✅ / "修复 bug" ❌ |
| **browser-tools**       | bug/错误/调试/性能/前端/控制台  | 前端诊断和性能分析 | "页面报错" ✅ / "语法错误" ❌     |
| **fetcher**             | 抓取/网页/URL/fetch/爬取        | 网页内容抓取处理   | "抓取网页" ✅ / "本地文件" ❌     |
| **git-mcp-server**      | git/提交/分支/版本/仓库         | Git 版本控制操作   | 所有 Git 操作必须使用             |
| **feedback-enhanced**   | 疑问/确认/改动/澄清             | 关键节点确认沟通   | 文件改动前后必须调用              |
| **Context7**            | 文档/API/教程/技术库            | 技术文档查询       | React/Vue/TS 等技术库             |
| **wikipedia**           | 百科/历史/科学/概念             | 专业知识查询       | 非代码相关知识                    |

### 🚫 Git 操作强制规则

- ❌ **禁用**: `git commit` → 必须用 `yarn git:push`
- ✅ **必须**: 执行前通过 feedback-enhanced 确认
- 📝 **必须**: 提交后通过 feedback-enhanced 总结结果
- ⚠️ **危险操作**: reset --hard, clean -f, push --force → 必须详细说明风险

### 📋 feedback-enhanced 必调用场景

| 场景     | 时机       | 目的                      |
| -------- | ---------- | ------------------------- |
| 疑问澄清 | 任何疑问时 | 确保理解准确              |
| 文件改动 | 改动前后   | 确认思路和结果            |
| Bug 调试 | 调试前后   | 确认策略和总结            |
| 危险操作 | 执行前     | 风险警告确认              |
| 任务完成 | 对话结束前 | 最终确认，AI 不能主动结束 |

### 🎯 其他工具规则

- **sequential-thinking**: 3+步骤任务后考虑调用 todo_tool
- **Context7**: 技术库文档查询优先使用
- **wikipedia**: 非代码专业知识查询优先使用
- **remember**: 仅用户明确要求时使用，不自动记忆

## 📊 工具调用优先级

| 优先级 | 工具类型            | 使用场景              |
| ------ | ------------------- | --------------------- |
| **P0** | sequential-thinking | 复杂问题必须先分析    |
| **P0** | browser-tools       | 前端 Bug 调试必须使用 |
| **P0** | fetcher             | 网页内容抓取必须使用  |
| **P0** | feedback-enhanced   | 关键节点必须确认      |
| **P0** | git-mcp-server      | Git 操作必须使用      |
| **P1** | Context7            | 技术文档查询高度推荐  |
| **P1** | wikipedia           | 专业知识查询高度推荐  |

## ⚠️ 避免过度调用

- 用户简短回复("是"、"好"、"可以") → 不再调用 feedback
- 收集信息后避免重复收集
- 新问题/新要求 → 重新调用工具

## ✅ 关键检查清单

### P0 级检查

- [ ] 复杂问题是否先调用 sequential-thinking？
- [ ] 前端 Bug 是否调用 browser-tools？
- [ ] Git 操作是否使用 git-mcp-server？
- [ ] 文件改动前后是否 feedback 确认？
- [ ] 危险操作是否详细说明风险？

### P1 级检查

- [ ] sequential-thinking 后是否考虑 todo_tool？
- [ ] 技术文档是否优先查 Context7？
- [ ] 专业知识是否优先查 wikipedia？

## 💡 核心理念

- **安全第一**: 确认优于假设，质量优于数量
- **工具定位**: sequential-thinking(分析大脑) + feedback(沟通桥梁) + git-mcp-server(操作手臂)
- **用户主导**: 对话结束权交给用户，AI 不能主动结束对话
