# vue-template

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vitejs.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```

## 页面过渡动画

现在可以通过路由的 `meta.animationType` 来控制页面切换时的动画效果:

```typescript
{
  path: 'example',
  name: 'Example',
  component: () => import('@/views/example/index.vue'),
  meta: {
    title: '示例页面',
    // 设置页面过渡动画类型
    animationType: 'slide-right'
  }
}
```

支持的动画类型:

- `slide-right`: 页面从右向左划入
- `slide-left`: 页面从左向右划入
- `fade`: 淡入淡出效果
- `scale`: 缩放效果（默认）

如果不设置 `animationType`，将使用默认的 `scale` 动画。
