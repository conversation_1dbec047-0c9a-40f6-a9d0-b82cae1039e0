<template>
  <div :class="currentTab == 1 ? 'mine-bg' : 'sync-train-bg'">
    <div
      class="w-full"
      :style="{
        height: useSettingStore().navigationHeight + 'px',
      }"
    ></div>
    <div
      class="flex flex-col"
      :style="{
        height: `calc(100vh - ${useSettingStore().navigationHeight}px)`,
      }"
    >
      <div
        class="flex-grow items-stretch min-h-[0px] overflow-auto no-bar px-16px"
      >
        <NbxMain
          v-show="currentTab == 0"
          type="NBX"
          :user-info="userInfo"
        ></NbxMain>
        <Mine v-show="currentTab == 1" type="NBX" :user-info="userInfo"></Mine>
      </div>
      <div
        class="flex-shrink-0 flex justify-between py-12px px-46px border-t-[1px] border-[#14141412] bg-white"
      >
        <div
          v-for="(item, index) in tabList"
          :key="index"
          class="flex flex-col items-center px-20px"
          :class="currentTab == index ? 'text-[#333] font-500' : 'text-[#999]'"
          @click="changeTab(index)"
        >
          <g-icon
            :name="currentTab == index ? item.svg : item.svgGray"
            size="24"
            class="mb-4px"
          />
          <span class="text-11px">{{ item.name }}</span>
        </div>
      </div>
    </div>
    <van-popup
      v-model:show="showPop"
      teleport="body"
      :style="{ background: 'transparent' }"
      class="cursor-pointer"
      :close-on-click-overlay="false"
    >
      <div class="relative px-[19px]" @click="toActivity">
        <img
          src="@/assets/img/home/<USER>"
          class="w-[337px] h-[324px]"
        />
        <g-icon
          name="svg-home-activityClose"
          size="38"
          :style="{ textAlign: 'center', marginTop: '37px' }"
          @click.stop="showPop = false"
        />
      </div>
    </van-popup>
    <van-floating-bubble
      v-model:offset="offset"
      :style="{
        width: '90px',
        height: '87px',
        background: 'transparent',
        overflow: 'inherit',
        borderRadius: 0,
        display: isShowFloat ? 'block' : 'none',
      }"
      axis="xy"
      @click="toActivity"
    >
      <template #default>
        <img
          src="@/assets/img/home/<USER>"
          class="w-[90px] h-[87px] object-cover"
        />
      </template>
    </van-floating-bubble>

    <AiFloatMenu
      v-if="currentTab == 0"
      :userInfo="userInfo"
      :platform="userPlatform"
    />
  </div>
</template>

<script setup lang="ts" name="NbxHome">
import { getActivityConfig } from '@/api/activity'
import { useSettingStore } from '@/stores/modules/setting'
import NbxMain from './components/NbxMain.vue'
import AiFloatMenu from './components/AiFloatMenu.vue'
import { getUserInfo } from '@/api/home'
import Mine from './components/Mine.vue'
import { useUserStore } from '@/stores/modules/user'
let { version, userPlatform } = $(storeToRefs(useUserStore()))
let userInfo: any = $ref({})
let currentTab: any = $ref(0)
let isShowActivity = $ref<any>(false)
let showPop = $ref<any>(false)
let offset = $ref<any>({})
const router = useRouter()
const userStore: any = useUserStore()

const tabList = [
  {
    name: '首页',
    com: NbxMain,
    svg: 'svg-home-nbx-home',
    svgGray: 'svg-home-home-gray',
  },
  {
    name: '我的',
    com: Mine,
    svg: 'svg-home-nbx-mine',
    svgGray: 'svg-home-mine-gray',
  },
]

function changeTab(data) {
  sessionStorage.setItem('TAB', data)
  currentTab = data
}

async function getUserInfoApi() {
  try {
    let res = await getUserInfo()
    userInfo = res
  } catch (e) {
    console.error(e)
  }
}
let isShowFloat = $computed(() => {
  return isShowActivity && currentTab == 0 && showPop == false
})
async function getActivityState() {
  const res = await getActivityConfig()
  showPop = res?.isShowDialog == 2 ? true : false
  isShowActivity = res?.activityState == 2 ? true : false
}
function toActivity() {
  showPop = false
  router.push({ name: 'ActivityIndex', query: { flag: 'nbxHome' } })
}
function onResize() {
  offset =
    window.innerWidth > 412
      ? { x: window.innerWidth - 200, y: window.innerHeight - 250 }
      : { x: window.innerWidth - 120, y: window.innerHeight - 250 }
}

onMounted(async () => {
  window.addEventListener('resize', onResize)
  nextTick(() => {
    offset =
      window.innerWidth > 412
        ? { x: window.innerWidth - 200, y: window.innerHeight - 250 }
        : { x: window.innerWidth - 120, y: window.innerHeight - 250 }
  })
})

onBeforeMount(() => {
  getActivityState()
  // 关掉系统滑动
  if (userStore?.role == 'student') {
    $g.flutter('enableWebPageBack', false)
  }
  getUserInfoApi()
  $g.bus.on('getUserInfoApi', getUserInfoApi)
  currentTab = sessionStorage.getItem('TAB') || 0
})

onBeforeUnmount(() => {
  $g.bus.off('getUserInfoApi', getUserInfoApi)
  window.removeEventListener('resize', onResize)
  //页面销毁时还原系统滑动
  if (userStore?.role == 'student') {
    $g.flutter('enableWebPageBack', true)
  }
})

onDeactivated(() => {
  //页面缓存  退出页面时还原系统滑动
  if (userStore?.role == 'student') {
    $g.flutter('enableWebPageBack', true)
  }
})

onActivated(() => {
  //页面缓存  进入页面时关闭系统滑动
  if (userStore?.role == 'student') {
    $g.flutter('enableWebPageBack', false)
  }
})
</script>

<style lang="scss" scoped>
.sync-train-bg {
  background: linear-gradient(
      180deg,
      rgba(252, 49, 3, 1) 0%,
      rgba(255, 242, 228, 0.32)
    )
    top / 100% 341px no-repeat;
}
.mine-bg {
  background: url(@/assets/img/home/<USER>/ 100% 100vh no-repeat;
}
</style>
