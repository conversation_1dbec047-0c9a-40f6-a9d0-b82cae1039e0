<template>
  <div>
    <div class="message-action-bar flex justify-between">
      <div
        class="flex items-center bg-white h-30px rounded-[12px] px-10px py-7px"
      >
        <!-- 播放 -->
        <div class="action-btn" @click="handlePlay">
          <img
            class="w-16px h-16px"
            :src="
              isPlaying
                ? $g.tool.getFileUrl('aiEn/home/<USER>')
                : $g.tool.getFileUrl('aiEn/home/<USER>')
            "
            alt=""
          />
        </div>

        <!-- AI模式：根据是否最新消息和展开状态显示功能 -->
        <template v-if="mode === 'ai'">
          <!-- 最新消息显示完整功能 -->
          <template v-if="props.isLatest">
            <!-- 分隔线 -->
            <div class="divider"></div>
            <!-- 点赞 -->
            <div
              class="action-btn"
              :class="{ disabled: isDisliked }"
              @click="handleLike"
            >
              <img
                class="w-16px h-16px"
                :class="{ 'grayscale opacity-70': isDisliked }"
                :src="
                  isLiked
                    ? $g.tool.getFileUrl('aiEn/home/<USER>')
                    : $g.tool.getFileUrl('aiEn/home/<USER>')
                "
                alt=""
              />
            </div>
            <!-- 分隔线 -->
            <div class="divider"></div>
            <!-- 踩 -->
            <div
              class="action-btn"
              :class="{ disabled: isDisliked }"
              @click="handleDislike"
            >
              <img
                class="w-16px h-16px"
                :src="
                  isDisliked
                    ? $g.tool.getFileUrl('aiEn/home/<USER>')
                    : $g.tool.getFileUrl('aiEn/home/<USER>')
                "
                alt=""
              />
            </div>
            <!-- 分隔线 -->
            <div class="divider"></div>
            <!-- 翻译 -->
            <div
              class="action-btn"
              :class="{ 'pointer-events-none': isTranslateLoading }"
              @click="handleTranslate"
            >
              <van-loading v-if="isTranslateLoading" size="16px" />
              <img
                v-else
                class="w-16px h-16px"
                :src="
                  props.showTranslation
                    ? $g.tool.getFileUrl('aiEn/home/<USER>')
                    : $g.tool.getFileUrl('aiEn/home/<USER>')
                "
                alt=""
              />
            </div>
          </template>

          <!-- 历史消息：展开动画包装 -->
          <template v-else>
            <!-- 分隔线 -->
            <div class="divider"></div>
            <!-- 历史消息展开的按钮组 -->
            <transition name="slide-in-right">
              <div
                v-if="isCurrentMessageExpanded"
                class="expanded-actions flex"
              >
                <!-- 点赞 -->
                <div
                  class="action-btn"
                  :class="{ disabled: isDisliked }"
                  @click="handleLike"
                >
                  <img
                    class="w-16px h-16px"
                    :class="{ 'grayscale opacity-50': isDisliked }"
                    :src="
                      isLiked
                        ? $g.tool.getFileUrl('aiEn/home/<USER>')
                        : $g.tool.getFileUrl('aiEn/home/<USER>')
                    "
                    alt=""
                  />
                </div>
                <!-- 分隔线 -->
                <div class="divider"></div>
                <!-- 踩 -->
                <div
                  class="action-btn"
                  :class="{ disabled: isDisliked }"
                  @click="handleDislike"
                >
                  <img
                    class="w-16px h-16px"
                    :src="
                      isDisliked
                        ? $g.tool.getFileUrl('aiEn/home/<USER>')
                        : $g.tool.getFileUrl('aiEn/home/<USER>')
                    "
                    alt=""
                  />
                </div>
                <!-- 分隔线 -->
                <div class="divider"></div>
                <!-- 翻译 -->
                <div
                  class="action-btn"
                  :class="{ 'pointer-events-none': isTranslateLoading }"
                  @click="handleTranslate"
                >
                  <van-loading v-if="isTranslateLoading" size="16px" />
                  <img
                    v-else
                    class="w-16px h-16px"
                    :src="
                      props.showTranslation
                        ? $g.tool.getFileUrl('aiEn/home/<USER>')
                        : $g.tool.getFileUrl('aiEn/home/<USER>')
                    "
                    alt=""
                  />
                </div>
              </div>
            </transition>
            <!-- 展开箭头 -->
            <div
              v-if="!isCurrentMessageExpanded"
              class="action-btn"
              @click="handleExpand"
            >
              <img
                class="w-14px h-16px"
                src="@/assets/img/aiEn/home/<USER>"
                alt=""
              />
            </div>
          </template>
        </template>
      </div>
      <!-- 教我回答 - 只在AI模式且最新消息显示 -->
      <div
        v-if="mode === 'ai' && props.isLatest && props.showTeachMe"
        class="flex items-center bg-white h-30px rounded-[12px] text-13px px-10px py-7px text-[#5C5F66] cursor-pointer"
        :class="{ 'pointer-events-none': isTeachMeLoading }"
        @click="handleTeachMe"
      >
        <!-- 加载状态显示spinner，否则显示灯泡图标 -->
        <van-loading v-if="isTeachMeLoading" size="16px" class="mr-4px" />
        <img
          v-else
          class="w-16px h-16px mr-4px"
          src="@/assets/img/aiEn/home/<USER>"
          alt=""
        />
        教我回答
      </div>
    </div>

    <!-- 教我回答卡片 - 只在AI模式且最新消息显示 -->
    <transition name="slide-left" appear>
      <div
        v-if="
          mode === 'ai' &&
          props.isLatest &&
          showTeachMeCard &&
          (props.message?.suggestions?.length || 0) > 0
        "
        class="teach-me-card mt-24px p-12px pb-0 rounded-[12px]"
      >
        <div
          class="teach-me-card-header text-16px font-500 leading-24px mb-13px"
        >
          <div class="teach-me-card-header-title">你可以尝试这么回答</div>
        </div>
        <div class="teach-me-card-content pb-20px">
          <van-radio-group
            v-model="checked"
            shape="dot"
            checked-color="#6583FF"
          >
            <template
              v-for="(item, index) in props.message?.suggestions"
              :key="index"
            >
              <van-radio
                :name="item.scenarioType"
                icon-size="17px"
                class="text-14px"
              >
                [{{ getScenarioTypeText(item.scenarioType) }}]
                {{ item.content }}
              </van-radio>
              <van-divider
                v-if="index < (props.message?.suggestions?.length || 0) - 1"
                :style="{
                  borderColor: 'rgba(133,144,166, 0.4)',
                }"
              />
            </template>
          </van-radio-group>
        </div>
        <div class="flex-1 flex-cc">
          <div
            class="w-135px h-44px text-16px text-theme-primary font-500 flex-cc"
            @click="submitTeachMe"
          >
            去回答
          </div>
        </div>
      </div>
    </transition>

    <!-- 问题反馈弹窗 - 只在AI模式显示 -->
    <van-popup
      v-if="mode === 'ai'"
      v-model:show="showPopup"
      class="h-[414px] px-16px py-18px flex flex-col"
      position="bottom"
      style="background: #f2f4fd"
      round
      teleport="#app"
    >
      <div class="popup-header flex justify-between items-center mb-18px">
        <div class="text-16px font-500">问题反馈</div>
        <g-icon
          name="ri-close-line"
          size="22"
          color="#858EA2"
          @click="showPopup = false"
        />
      </div>
      <!-- 反馈选项 -->
      <div
        class="feedback-options flex flex-wrap gap-x-12px gap-y-12px mb-12px"
      >
        <div
          v-for="item in feedbackType"
          :key="item.chatFeedbackItemId"
          class="feedback-item border border-[transparent] text-14px py-6px px-17px rounded-[8px] cursor-pointer"
          :class="
            selectedFeedbacks.includes(item.chatFeedbackItemId)
              ? 'bg-[#E7E9FD] text-[#6666FF] border-[#6666FF]'
              : 'bg-white text-[#333]'
          "
          @click="toggleFeedback(item.chatFeedbackItemId)"
        >
          {{ item.feedbackItemName }}
        </div>
      </div>
      <van-field
        v-model="feedbackContent"
        class="feedback-content rounded-[8px] flex-1 mb-24px"
        style="min-height: 50px"
        maxlength="30"
        show-word-limit
        rows="4"
        autosize
        type="textarea"
        placeholder="请输入反馈内容"
      />
      <!-- 提交按钮 -->
      <div
        class="submit-btn w-full h-45px text-white text-16px flex justify-center items-center cursor-pointer rounded-[22px]"
        style="background: linear-gradient(45deg, #4a84ff 0%, #8283ff 100%)"
        @click="submitFeedback"
      >
        提交
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import type { IMessage } from '../type'
import {
  getTeachMeAnswer,
  getMessageFeedbackApi,
  submitMessageFeedback,
  getTextTranslate,
} from '@/api/aiEn'

interface IProps {
  /** 消息 */
  message?: IMessage
  /** 是否显示播放按钮 */
  showPlay?: boolean
  /** 是否显示点赞踩按钮 */
  showFeedback?: boolean
  /** 是否显示重新生成按钮 */
  showRegenerate?: boolean
  /** 是否显示教我回答按钮 */
  showTeachMe?: boolean
  /** 是否显示翻译 */
  showTranslation?: boolean
  /** 是否有翻译内容 */
  hasTranslationContent?: boolean
  /** 是否是最新消息 */
  isLatest?: boolean
  /** 模式：AI消息ai、学生消息student */
  mode?: 'ai' | 'student'
  /** 当前展开工具栏的消息ID列表 */
  expandedMessageIds?: number[]
}

const props = withDefaults(defineProps<IProps>(), {
  showPlay: true,
  showFeedback: true,
  showRegenerate: true,
  showTeachMe: true,
  showTranslation: false,
  isLatest: false,
  mode: 'ai',
  expandedMessageIds: () => [],
})

const emit = defineEmits<{
  play: [message?: IMessage]
  like: [message?: IMessage]
  dislike: [message?: IMessage]
  'bar-translate': [message?: IMessage, translateResult?: any]
  teachMe: [message?: IMessage]
  feedback: [message?: IMessage, feedbacks?: number[]]
  'update-message': [message: IMessage]
  'expand-history': [messageId: number | null]
}>()

// 状态管理
const isPlaying = ref(false)
const isLiked = ref(false)
const isDisliked = ref(false)

// 监听消息的播放状态变化
watch(
  () => props.message?.isPlaying,
  (newPlaying) => {
    if (newPlaying !== undefined) {
      isPlaying.value = newPlaying
    }
  },
  { immediate: true },
)
const showPopup = ref(false)
const feedbackContent = ref('')
// 选中的反馈项
const selectedFeedbacks = ref<number[]>([])
// 教我回答卡片显示状态
const showTeachMeCard = ref(false)
// 教我回答选中的答案
const checked = ref(1)
// 历史消息展开状态 - 移除内部状态，改用外部传入
// const isHistoryExpanded = ref(false)
// 教我回答加载状态
const isTeachMeLoading = ref(false)
// 翻译加载状态
const isTranslateLoading = ref(false)
// 移除单独的教我回答数据变量，改用消息对象的 suggestions 字段

// 计算当前消息是否展开
const isCurrentMessageExpanded = computed(() => {
  return (
    props.expandedMessageIds?.includes(props.message?.messageId || 0) || false
  )
})

// 注入conversationId
const currentConversationId = inject<Ref<string | null>>(
  'currentConversationId',
)

// 注入聊天模式
const chatMode = inject<Ref<string>>('chatMode')

// 移除 ITeachMeResponse 接口定义，使用 type.ts 中的 ISuggestion

let feedbackType = $ref<
  Array<{
    chatFeedbackItemId: number
    feedbackItemName: string
    remark?: string
  }>
>([])

// 初始化反馈状态
function initializeFeedbackState() {
  if (props.message?.feedback) {
    const feedback = props.message.feedback
    isLiked.value = feedback.feedbackType === 'GOOD'
    isDisliked.value = feedback.feedbackType === 'BAD'
    feedbackContent.value = feedback.remark
    selectedFeedbacks.value = [...feedback.chatFeedbackItemIdList]
  } else {
    isLiked.value = false
    isDisliked.value = false
    feedbackContent.value = ''
    selectedFeedbacks.value = []
  }
}

// 更新消息反馈信息
function updateMessageFeedback(
  feedbackType: 'GOOD' | 'BAD' | null,
  remark: string = '',
  chatFeedbackItemIdList: number[] = [],
) {
  if (props.message) {
    const updatedMessage: IMessage = {
      ...props.message,
      feedback: {
        feedbackType,
        remark,
        chatFeedbackItemIdList: [...chatFeedbackItemIdList],
      },
    }
    emit('update-message', updatedMessage)
  }
}

// 监听 message 变化，重新初始化状态
watch(
  () => props.message,
  () => {
    initializeFeedbackState()
  },
  { immediate: true },
)

/** 播放/暂停 */
function handlePlay() {
  isPlaying.value = !isPlaying.value
  emit('play', props.message)
}

/** 点赞 */
async function handleLike() {
  // 如果已经踩了，不能点赞
  if (isDisliked.value) {
    return
  }

  try {
    if (isLiked.value) {
      // 取消点赞
      await submitMessageFeedback({
        conversationId: currentConversationId?.value,
        messageId: props.message?.messageId,
        feedbackType: null, // 取消反馈
        remark: '',
        chatFeedbackItemIdList: [],
      })
      isLiked.value = false
      updateMessageFeedback(null, '', [])
    } else {
      // 点赞
      await submitMessageFeedback({
        conversationId: currentConversationId?.value,
        messageId: props.message?.messageId,
        feedbackType: 'GOOD', // 点赞反馈
        remark: '',
        chatFeedbackItemIdList: [],
      })
      isLiked.value = true
      isDisliked.value = false
      updateMessageFeedback('GOOD', '', [])
    }
    emit('like', props.message)
  } catch (error) {
    console.error('点赞操作失败:', error)
    $g.showToast('操作失败，请重试')
  }
}

/** 踩 */
async function handleDislike() {
  // 如果已经踩了，不能再点击
  if (isDisliked.value) {
    return
  }

  try {
    // 将chatMode转换为数字类型的moduleType
    const moduleType = chatMode?.value === 'theme' ? 2 : 1 // 1-自由对话,2-场景对话

    // 调用API获取反馈选项
    const feedbackItems = await getMessageFeedbackApi({
      moduleType, // 使用数字类型的moduleType
    })

    // 设置获取到的反馈选项
    if (feedbackItems && feedbackItems.length > 0) {
      feedbackType = feedbackItems
    }

    // 如果已有反馈信息，回填到表单
    if (
      props.message?.feedback &&
      props.message.feedback.feedbackType === 'BAD'
    ) {
      feedbackContent.value = props.message.feedback.remark
      selectedFeedbacks.value = [
        ...props.message.feedback.chatFeedbackItemIdList,
      ]
    } else {
      // 重置之前选中的反馈
      feedbackContent.value = ''
      selectedFeedbacks.value = []
    }

    showPopup.value = true
  } catch (error) {
    console.error('获取反馈选项失败:', error)
    showPopup.value = true
    selectedFeedbacks.value = []
  }

  emit('dislike', props.message)
}

/** 教我回答 */
async function handleTeachMe() {
  if (showTeachMeCard.value) {
    showTeachMeCard.value = false
    nextTick(() => {
      emit('teachMe', props.message)
    })
    return
  }
  if (props.message?.suggestions?.length) {
    // 每次显示卡片时，重置为默认选中正常回复(scenarioType为1)
    const normalSuggestion = props.message.suggestions.find(
      (item) => item.scenarioType === 1,
    )
    checked.value =
      normalSuggestion?.scenarioType ||
      props.message.suggestions[0]?.scenarioType ||
      1

    showTeachMeCard.value = true
    nextTick(() => {
      emit('teachMe', props.message)
    })
    return
  }
  showTeachMeCard.value = true
  isTeachMeLoading.value = true

  await getTeachMeAnswer({
    conversationId: currentConversationId?.value,
    messageId: props.message?.messageId,
  })
    .then((res) => {
      if (res && res.length && props.message) {
        // 创建更新后的消息对象
        const updatedMessage: IMessage = {
          ...props.message,
          suggestions: res,
        }
        // 通知父组件更新消息
        emit('update-message', updatedMessage)

        // 设置默认选中正常回复(scenarioType为1)
        const normalSuggestion = res.find((item) => item.scenarioType === 1)
        checked.value =
          normalSuggestion?.scenarioType || res[0]?.scenarioType || 1
      }

      // 延迟50ms执行，避免卡片未出来就调用滚动到底部
      setTimeout(() => {
        nextTick(() => {
          emit('teachMe', props.message)
        })
      }, 50)
    })
    .finally(() => {
      isTeachMeLoading.value = false
    })
}

/** 重置播放状态 */
function resetPlayState() {
  isPlaying.value = false
}

/** 重置反馈状态 */
function resetFeedback() {
  isLiked.value = false
  isDisliked.value = false
}

/** 翻译 */
async function handleTranslate() {
  if (isTranslateLoading.value) return

  // 如果已有翻译内容，直接切换显示状态
  if (props.hasTranslationContent) {
    emit('bar-translate', props.message)
    return
  }

  // 没有翻译内容，请求接口获取
  try {
    isTranslateLoading.value = true

    const translateResult = await getTextTranslate({
      conversationId: currentConversationId?.value,
      messageId: props.message?.messageId,
    })

    // 通过emit传递翻译结果给父组件，让父组件处理显示
    emit('bar-translate', props.message, translateResult)
  } catch (error) {
    console.error('翻译失败:', error)
    $g.showToast('翻译失败，请重试')
  } finally {
    isTranslateLoading.value = false
  }
}

/** 切换反馈选项 */
function toggleFeedback(feedbackItemId: number) {
  if (selectedFeedbacks.value.includes(feedbackItemId)) {
    selectedFeedbacks.value = selectedFeedbacks.value.filter(
      (v) => v !== feedbackItemId,
    )
  } else {
    selectedFeedbacks.value.push(feedbackItemId)
  }
}

/** 提交反馈 */
async function submitFeedback() {
  if (selectedFeedbacks.value.length > 0) {
    try {
      // 调用提交反馈的API
      await submitMessageFeedback({
        conversationId: currentConversationId?.value,
        messageId: props.message?.messageId,
        feedbackType: 'BAD', // 点踩反馈
        remark: feedbackContent.value,
        chatFeedbackItemIdList: selectedFeedbacks.value,
      })

      isDisliked.value = true
      isLiked.value = false

      // 更新消息反馈信息
      updateMessageFeedback(
        'BAD',
        feedbackContent.value,
        selectedFeedbacks.value,
      )

      emit('feedback', props.message, selectedFeedbacks.value)
      showPopup.value = false

      $g.showToast('反馈提交成功')
    } catch (error) {
      console.error('提交反馈失败:', error)
      $g.showToast('反馈提交失败，请重试')
    }
  } else {
    // 可以添加提示，要求至少选择一项
    $g.showToast('请选择反馈类型')
  }
}

/** 展开历史消息 */
function handleExpand() {
  emit('expand-history', props.message?.messageId || null)
}

/** 获取场景类型文本 */
function getScenarioTypeText(scenarioType: number): string {
  switch (scenarioType) {
    case 1:
      return '正常'
    case 2:
      return '幽默'
    case 3:
      return '专业'
    default:
      return '正常'
  }
}

function submitTeachMe() {
  let item = props.message?.suggestions?.find(
    (item) => item.scenarioType === checked.value,
  )
  console.log(`⚡[ item ] >`, item)
  showTeachMeCard.value = false
  emit('teachMe', props.message)
  $g.bus.emit('aiEn_teachMe', item)
}

// 暴露方法供父组件调用
defineExpose({
  resetPlayState,
  resetFeedback,
})
</script>

<style scoped lang="scss">
.message-action-bar {
  .action-btn {
    cursor: pointer;

    &.disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  .teach-btn {
  }

  .divider {
    width: 0;
    height: 12px;
    border-left: 1px solid rgba($color: #7a8599, $alpha: 0.2);
    margin: 0 11px;
  }

  .feedback-options {
    .feedback-item {
      height: 36px;
      line-height: 36px;
      border: 1px solid rgba(0, 0, 0, 0.05);
      box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);
    }
  }
}

// 教我回答卡片从左侧滑入动画
.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s ease-out;
}

.slide-left-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-left-enter-to {
  transform: translateX(0);
  opacity: 1;
}

.slide-left-leave-from {
  transform: translateX(0);
  opacity: 1;
}

.slide-left-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

// 历史消息展开按钮简单出现动画
.slide-in-right-enter-active {
  transition: all 0.2s ease;
}

.slide-in-right-enter-from {
  width: 0;
  opacity: 0;
}

.slide-in-right-enter-to {
  width: auto;
  opacity: 1;
}

.expanded-actions {
  align-items: center;
  overflow: hidden;
}

.teach-me-card {
  display: flex;
  flex-direction: column;
  min-height: 270px;
  background: linear-gradient(
    45deg,
    rgba(74, 132, 255, 0.15) 0%,
    rgba(130, 131, 255, 0.15) 100%
  );
  padding-bottom: 0;

  .teach-me-card-content {
    background: #fff;
    border-radius: 10px;
    padding: 17px 12px;
  }
}
</style>

<style lang="scss">
.feedback-content {
  // 小于500px时使用sticky定位
  .van-field__word-limit {
    position: sticky;
    bottom: 1px;
    right: 10px;
    color: #999;
    font-size: 12px;
    pointer-events: none; // 避免遮挡输入框点击
    z-index: 1; // 降低层级
  }

  .van-field__control {
  }

  // 大于500px时改为absolute定位
  @media (min-width: 500px) {
    position: relative;

    .van-field__word-limit {
      position: absolute;
      bottom: 12px;
      right: 16px;
      pointer-events: none; // 避免遮挡输入框点击
      z-index: 1; // 降低层级
    }

    :deep(.van-field__control) {
      padding-bottom: 24px !important;
    }
  }
}

.teach-me-card-content {
  .van-radio__icon {
    border-width: 2px;
  }
  // 覆盖vant radio选中圆形图标的样式
  .van-radio__icon--dot__icon {
    width: 60% !important;
    height: 60% !important;
  }
}
</style>
