import { ref, computed } from 'vue'
import OSS from '@/plugins/AiEnOSS'
import type { ITaskInfo, IStreamSegment } from './useStreamTTSPlayer'

export interface IAudioAssemblerOptions {
  /** 任务完成后的回调，返回音频地址 */
  onAudioReady?: (taskId: string, audioUrl: string) => void
  /** 组装过程出错的回调 */
  onError?: (taskId: string, error: string) => void
  /** 进度回调 */
  onProgress?: (taskId: string, progress: number) => void
  /** 调试日志开关 */
  showDebugLog?: boolean
}

export interface IAssemblerTask {
  /** 任务ID */
  taskId: string
  /** 任务状态 */
  status:
    | 'pending'
    | 'downloading'
    | 'assembling'
    | 'uploading'
    | 'completed'
    | 'failed'
  /** 进度 0-100 */
  progress: number
  /** 最终音频地址 */
  audioUrl?: string
  /** 错误信息 */
  error?: string
  /** 开始时间 */
  startTime: number
  /** 结束时间 */
  endTime?: number
}

export function useAudioAssembler(options: IAudioAssemblerOptions = {}) {
  const { onAudioReady, onError, onProgress, showDebugLog = true } = options

  // OSS实例
  const oss = new OSS()

  // 状态管理
  const assemblerTasks = ref(new Map<string, IAssemblerTask>())

  /** 添加调试日志 */
  function addDebugLog(message: string): void {
    if (import.meta.env.VITE_APP_ENV !== 'production' && showDebugLog) {
      console.log(`🎵 AudioAssembler: ${message}`)
    }
  }

  /** 检查任务是否可以开始组装 */
  function canStartAssemble(taskInfo: ITaskInfo): boolean {
    if (!taskInfo.inputFinished || !taskInfo.totalSegments) {
      return false
    }

    // 检查所有段落是否都有音频或已失败
    const processedSegments = taskInfo.segments.filter(
      (seg) =>
        seg.status === 'completed' || seg.status === 'failed' || seg.audioUrl,
    )

    return processedSegments.length >= taskInfo.totalSegments
  }

  /** 开始音频组装任务 */
  async function startAssembleTask(
    taskId: string,
    taskInfo: ITaskInfo,
  ): Promise<void> {
    if (assemblerTasks.value.has(taskId)) {
      addDebugLog(`任务 ${taskId} 已在组装队列中`)
      return
    }

    if (!canStartAssemble(taskInfo)) {
      addDebugLog(`任务 ${taskId} 条件不满足，跳过组装`)
      return
    }

    // 创建组装任务
    const assemblerTask: IAssemblerTask = {
      taskId,
      status: 'pending',
      progress: 0,
      startTime: Date.now(),
    }

    assemblerTasks.value.set(taskId, assemblerTask)
    addDebugLog(`开始组装任务: ${taskId}`)

    try {
      // 更新状态
      updateAssemblerTask(taskId, { status: 'downloading', progress: 10 })

      // 1. 下载所有音频段落
      const audioSegments = await downloadAudioSegments(
        taskId,
        taskInfo.segments,
      )

      // 更新状态
      updateAssemblerTask(taskId, { status: 'assembling', progress: 40 })

      // 2. 组装音频
      const assembledAudio = await assembleAudioSegments(taskId, audioSegments)

      // 更新状态
      updateAssemblerTask(taskId, { status: 'uploading', progress: 70 })

      // 3. 上传到OSS
      const audioUrl = await uploadAssembledAudio(taskId, assembledAudio)

      // 完成
      updateAssemblerTask(taskId, {
        status: 'completed',
        progress: 100,
        audioUrl,
        endTime: Date.now(),
      })

      addDebugLog(`任务 ${taskId} 组装完成: ${audioUrl}`)
      onAudioReady?.(taskId, audioUrl)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '组装失败'
      updateAssemblerTask(taskId, {
        status: 'failed',
        error: errorMessage,
        endTime: Date.now(),
      })

      addDebugLog(`任务 ${taskId} 组装失败: ${errorMessage}`)
      onError?.(taskId, errorMessage)
    }
  }

  /** 更新组装任务状态 */
  function updateAssemblerTask(
    taskId: string,
    updates: Partial<IAssemblerTask>,
  ): void {
    const task = assemblerTasks.value.get(taskId)
    if (!task) return

    Object.assign(task, updates)
    assemblerTasks.value.set(taskId, task)

    if (updates.progress !== undefined) {
      onProgress?.(taskId, updates.progress)
    }
  }

  /** 下载音频段落 */
  async function downloadAudioSegments(
    taskId: string,
    segments: IStreamSegment[],
  ): Promise<ArrayBuffer[]> {
    addDebugLog(`开始下载 ${segments.length} 个音频段落`)

    const audioBuffers: ArrayBuffer[] = []
    let downloadedCount = 0

    for (const segment of segments.sort((a, b) => a.index - b.index)) {
      if (segment.audioUrl && segment.status !== 'failed') {
        try {
          const response = await fetch(segment.audioUrl)
          if (!response.ok) {
            throw new Error(`下载失败: ${response.status}`)
          }

          const arrayBuffer = await response.arrayBuffer()
          audioBuffers.push(arrayBuffer)

          downloadedCount++
          const progress = 10 + (downloadedCount / segments.length) * 30 // 10-40%
          updateAssemblerTask(taskId, { progress })

          addDebugLog(
            `下载段落 ${segment.index} 完成 (${downloadedCount}/${segments.length})`,
          )
        } catch (error) {
          addDebugLog(`下载段落 ${segment.index} 失败: ${error}`)
          // 失败的段落跳过，不影响其他段落
        }
      } else {
        addDebugLog(`跳过段落 ${segment.index} (无音频或已失败)`)
      }
    }

    if (audioBuffers.length === 0) {
      throw new Error('没有可用的音频段落')
    }

    addDebugLog(`成功下载 ${audioBuffers.length} 个音频段落`)
    return audioBuffers
  }

  /** 组装音频段落 */
  async function assembleAudioSegments(
    taskId: string,
    audioBuffers: ArrayBuffer[],
  ): Promise<Blob> {
    addDebugLog(`开始组装 ${audioBuffers.length} 个音频段落`)

    if (audioBuffers.length === 1) {
      // 只有一个音频段落，直接返回
      return new Blob([audioBuffers[0]], { type: 'audio/wav' })
    }

    // 简单的音频拼接（适用于相同格式的WAV文件）
    const totalSize = audioBuffers.reduce(
      (sum, buffer) => sum + buffer.byteLength,
      0,
    )
    const mergedBuffer = new Uint8Array(totalSize)

    let offset = 0
    audioBuffers.forEach((buffer, index) => {
      const uint8Array = new Uint8Array(buffer)

      if (index === 0) {
        // 第一个文件：保留完整的WAV头
        mergedBuffer.set(uint8Array, offset)
        offset += uint8Array.length
      } else {
        // 后续文件：跳过WAV头（前44字节），只合并音频数据
        const audioData = uint8Array.slice(44)
        mergedBuffer.set(audioData, offset)
        offset += audioData.length
      }

      const progress = 40 + ((index + 1) / audioBuffers.length) * 30 // 40-70%
      updateAssemblerTask(taskId, { progress })
    })

    // 更新第一个文件的WAV头中的文件大小信息
    const dataView = new DataView(mergedBuffer.buffer)
    dataView.setUint32(4, mergedBuffer.length - 8, true) // 文件大小
    dataView.setUint32(40, mergedBuffer.length - 44, true) // 音频数据大小

    addDebugLog(`音频组装完成，总大小: ${mergedBuffer.length} 字节`)
    return new Blob([mergedBuffer], { type: 'audio/wav' })
  }

  /** 上传组装后的音频 */
  async function uploadAssembledAudio(
    taskId: string,
    audioBlob: Blob,
  ): Promise<string> {
    addDebugLog(`开始上传音频，大小: ${audioBlob.size} 字节`)

    const fileName = `assembled_audio_${taskId}_${Date.now()}.wav`

    const result: any = await (oss as any).uploadFile({
      file: audioBlob,
      id: fileName,
      name: fileName,
    })

    const audioUrl = result?.resource_url || result?.fullUrl
    if (!audioUrl) {
      throw new Error('上传成功但未获取到音频地址')
    }

    addDebugLog(`音频上传完成: ${audioUrl}`)
    return audioUrl
  }

  /** 获取任务状态 */
  function getTaskStatus(taskId: string): IAssemblerTask | undefined {
    return assemblerTasks.value.get(taskId)
  }

  /** 清理任务 */
  function clearTask(taskId: string): void {
    assemblerTasks.value.delete(taskId)
    addDebugLog(`清理组装任务: ${taskId}`)
  }

  /** 清理所有任务 */
  function clearAllTasks(): void {
    assemblerTasks.value.clear()
    addDebugLog('清理所有组装任务')
  }

  // 计算属性
  const allTasks = computed(() => Array.from(assemblerTasks.value.values()))
  const activeTasks = computed(() =>
    allTasks.value.filter(
      (task) =>
        task.status === 'downloading' ||
        task.status === 'assembling' ||
        task.status === 'uploading',
    ),
  )
  const completedTasks = computed(() =>
    allTasks.value.filter((task) => task.status === 'completed'),
  )
  const failedTasks = computed(() =>
    allTasks.value.filter((task) => task.status === 'failed'),
  )

  return {
    // 状态
    allTasks,
    activeTasks,
    completedTasks,
    failedTasks,

    // 方法
    canStartAssemble,
    startAssembleTask,
    getTaskStatus,
    clearTask,
    clearAllTasks,

    // 工具方法
    addDebugLog,
  }
}
