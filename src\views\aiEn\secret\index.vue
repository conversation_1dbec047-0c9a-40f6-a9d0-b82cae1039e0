<template>
  <div>
    <g-navbar customTitle="协议管理" customBackGround="transparent"> </g-navbar>
    <g-loading v-if="loading" class="h-200px"></g-loading>
    <div
      v-if="!loading"
      class="px-[12px] py-[16px] overflow-y-auto no-bar"
      :style="{
        height: `calc(100vh - ${useSettingStore().navBarTotalHeight}px - ${useSettingStore().navigationHeight}px)`,
      }"
    >
      <div
        v-for="(item, index) in list"
        :key="index"
        class="flex bg-[#FFF] mb-[16px] br-[12px] p-[17px] items-center justify-between"
        @click="onDetail(item)"
      >
        <div class="flex items-center">
          <span
            class="text-[15px] flex-1 max-w-[200px] line-1 text-[#141619] ml-[12px]"
            >{{ item?.title }}</span
          >
        </div>
        <img src="@/assets/img/aiEn/mine/right.png" class="w-[18px] h-[18px]" />
      </div>
    </div>
    <g-empty v-if="!loading && !list?.length"></g-empty>
  </div>
</template>

<script setup lang="ts">
import { getSecretListApi } from '@/api/aiEn'
import { useSettingStore } from '@/stores/modules/setting'
let list = $ref<any>([])
let loading = $ref<any>(false)
const router = useRouter()
async function getSecretList() {
  try {
    loading = true
    const res = await getSecretListApi({ applyId: 2 })
    loading = false
    list = res || []
  } catch (err) {}
}
onMounted(() => {
  getSecretList()
})
function onDetail(data) {
  router.push({
    name: 'SecretDetail',
    query: { content: data.content, title: data.title },
  })
}
</script>

<style lang="scss" scoped></style>
