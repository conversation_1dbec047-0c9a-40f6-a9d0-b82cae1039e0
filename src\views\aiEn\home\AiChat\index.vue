<template>
  <div class="h-full">
    <!-- 主内容区域 -->
    <div
      class="overflow-auto"
      :class="{
        'h-[calc(100vh-273px)]': showAnimation,
        'h-[calc(100vh-122px)]': !showAnimation,
      }"
    >
      <MessageList
        ref="messageListRef"
        :messages="messages"
        :auto-scroll-to-bottom="true"
        @update-message="handleUpdateMessage"
        @play="handleMessagePlay"
      />
    </div>

    <!-- 输入框 -->
    <div class="h-68px p-12px">
      <AudioBar
        :is-ai-connecting="isAiConnecting"
        :message-id="nextMessageId"
        @send-audio="handleSendAudio"
        @start-learn="handleStartLearn"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import AudioBar from './components/AudioSendBar/index.vue'
import MessageList from './components/MessageList.vue'
import { EMessageType, EMessageStatus, EChatMode } from './type'
import type {
  IMessage,
  IApiMessage,
  IConversationDetail,
  IThemeChatParams,
  IError,
} from './type'
import { AIService, type IReadyMessageData } from '@/utils/aiService'
import {
  getFreeChatList,
  createFreeChat,
  getFreeChatDetail,
  createThemeChat,
  savePcmWav,
} from '@/api/aiEn'
import { useWelcome } from './hooks/useWelcome'
import { useStreamTTSPlayer } from './hooks/useStreamTTSPlayer'
import { useLocalAudio } from './hooks/useLocalAudio'
import { useAiSettingStore } from '@/stores/modules/aiEnSetting'

// 常量定义
const MESSAGES = {
  AI_REPLY_FAILED: '抱歉，回复失败了，请稍后重试',
  NETWORK_ERROR: '发送失败，请检查网络连接',
  TRANSLATION_PLACEHOLDER: '翻译功能待实现',
} as const

let smoothOutputConfig = $ref<'slow' | 'normal' | 'fast' | false>('fast')
const aiService = new AIService({
  url: import.meta.env.VITE_APP_BASE_API5 + '/aienglish/user/chat/conversation',
  smoothOutput: smoothOutputConfig, // 启用快速匀速输出（60字符/秒）
  // smoothOutput: false,
})
const emits = defineEmits(['startLearn'])

// Store
const aiSettingStore = useAiSettingStore()

// 调试日志总开关
const showDebugLog = false
// TTS播放器配置和初始化
const streamTTSPlayer = useStreamTTSPlayer({
  showDebugLog,
  onTaskStart: (taskId) => {
    addDebugLog(`🎬 TTS任务开始: ${taskId}`)
    updateMessageTTSState(taskId, { isPlaying: false, hasAudioError: false })
    // 任务开始不等于开始播放，不修改全局状态
  },
  onTaskComplete: (taskId, summary) => {
    addDebugLog(`🏁 TTS任务完成: ${taskId}`, summary)
    updateMessageTTSState(taskId, {
      isPlaying: false,
      isAudioCompleted: true,
    })
    // 整个任务完成时，延迟检查是否还有其他正在播放的任务
    setTimeout(() => {
      const hasOtherPlayingTasks = messages.some(
        (msg) => msg.messageId.toString() !== taskId && msg.isPlaying,
      )
      if (!hasOtherPlayingTasks) {
        aiSettingStore.isVoicePlaying = false
      }
    }, 100) // 延迟100ms检查，避免段落切换时的瞬时false
  },
  onTaskPlayStateChange: (taskId, playing) => {
    addDebugLog(`🔄 任务 ${taskId} 播放状态: ${playing}`)
    updateMessageTTSState(taskId, { isPlaying: playing })

    if (playing) {
      // TTS开始播放时，停止本地音频播放
      if (localAudio.currentPlayingMessageId.value) {
        addDebugLog(
          `🛑 TTS播放开始，停止本地音频: ${localAudio.currentPlayingMessageId.value}`,
        )
        localAudio.stopCurrentAudio()
      }
      aiSettingStore.isVoicePlaying = true
    } else {
      // 播放暂停，延迟检查是否真正结束（避免段落切换时误判）
      setTimeout(() => {
        const hasOtherPlayingTasks = messages.some(
          (msg) => msg.messageId.toString() !== taskId && msg.isPlaying,
        )
        if (!hasOtherPlayingTasks) {
          aiSettingStore.isVoicePlaying = false
        }
      }, 200) // 延迟200ms检查，给段落切换留时间
    }
  },
  onSegmentReady: (segment, index, taskId) => {
    addDebugLog(`📄 段落就绪: ${index} - ${segment.substring(0, 20)}...`)
    const message = findMessageByTaskId(taskId)
    if (message) {
      updateMessageSegments(message)

      // 获取第一个段落的音频URL作为展示
      const segments = streamTTSPlayer.segments.value
      const firstSegment = segments.find(
        (seg) => seg.taskId === taskId && seg.audioUrl,
      )
      if (firstSegment && firstSegment.audioUrl && !message.audioUrl) {
        const messageId = parseInt(taskId)
        updateMessageById(messageId, (msg) => {
          msg.audioUrl = firstSegment.audioUrl
        })
      }
    }
  },
  onConvertComplete: (index, success, taskId) => {
    addDebugLog(`🔄 段落 ${index} 转换完成: ${success}`)
    const message = findMessageByTaskId(taskId)
    if (message) {
      if (!success) {
        updateMessageTTSState(taskId, { hasAudioError: true })
      }
      updateMessageSegments(message)
    }
  },
  // 音频组装配置
  audioAssembler: {
    onAudioReady: async (taskId, audioUrl) => {
      addDebugLog(`🎵 任务 ${taskId} 组装音频完成: ${audioUrl}`)
      console.log(`🎵 完整音频地址: ${audioUrl}`)

      const messageId = parseInt(taskId)
      const message = messages.find((msg) => msg.messageId === messageId)

      if (message) {
        const audioItem = {
          convertType: 1, // 1-文本转语音
          content: message.content,
          audio: audioUrl,
        }

        // 先更新本地消息的音频列表（无论API成功失败都要更新）
        updateMessageById(messageId, (message) => {
          message.audioList = [audioItem]
        })
        addDebugLog(`🎵 任务 ${taskId} 本地音频列表已更新`)

        // 尝试保存到后端
        if (currentConversationId) {
          try {
            await savePcmWav({
              conversationId: currentConversationId,
              messageId: messageId,
              audioList: [audioItem],
            })
            addDebugLog(`🎵 任务 ${taskId} 音频保存成功`)
          } catch (error) {
            addDebugLog(`❌ 任务 ${taskId} 音频保存失败: ${error}`)
            console.error('音频保存失败:', error)
            // 注意：即使保存失败，本地audioList已经更新，下次可以直接使用
          }
        } else {
          addDebugLog(`⚠️ 任务 ${taskId} 无对话ID，跳过后端保存`)
        }
      } else {
        addDebugLog(`❌ 任务 ${taskId} 未找到对应消息`)
      }
    },
    onError: (taskId, error) => {
      addDebugLog(`❌ 任务 ${taskId} 音频组装失败: ${error}`)
      console.error(`音频组装失败:`, error)
    },
    onProgress: (taskId, progress) => {
      addDebugLog(`📊 任务 ${taskId} 组装进度: ${progress}%`)
    },
  },
})

// 流式输出任务实例 (已移除，直接使用基础TTS任务)

// 类型定义
interface IProps {
  /** 聊天模式 */
  mode?: EChatMode
  /** 主题对话创建参数（主题对话模式下需要） */
  themeParams?: IThemeChatParams
  /** 动画展开状态 */
  showAnimation?: boolean
}

// Props 定义
let props = withDefaults(defineProps<IProps>(), {
  mode: EChatMode.FREE,
  showAnimation: false,
})

// 使用欢迎语hooks
const welcome = useWelcome()

// 本地音频播放hooks
const localAudio = useLocalAudio({
  showDebugLog,
  onPlayStateChange: (messageId, isPlaying) => {
    addDebugLog(`🎵 本地音频播放状态变化: ${messageId} - ${isPlaying}`)
    updateMessageTTSState(messageId, { isPlaying })
    if (isPlaying) {
      // 停止其他TTS播放
      streamTTSPlayer.stopAllPlayback()
      aiSettingStore.isVoicePlaying = true
    } else {
      aiSettingStore.isVoicePlaying = false
    }
  },
  onPlayComplete: (messageId) => {
    addDebugLog(`🎵 本地音频播放完成: ${messageId}`)
    updateMessageTTSState(messageId, {
      isPlaying: false,
      isAudioCompleted: true,
    })
  },
  onPlayError: (messageId, error) => {
    addDebugLog(`❌ 本地音频播放错误: ${messageId}`)
    updateMessageTTSState(messageId, {
      isPlaying: false,
      hasAudioError: true,
    })
  },
  onDebugLog: addDebugLog,
})

// 响应式数据 - 统一使用 $ref
let messages = $ref<IMessage[]>([])
let messageListRef = $ref<InstanceType<typeof MessageList>>()
/** 当前对话ID */
let currentConversationId = $ref<string | null>(null)

// 使用 provide 传递 mode 和 conversationId，方便子组件使用
provide('chatMode', props.mode)
provide('currentConversationId', $$(currentConversationId))
/** AI是否正在连接中 */
let isAiConnecting = $ref(false)
/** 本地消息ID计数器 - 用于生成顺序ID */
let nextMessageId = $ref(1)
/** 匀速输出配置 - 用户可控制 */

// 工具函数
/** 获取下一个消息ID */
function getNextMessageId(): number {
  return nextMessageId++
}

/** 添加调试日志 */
function addDebugLog(message: string, data?: any): void {
  if (import.meta.env.VITE_APP_ENV !== 'production' && showDebugLog) {
    const timestamp = new Date().toLocaleTimeString()
    const logMessage = `[${timestamp}] 🔊 ${message}`
    console.log(logMessage, data || '')
  }
}

/** 通过taskId查找消息 */
function findMessageByTaskId(taskId: string): IMessage | undefined {
  const messageId = parseInt(taskId)
  return messages.find((msg) => msg.messageId === messageId)
}

/** 更新消息的TTS状态 */
function updateMessageTTSState(
  taskId: string,
  state: Partial<
    Pick<
      IMessage,
      'isPlaying' | 'hasAudioError' | 'isAudioCompleted' | 'audioUrl'
    >
  >,
): void {
  const messageId = parseInt(taskId)
  updateMessageById(messageId, (message) => {
    Object.assign(message, state)
  })
}

/** 更新消息的音频段落信息 */
function updateMessageSegments(message: IMessage): void {
  const taskId = message.messageId.toString()
  const segments = streamTTSPlayer.segments.value.filter(
    (seg) => seg.taskId === taskId,
  )

  // 转换为IMessage接口中定义的IAudioSegment格式
  const audioSegments = segments.map((seg) => ({
    index: seg.index,
    text: seg.text,
    status: seg.status,
    audioUrl: seg.audioUrl,
    audioSize: seg.audioSize,
  }))

  updateMessageById(message.messageId, (msg) => {
    msg.audioSegments = audioSegments
  })
}

/** 通过引用更新消息的通用函数 */
function updateMessageByReference(
  messageRef: IMessage,
  updateFn: (message: IMessage) => void,
): boolean {
  const messageIndex = messages.findIndex((msg) => msg === messageRef)
  if (messageIndex !== -1) {
    updateFn(messages[messageIndex])

    return true
  }

  return false
}

/** 更新消息状态的通用函数 */
function updateMessageById(
  messageId: number,
  updateFn: (message: IMessage) => void,
): boolean {
  const messageIndex = messages.findIndex((msg) => msg.messageId === messageId)
  if (messageIndex !== -1) {
    updateFn(messages[messageIndex])
    return true
  }
  console.warn(
    `⚠️ 未找到messageId为 ${messageId} 的消息，当前消息列表:`,
    messages.map((msg) => ({
      messageId: msg.messageId,
      messageType: msg.messageType,
      content: msg.content.substring(0, 20) + '...',
    })),
  )
  return false
}

/** 设置消息状态 */
function setMessageStatus(
  messageId: number,
  status: EMessageStatus,
  error?: string,
): void {
  updateMessageById(messageId, (message) => {
    message.status = status
    if (error) {
      message.error = error
      message.canRetry = true
    }
  })
}

/** 处理消息更新事件 */
function handleUpdateMessage(updatedMessage: IMessage): void {
  updateMessageById(updatedMessage.messageId, (message) => {
    // 更新消息的所有属性
    Object.assign(message, updatedMessage)
  })
}

/** 处理消息播放事件 */
function handleMessagePlay(message?: IMessage): void {
  if (!message) {
    console.warn('播放消息为空')
    return
  }

  const taskId = message.messageId.toString()
  const hasLocalAudio = message.audioList && message.audioList.length > 0

  addDebugLog(`🎵 处理播放消息: ${taskId}，有本地音频: ${hasLocalAudio}`)

  // 检查当前消息的播放状态
  const isCurrentlyPlayingLocal = localAudio.isMessagePlaying(taskId)
  const isCurrentlyPlayingTTS = message.isPlaying && !isCurrentlyPlayingLocal

  // 如果该消息正在播放（无论是本地音频还是TTS），则停止
  if (isCurrentlyPlayingLocal || isCurrentlyPlayingTTS) {
    addDebugLog(`🛑 停止当前播放的消息: ${taskId}`)

    if (isCurrentlyPlayingLocal) {
      localAudio.stopCurrentAudio()
    } else {
      streamTTSPlayer.cancelTask(taskId)
      updateMessageTTSState(taskId, { isPlaying: false })
    }
    return // 停止后直接返回
  }

  // 开始播放新音频前，先停止所有其他播放
  addDebugLog(`🛑 开始新播放前，停止所有其他播放`)

  // 彻底取消所有TTS任务（不仅停止播放，还要停止转换）
  messages.forEach((msg) => {
    if (msg.isPlaying && msg.messageId.toString() !== taskId) {
      const otherTaskId = msg.messageId.toString()
      addDebugLog(`🛑 取消其他TTS任务: ${otherTaskId}`)
      streamTTSPlayer.cancelTask(otherTaskId)
      updateMessageTTSState(otherTaskId, { isPlaying: false })
    }
  })

  // 停止本地音频播放
  if (localAudio.currentPlayingMessageId.value) {
    localAudio.stopCurrentAudio()
  }

  // 根据音频资源类型选择播放方式
  if (hasLocalAudio) {
    // 使用本地音频播放
    const audioItem = message.audioList![0]
    addDebugLog(`🎵 使用本地音频播放: ${audioItem.audio}`)
    localAudio.playLocalAudio(message, audioItem.audio)
  } else {
    // 重置消息的音频状态
    updateMessageTTSState(taskId, {
      hasAudioError: false,
      isAudioCompleted: false,
      audioUrl: undefined,
    })

    // 使用TTS播放
    addDebugLog(`🎵 使用TTS播放: ${message.content.substring(0, 20)}...`)
    streamTTSPlayer.playWithSmartSwitch(taskId, message.content)
  }
}

/** 处理欢迎语逻辑 */
async function handleWelcomeMessage(
  conversationId: string,
  hasHistoryMessages: boolean = false,
): Promise<void> {
  await welcome.handleWelcomeMessage(conversationId, hasHistoryMessages, {
    mode: props.mode,
    // 检查是否启用自动播放
    autoPlay: aiSettingStore.config.voiceAutoPlayStatus,
    displayStream: (data, autoPlay = false) =>
      welcome
        .displayWelcomeMessageWithStream(data, {
          messages,
          createLocalMessageFromApi,
          updateMessageById,
          scrollToBottom: () => messageListRef?.scrollToBottom(),
          streamTTSPlayer,
          autoPlay, // 传递autoPlay参数给流式显示函数
        })
        .then(() => {
          // 欢迎消息添加后，同步nextMessageId计数器
          if (messages.length > 0) {
            const maxId = Math.max(...messages.map((msg) => msg.messageId))
            if (maxId >= nextMessageId) {
              nextMessageId = maxId + 1
            }
          }

          // 注意：TTS播放已经在displayWelcomeMessageWithStream中处理，
          // 这里不再需要重复调用handleWelcomeMessageTTS
          if (autoPlay) {
            addDebugLog(`🎉 欢迎消息TTS已在流式显示中启动`)
          }
        }),
  })
}

/** 消息工厂函数 - 统一消息创建逻辑 */
function createMessage(params: {
  messageType: EMessageType
  content: string
  status?: EMessageStatus
  messageId?: number
  parentId?: number
  audioList?: Array<{
    convertType: number
    content: string
    audio: string
  }>
}): IMessage {
  const now = new Date()
  return {
    messageId: params.messageId || getNextMessageId(),
    parentId: params.parentId,
    messageType: params.messageType,
    content: params.content,
    createTime: now.toISOString(),
    timestamp: now.getTime(),
    status: params.status || EMessageStatus.SUCCESS,
    translation: '',
    showTranslation: false,
    audioList: params.audioList,
  }
}

/** 从API消息创建本地消息 */
function createLocalMessageFromApi(apiMsg: IApiMessage): IMessage {
  return {
    ...apiMsg,
    messageId: apiMsg.messageId, // 使用服务器返回的messageId
    parentId: apiMsg.parentId, // 保持原始的parentId
    timestamp: new Date(apiMsg.createTime).getTime(),
    status: EMessageStatus.SUCCESS,
    translation: '',
    showTranslation: false,
  }
}

// 处理音频发送
function handleSendAudio(audioData): void {
  const audioList = [
    {
      convertType: 2, // 2-语音识别
      content: audioData.content,
      audio: audioData.audioUrl || audioData.audio || '',
    },
  ]
  if (audioData) {
    sendTextMessage(audioData.content, audioList)
  }
}

/** 处理开始学习事件 - 重置主题对话 */
async function handleStartLearn(): Promise<void> {
  // 只针对主题对话模式进行重置，自由对话不做任何处理
  if (props.mode !== EChatMode.THEME) {
    return
  }

  try {
    // 清空当前消息列表
    clearMessages()

    // 重新创建主题对话
    if (props.themeParams) {
      const createResult = await createThemeChat(props.themeParams)
      if (createResult) {
        currentConversationId = createResult
        // 重置后的新对话，显示首次欢迎语
        await handleWelcomeMessage(createResult, false)
      }
    } else {
      console.error('主题对话参数不存在，无法重置')
    }
  } catch (error) {
    console.error('重置主题对话失败:', error)
  }
}

// 清空消息
function clearMessages(): void {
  messages = []
  // 重置消息ID计数器
  nextMessageId = 1
}

// 添加消息到列表 - 使用消息工厂简化
function addMessage(params: {
  messageType: EMessageType
  content: string
  status?: EMessageStatus
  parentId?: number
  audioList?: Array<{
    convertType: number
    content: string
    audio: string
  }>
}): IMessage {
  const newMessage = createMessage(params)
  messages.push(newMessage)

  // 删除手动滚动调用，由 useMessageListScroll 自动处理

  return newMessage
}

/** 获取最后一条消息的ID */
function getLastMessageId(): number | undefined {
  return messages.length > 0
    ? messages[messages.length - 1].messageId
    : undefined
}

/** 处理流式AI响应 */
async function handleStreamAiResponse(
  userText: string,
  aiMessage: IMessage,
): Promise<void> {
  if (!currentConversationId) {
    console.error('没有对话ID，无法发送消息')
    setMessageStatus(
      aiMessage.messageId,
      EMessageStatus.ERROR,
      MESSAGES.NETWORK_ERROR,
    )
    return
  }

  let fullContent = ''
  let hasStartedTTS = false // 标记是否已启动TTS

  try {
    await aiService.streamChat(
      {
        conversationId: currentConversationId,
        content: userText,
      },
      {
        onReady: (readyData: IReadyMessageData) => {
          // 更新用户消息的messageId
          const userMessage = messages.findLast(
            (msg) => msg.messageType === EMessageType.USER,
          )
          if (userMessage) {
            updateMessageByReference(userMessage, (message) => {
              message.messageId = readyData.requestMessageId
            })
          }

          // 更新AI消息的messageId
          updateMessageByReference(aiMessage, (message) => {
            message.messageId = readyData.responseMessageId
          })
        },
        onMessage: (content: string) => {
          fullContent += content

          // 使用updateMessageById确保响应式更新
          updateMessageById(aiMessage.messageId, (message) => {
            message.content = fullContent
            message.timestamp = Date.now()
          })

          // 在收到第一块内容时启动流式TTS（如果启用自动播放）
          if (
            !hasStartedTTS &&
            fullContent.length > 10 &&
            aiSettingStore.config.voiceAutoPlayStatus
          ) {
            hasStartedTTS = true
            addDebugLog(`🎤 首次内容到达，启动流式TTS`)

            // 使用消息ID作为任务ID，直接使用基础的TTS任务而非流式任务
            const taskId = aiMessage.messageId.toString()
            streamTTSPlayer.startTask(taskId)
            streamTTSPlayer.addTextToTask(taskId, fullContent)
          } else if (hasStartedTTS) {
            // 继续添加新内容到TTS任务
            const taskId = aiMessage.messageId.toString()
            streamTTSPlayer.addTextToTask(taskId, content)
          }
        },
        onComplete: () => {
          // 完成TTS任务
          if (hasStartedTTS) {
            addDebugLog(`🏁 AI回复完成，结束TTS`)
            const taskId = aiMessage.messageId.toString()
            streamTTSPlayer.finishTask(taskId)
          }

          // 使用updateMessageById确保响应式更新
          updateMessageById(aiMessage.messageId, (message) => {
            message.status = EMessageStatus.SUCCESS
            message.translation = MESSAGES.TRANSLATION_PLACEHOLDER
          })
        },
        onError: (error: IError) => {
          console.error('AI回复失败:', error)

          // 取消TTS任务
          if (hasStartedTTS) {
            addDebugLog(`❌ AI回复出错，取消TTS`)
            const taskId = aiMessage.messageId.toString()
            streamTTSPlayer.cancelTask(taskId)
          }

          setMessageStatus(
            aiMessage.messageId,
            EMessageStatus.ERROR,
            MESSAGES.AI_REPLY_FAILED,
          )
        },
      },
    )
  } catch (error) {
    console.error('发送消息失败:', error)

    // 取消TTS任务
    if (hasStartedTTS) {
      addDebugLog(`❌ 发送消息失败，取消TTS`)
      const taskId = aiMessage.messageId.toString()
      streamTTSPlayer.cancelTask(taskId)
    }

    const errorMessage =
      error instanceof Error ? error.message : MESSAGES.NETWORK_ERROR
    setMessageStatus(aiMessage.messageId, EMessageStatus.ERROR, errorMessage)
  }
}

// 发送文本消息 - 重构后的版本
async function sendTextMessage(
  text: string,
  audioList?: Array<{
    convertType: number
    content: string
    audio: string
  }>,
): Promise<void> {
  // 设置AI连接状态
  isAiConnecting = true

  try {
    // 获取当前最后一条消息的ID作为用户消息的parentId
    const lastMessageId = getLastMessageId()

    // 添加用户消息
    const userMessage = addMessage({
      messageType: EMessageType.USER,
      content: text,
      status: EMessageStatus.SUCCESS,
      parentId: lastMessageId,
      audioList: audioList,
    })

    // 添加AI回复的占位消息，parentId为刚添加的用户消息ID
    const aiMessage = addMessage({
      messageType: EMessageType.ASSISTANT,
      content: '',
      status: EMessageStatus.GENERATING,
      parentId: userMessage.messageId,
    })

    // 删除手动滚动调用，由 useMessageListScroll 自动处理

    // 处理流式AI响应
    await handleStreamAiResponse(text, aiMessage)
  } finally {
    // 确保连接状态被重置
    isAiConnecting = false
  }
}

/** 初始化自由对话 */
async function initializeFreeChat(): Promise<void> {
  const listResult = await getFreeChatList()

  // 判断列表是否为空
  if (!listResult || listResult.length === 0) {
    // 创建新的自由对话
    const createResult = await createFreeChat()
    if (createResult) {
      currentConversationId = createResult
      // 新创建的对话，显示首次欢迎语
      await handleWelcomeMessage(createResult, false)
    }
    return
  }

  // 永远使用最新的对话（第一个）
  const latestConversation = listResult[0]
  currentConversationId = latestConversation.conversationId

  // 获取对话详情
  const detailResult = await getFreeChatDetail({
    conversationId: currentConversationId,
  })

  // 先加载历史消息
  loadHistoryFromDetail(detailResult)

  // 根据历史消息数量判断欢迎语类型并调用欢迎语接口
  const hasHistoryMessages =
    detailResult?.messages && detailResult.messages.length > 0
  if (currentConversationId) {
    await handleWelcomeMessage(currentConversationId, hasHistoryMessages)
  }
}

/** 初始化主题对话 */
async function initializeThemeChat(): Promise<void> {
  if (!props.themeParams) {
    console.error('主题对话参数不存在')
    return
  }

  const createResult = await createThemeChat(props.themeParams)
  if (createResult) {
    currentConversationId = createResult
    // 新创建的主题对话，显示首次欢迎语
    await handleWelcomeMessage(createResult, false)
  }
}

/** 从对话详情加载历史消息 */
function loadHistoryFromDetail(conversationData: IConversationDetail): void {
  const messagesData = conversationData?.messages

  if (!messagesData || messagesData.length === 0) {
    return
  }

  // 批量处理，减少响应式更新
  messages = messagesData
    .filter((apiMsg) => apiMsg.messageType !== EMessageType.SYSTEM)
    .map((apiMsg) => createLocalMessageFromApi(apiMsg))

  // 重新设置nextMessageId为当前最大ID + 1，确保后续消息ID连续
  if (messages.length > 0) {
    const maxId = Math.max(...messages.map((msg) => msg.messageId))
    nextMessageId = maxId + 1
  }

  // 删除手动滚动调用，由 useMessageListScroll 自动处理消息数组变化
}

// 加载历史消息 - 重构后的版本
async function loadHistoryMessages(): Promise<void> {
  try {
    if (props.mode === EChatMode.FREE) {
      await initializeFreeChat()
    } else {
      await initializeThemeChat()
    }
  } catch (error) {
    console.error('加载历史消息失败:', error)
  }
}

// 初始化
onMounted(() => {
  loadHistoryMessages()

  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

// keep-alive 组件激活时触发
onActivated(() => {
  addDebugLog('📱 组件激活')
})

// keep-alive 组件失活时触发 - 停止所有音频播放
onDeactivated(() => {
  addDebugLog('📱 组件失活，停止所有音频播放')
  stopAllAudioPlayback()
})

/** 停止所有音频播放的通用方法 */
function stopAllAudioPlayback(): void {
  addDebugLog('🛑 执行停止所有音频播放')

  // 停止所有TTS播放
  streamTTSPlayer.stopAllPlayback()
  streamTTSPlayer.clearAll() // 添加清理所有任务

  // 停止本地音频播放
  if (localAudio.currentPlayingMessageId.value) {
    localAudio.stopCurrentAudio()
  }

  // 更新全局播放状态
  aiSettingStore.isVoicePlaying = false

  // 清理所有消息的播放状态
  messages.forEach((msg) => {
    if (msg.isPlaying) {
      updateMessageTTSState(msg.messageId.toString(), { isPlaying: false })
    }
  })
}

/** 处理页面可见性变化 */
function handleVisibilityChange(): void {
  if (document.hidden) {
    addDebugLog('📱 页面隐藏，停止所有音频播放')
    stopAllAudioPlayback()
  } else {
    addDebugLog('📱 页面可见')
  }
}

// 组件卸载时清理资源
onUnmounted(() => {
  addDebugLog('🧹 组件卸载，清理资源')

  // 移除页面可见性监听
  document.removeEventListener('visibilitychange', handleVisibilityChange)

  // 清理所有TTS任务
  streamTTSPlayer.clearAll()

  // 清理本地音频资源
  localAudio.cleanup()
})

// 暴露方法和状态供父组件调用
defineExpose({
  sendTextMessage,
  clearMessages,
  loadHistoryMessages,
  isAiConnecting: $$(isAiConnecting),
  smoothOutputConfig: $$(smoothOutputConfig),
  // TTS相关方法
  handleMessagePlay,
  streamTTSPlayer,
  // TTS状态
  get isTTSPlaying() {
    return streamTTSPlayer.isPlaying.value
  },
})
</script>

<style scoped></style>
