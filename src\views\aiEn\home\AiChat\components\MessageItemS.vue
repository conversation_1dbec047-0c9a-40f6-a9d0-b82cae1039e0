<template>
  <div>
    <div class="message-item-s">
      <div class="message-item-s-content">
        <div>{{ message?.content }}</div>
        <!-- 评估展示区域 -->
        <div
          v-for="item in visibleAssessments"
          :key="item.className"
          :class="`${item.className} flex items-center justify-between text-13px ${
            item.isLoading
              ? 'cursor-not-allowed opacity-80'
              : item.isRetry &&
                  item.type === 'spoken' &&
                  item.text.includes('需要音频')
                ? 'cursor-not-allowed opacity-60'
                : 'cursor-pointer'
          }`"
          @click="() => handleAssessmentJump(item.type)"
        >
          <!-- 左侧：感叹号图标 + 文案 -->
          <div class="flex items-center">
            <!-- 加载状态 -->
            <template v-if="item.isLoading">
              <van-loading
                type="circular"
                size="16px"
                color="#f2f5ff"
                class="mr-6px"
              />
              <span>{{ item.loadingText }}</span>
            </template>
            <!-- 检测结果 -->
            <template v-else>
              <img
                :src="item.image"
                class="w-16px h-16px mr-6px"
                :alt="item.alt"
              />
              <span>{{ item.text }}</span>
            </template>
          </div>
          <!-- 右侧：箭头图标 -->
          <g-icon
            v-if="!item.isLoading"
            name="ri-arrow-right-s-line"
            size="14"
            color="#f2f5ff"
          />
        </div>
        <!-- 润色 -->
        <div
          class="runse flex items-center cursor-pointer"
          @click="handleImprove"
        >
          <van-loading
            v-if="message?.isImproving"
            type="circular"
            size="16px"
            color="#f2f5ff"
            class="mr-6px"
          />
          <img
            v-else
            class="w-16px h-16px mr-6px"
            src="@/assets/img/aiEn/home/<USER>"
            alt=""
          />
          {{
            message?.isImproving
              ? '润色中，请耐心等待...'
              : '点击润色，让表达更地道'
          }}
        </div>
        <!-- 润色结果展示区域 -->
        <div
          v-if="message?.improve && message?.showImproveResult"
          class="timeline-container mt-10px"
        >
          <div class="timeline-item">
            <div class="timeline-icon active">
              <g-icon name="ri-star-fill" size="12" color="#FFFFFF" />
            </div>
            <div class="timeline-content">
              <div class="timeline-title">地道表达</div>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-icon">
              <g-icon name="ri-star-line" size="12" color="#FFFFFF" />
            </div>
            <div class="timeline-content">
              <div class="timeline-title">
                这样说会更好: {{ message?.improve?.improveContent || '无' }}
              </div>
            </div>
          </div>
          <div v-if="message?.improve?.improveReason" class="timeline-item">
            <div class="timeline-icon">
              <g-icon name="ri-star-line" size="12" color="#FFFFFF" />
            </div>
            <div class="timeline-content">
              <div class="timeline-title">
                优化原因: {{ message?.improve?.improveReason || '无' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 学生消息操作工具栏 - 只有播放功能 -->
    <div class="mt-8px">
      <MessageActionBar :message="message" mode="student" @play="handlePlay" />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { IMessage } from '../type'
import MessageActionBar from './MessageActionBar.vue'
import { getImproveInfo, getGrammarTestApi, getSpeakTestApi } from '@/api/aiEn'

interface IProps {
  message?: IMessage
}

const props = withDefaults(defineProps<IProps>(), {})

// 注入对话ID
const currentConversationId = inject<Ref<string | null>>(
  'currentConversationId',
)

// 路由实例
const router = useRouter()

const emit = defineEmits<{
  play: [message?: IMessage]
  updateMessage: [message: IMessage]
  closeOtherImproveResults: [currentMessageId: number]
}>()

// 添加一个静态变量跟踪当前展开的消息ID
let currentImproveMessageId: number | null = null

/** 统一评级配置对象 */
const assessmentConfig = {
  grammar: {
    loadingText: '正在进行语法评估',
    1: { image: 'aiEn/home/<USER>', text: '语法运用娴熟！' }, // 完美
    2: { image: 'aiEn/home/<USER>', text: '语法运用娴熟！' }, // 很好
    3: {
      image: 'aiEn/home/<USER>',
      text: '语法基础达标，细节可精进！',
    }, // 待提高
    4: {
      image: 'aiEn/home/<USER>',
      text: '语法体系需系统巩固！',
    }, // 比较不好
  },
  spoken: {
    loadingText: '正在进行口语评估',
    1: { image: 'aiEn/home/<USER>', text: '发音堪称标准！' }, // 完美
    2: { image: 'aiEn/home/<USER>', text: '发音堪称标准！' }, // 很好
    3: {
      image: 'aiEn/home/<USER>',
      text: '发音清晰但有优化空间！',
    }, // 待提高
    4: {
      image: 'aiEn/home/<USER>',
      text: '发音问题较突出！',
    }, // 比较不好
  },
} as const

/** 评估类型配置 */
const assessmentTypes = [
  {
    type: 'grammar',
    assessField: 'grammarAssess',
    loadingField: 'isGrammarTesting',
    className: 'yfpg',
  },
  {
    type: 'spoken',
    assessField: 'spokenAssess',
    loadingField: 'isSpeakTesting',
    className: 'fypg',
  },
] as const

/** 当前需要显示的评估项 */
const visibleAssessments = computed(() => {
  const message = props.message
  if (!message) return []

  return assessmentTypes.map(({ type, loadingField, className }) => {
    const isLoading = message[loadingField]
    const assessData =
      message[type === 'grammar' ? 'grammarAssess' : 'spokenAssess']
    const hasFailedField = type === 'grammar' ? 'grammarFailed' : 'spokenFailed'
    const hasFailed = message[hasFailedField]

    // 判断是否有音频 - 仅用于口语测评
    const hasAudioFile = hasAudio(message)

    // 如果是加载中
    if (isLoading) {
      return {
        type,
        className,
        isLoading: true,
        loadingText: assessmentConfig[type].loadingText,
        score: undefined,
        image: '',
        text: '',
        alt: type === 'grammar' ? '语法检测' : '口语测评',
      }
    }

    // 如果有检测结果
    if (assessData) {
      return {
        type,
        className,
        isLoading: false,
        loadingText: assessmentConfig[type].loadingText,
        score: assessData.score,
        image: $g.tool.getFileUrl(
          assessmentConfig[type][assessData.assessLevel]?.image ||
            assessmentConfig[type][1].image,
        ),
        text: `${assessData.score}分，${assessmentConfig[type][assessData.assessLevel]?.text || assessmentConfig[type][1].text}`,
        alt: type === 'grammar' ? '语法检测' : '口语测评',
      }
    }

    // 如果接口失败，显示点击重试
    if (hasFailed) {
      return {
        type,
        className,
        isLoading: false,
        loadingText: assessmentConfig[type].loadingText,
        score: undefined,
        image: $g.tool.getFileUrl('aiEn/home/<USER>'),
        text:
          type === 'grammar'
            ? '点击重新进行语法检测'
            : hasAudioFile
              ? '点击重新进行口语测评'
              : '需要音频才能进行口语测评',
        alt: type === 'grammar' ? '语法检测' : '口语测评',
        isRetry: true, // 标记为重试状态
      }
    }

    // 默认loading占位状态（初始状态）
    return {
      type,
      className,
      isLoading: true,
      loadingText: assessmentConfig[type].loadingText,
      score: undefined,
      image: '',
      text: '',
      alt: type === 'grammar' ? '语法检测' : '口语测评',
      isInitialLoading: true, // 标记为初始loading状态
    }
  })
})

/** 播放处理 */
function handlePlay(message?: IMessage) {
  if (message) {
    emit('play', message)
  }
}

/** 处理润色点击 */
async function handleImprove() {
  if (!props.message || !currentConversationId?.value) return

  // 如果已有润色数据，则切换显示状态
  if (props.message.improve) {
    toggleImproveResult()
    return
  }

  // 通知父组件关闭其他展开的润色消息
  emit('closeOtherImproveResults', props.message.messageId)

  // 记录当前展开的消息ID
  currentImproveMessageId = props.message.messageId

  // 设置加载状态
  const updatedMessage = {
    ...props.message,
    isImproving: true,
  }
  emit('updateMessage', updatedMessage)

  try {
    const response = await getImproveInfo({
      conversationId: currentConversationId.value,
      messageId: props.message.messageId,
    })

    if (response) {
      // 更新消息数据
      const finalMessage = {
        ...props.message,
        isImproving: false,
        improve: {
          improveContent: response.improveContent,
          improveReason: response.improveReason,
        },
        showImproveResult: true,
      }
      emit('updateMessage', finalMessage)
    } else {
      throw new Error('润色失败')
    }
  } catch (error) {
    console.error('润色失败:', error)
    $g.showToast('润色失败，请重试')

    // 重置加载状态
    const resetMessage = {
      ...props.message,
      isImproving: false,
    }
    emit('updateMessage', resetMessage)

    // 重置当前展开的消息ID（如果当前消息是展开的消息）
    if (currentImproveMessageId === props.message.messageId) {
      currentImproveMessageId = null
    }
  }
}

/** 切换润色结果显示 */
function toggleImproveResult() {
  if (!props.message || !props.message.improve) return

  // 是否当前正在显示结果
  const isCurrentlyShowing = props.message.showImproveResult

  // 如果当前正在显示，则关闭并重置记录
  if (isCurrentlyShowing) {
    const updatedMessage = {
      ...props.message,
      showImproveResult: false,
    }
    emit('updateMessage', updatedMessage)

    // 清除当前展开的消息ID
    if (currentImproveMessageId === props.message.messageId) {
      currentImproveMessageId = null
    }
    return
  }

  // 如果当前没有显示，需要先通知父组件关闭其他消息的润色结果
  emit('closeOtherImproveResults', props.message.messageId)

  // 记录当前展开的消息ID
  currentImproveMessageId = props.message.messageId

  // 打开当前消息的润色结果
  const updatedMessage = {
    ...props.message,
    showImproveResult: true,
  }
  emit('updateMessage', updatedMessage)
}

/** 通用测评处理函数 */
async function handleAssessment(type: 'grammar' | 'spoken') {
  if (!props.message || !currentConversationId?.value) return

  const assessField = type === 'grammar' ? 'grammarAssess' : 'spokenAssess'
  const loadingField =
    type === 'grammar' ? 'isGrammarTesting' : 'isSpeakTesting'
  const hasFailedField = type === 'grammar' ? 'grammarFailed' : 'spokenFailed'

  // 避免重复检测 - 如果已有检测结果则返回
  if ($g.tool.isTrue(props.message[assessField])) {
    return
  }

  // 设置加载状态，清除失败状态
  const loadingMessage = {
    ...props.message,
    [loadingField]: true,
    [hasFailedField]: false, // 清除失败状态
  }
  emit('updateMessage', loadingMessage)

  try {
    // 根据类型调用不同的API
    const apiCall = type === 'grammar' ? getGrammarTestApi : getSpeakTestApi
    const response = await apiCall({
      conversationId: currentConversationId.value,
      messageId: props.message.messageId,
    })

    // 检查响应数据的完整性
    if (
      response &&
      typeof response.score === 'number' &&
      response.assessLevel &&
      response.assessLevelName
    ) {
      // 更新消息数据 - 直接使用API返回的数据
      const finalMessage = {
        ...props.message,
        [loadingField]: false,
        [hasFailedField]: false, // 确保清除失败状态
        [assessField]: {
          score: response.score,
          assessLevel: response.assessLevel,
          assessLevelName: response.assessLevelName,
        },
      }

      emit('updateMessage', finalMessage)
    } else {
      throw new Error(
        `${type === 'grammar' ? '语法检测' : '口语测评'}响应数据不完整`,
      )
    }
  } catch (error) {
    console.error(`${type === 'grammar' ? '语法检测' : '口语测评'}失败:`, error)

    // 根据错误类型显示不同提示
    let errorMessage = `${type === 'grammar' ? '语法检测' : '口语测评'}失败，请重试`
    if (error instanceof Error) {
      if (error.name === 'TypeError' || error.message.includes('网络')) {
        errorMessage = '网络连接异常，请检查网络后重试'
      } else if (error.message.includes('超时')) {
        errorMessage = '请求超时，请重试'
      }
    }

    $g.showToast(errorMessage)

    // 更新失败状态
    const updatedMessage = {
      ...props.message,
      [loadingField]: false,
      [hasFailedField]: true, // 设置失败状态
    }
    emit('updateMessage', updatedMessage)
  }
}

/** 测评结果跳转处理 */
function handleAssessmentJump(type: 'grammar' | 'spoken') {
  const loadingField =
    type === 'grammar' ? 'isGrammarTesting' : 'isSpeakTesting'
  const assessField = type === 'grammar' ? 'grammarAssess' : 'spokenAssess'
  const hasFailedField = type === 'grammar' ? 'grammarFailed' : 'spokenFailed'

  // 当前评估项加载状态时不允许跳转
  if (props.message?.[loadingField]) {
    return
  }

  // 如果有失败状态，则尝试重新检测
  if (props.message?.[hasFailedField]) {
    handleAssessment(type)
    return
  }

  // 如果没有检测结果，说明是占位状态，触发检测
  if (!props.message?.[assessField]) {
    // 口语测评需要检查是否有音频
    if (type === 'spoken' && !hasAudio(props.message!)) {
      $g.showToast('需要音频才能进行口语测评')
      return
    }

    // 触发检测
    handleAssessment(type)
    return
  }

  // 有检测结果时跳转到反馈页面
  if (props.message && currentConversationId?.value) {
    router.push({
      name: 'AiEnFeedback',
      query: {
        conversationId: currentConversationId.value,
        messageId: props.message.messageId,
        type, // 传递评估类型
      },
    })
  }
}

/** 判断消息是否包含音频 */
function hasAudio(message: IMessage): boolean {
  return !!message.audioList?.length
}

/** 自动触发测评 */
function autoTriggerAssessment(message: IMessage, type: 'grammar' | 'spoken') {
  const assessField = type === 'grammar' ? 'grammarAssess' : 'spokenAssess'
  const loadingField =
    type === 'grammar' ? 'isGrammarTesting' : 'isSpeakTesting'
  const hasFailedField = type === 'grammar' ? 'grammarFailed' : 'spokenFailed'

  // 口语测评需要音频文件
  if (type === 'spoken' && !hasAudio(message)) {
    return
  }

  // 只有在没有检测结果、没有加载状态、没有失败状态时才自动触发
  if (
    !message[assessField] &&
    !message[loadingField] &&
    !message[hasFailedField]
  ) {
    // 立即设置加载状态，清除失败状态
    const updatedMessage = {
      ...message,
      [loadingField]: true,
      [hasFailedField]: false, // 清除失败状态
    }
    emit('updateMessage', updatedMessage)

    // 延迟后请求，确保消息在服务端已保存
    setTimeout(() => {
      handleAssessment(type)
    }, 500)
  }
}

// 监听消息变化，自动触发语法检测和口语测评
watch(
  () => props.message,
  (newMessage, oldMessage) => {
    // 只对新的学生消息进行检测
    if (newMessage && newMessage !== oldMessage) {
      setTimeout(() => {
        // 语法检测 - 对所有学生消息进行
        autoTriggerAssessment(newMessage, 'grammar')
        // 口语测评 - 对所有学生消息进行
        setTimeout(() => {
          autoTriggerAssessment(newMessage, 'spoken')
        }, 500)
      }, 500)
    }
  },
  { immediate: true },
)
</script>

<style scoped lang="scss">
.message-item-s {
  font-weight: 500;
  font-size: 16px;
  color: #141619;
  line-height: 22px;
  background: #fff;
  background: linear-gradient(45deg, #4a84ff 0%, #8283ff 100%);
  border-radius: 12px 4px 12px 12px;
  min-height: 40px;
  padding: 12px;
  color: #fff;
  transition: all 0.3s ease;

  .yfpg {
    height: 36px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px 10px 10px 10px;
    margin-top: 12px;
    font-size: 13px;
    line-height: 36px;
    padding: 0 10px;
    color: #f2f5ff;
  }
  .fypg {
    height: 36px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px 10px 10px 10px;
    margin-top: 12px;
    font-size: 13px;
    line-height: 36px;
    padding: 0 10px;
    color: #f2f5ff;
  }
  .runse {
    border-radius: 10px 10px 10px 10px;
    margin-top: 12px;
    font-size: 13px;
    line-height: 20px;
    padding: 0 10px;
    color: #f2f5ff;
  }

  // 时间线样式
  .timeline-container {
    position: relative;
    padding-left: 24px;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 9px;
      left: 10px;
      width: 1px;
      height: calc(100% - 32px);
      background-image: repeating-linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.3),
        rgba(255, 255, 255, 0.3) 4px,
        transparent 4px,
        transparent 8px
      );
    }

    .timeline-item {
      position: relative;

      &:last-child {
        margin-bottom: 0;
      }

      .timeline-icon {
        position: absolute;
        left: -20px;
        top: 3px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1;
      }

      .timeline-content {
        padding: 6px 0;

        .timeline-title {
          font-size: 13px;
          line-height: 20px;
          color: #f2f5ff;
        }
      }
    }
  }
}
</style>
