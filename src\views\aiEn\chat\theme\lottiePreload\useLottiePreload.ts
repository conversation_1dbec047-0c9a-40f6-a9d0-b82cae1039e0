import { ref, computed } from 'vue'
import {
  preloadLottieAnimation,
  getCachedLottieAnimation,
  clearExpiredLottieCache,
  clearAllLottieCache,
} from './preloadLottie'

// 动画加载状态接口
interface AnimationStatus {
  url: string
  loading: boolean
  cached: boolean
  error: string | null
  data: any | null
}

export function useLottiePreload(urls: string[]) {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const isCached = ref(false)

  // 每个动画的详细状态
  const animationStatuses = ref<Map<string, AnimationStatus>>(new Map())

  // 初始化状态
  const initStatuses = () => {
    urls.forEach((url) => {
      animationStatuses.value.set(url, {
        url,
        loading: false,
        cached: false,
        error: null,
        data: null,
      })
    })
  }

  // 初始化
  initStatuses()

  // 检查缓存状态
  const checkCache = async () => {
    try {
      const promises = urls.map(async (url) => {
        const cached = await getCachedLottieAnimation(url)
        const status = animationStatuses.value.get(url)!
        status.cached = cached !== null
        status.data = cached
        return cached
      })

      const results = await Promise.all(promises)
      const hasCache = results.some((result) => result !== null)
      isCached.value = hasCache
      return hasCache
    } catch (err) {
      console.error('检查缓存失败:', err)
      return false
    }
  }

  // 预加载动画
  const preload = async () => {
    loading.value = true
    error.value = null

    try {
      // 先检查缓存
      await checkCache()

      // 找出需要加载的动画
      const urlsToLoad = urls.filter((url) => {
        const status = animationStatuses.value.get(url)!
        return !status.cached
      })

      if (urlsToLoad.length === 0) {
        console.log('所有动画已缓存，无需重新加载')
        loading.value = false
        return true
      }

      // 开始预加载未缓存的动画
      console.log(`开始预加载 ${urlsToLoad.length} 个动画...`)

      const promises = urlsToLoad.map(async (url) => {
        const status = animationStatuses.value.get(url)!
        status.loading = true
        status.error = null

        try {
          const data = await preloadLottieAnimation(url)
          status.data = data
          status.cached = true
          status.loading = false
          return data
        } catch (err) {
          status.error = err instanceof Error ? err.message : '加载失败'
          status.loading = false
          throw err
        }
      })

      await Promise.all(promises)

      console.log('动画预加载完成')
      isCached.value = true
      loading.value = false
      return true
    } catch (err) {
      console.error('预加载失败:', err)
      error.value = err instanceof Error ? err.message : '预加载失败'
      loading.value = false
      return false
    }
  }

  // 获取单个动画数据（优先从缓存）
  const getAnimationData = async (url: string) => {
    try {
      const status = animationStatuses.value.get(url)
      if (!status) {
        console.error(`未找到动画状态: ${url}`)
        return null
      }

      // 如果已有数据，直接返回
      if (status.data) {
        return status.data
      }

      // 尝试从缓存获取
      const cached = await getCachedLottieAnimation(url)
      if (cached) {
        status.data = cached
        status.cached = true
        return cached
      }

      // 如果没有缓存，尝试预加载并直接返回数据
      console.log(`缓存不存在，开始预加载: ${url}`)
      status.loading = true
      const animationData = await preloadLottieAnimation(url)
      status.data = animationData
      status.cached = true
      status.loading = false
      console.log('预加载完成，直接返回数据')
      return animationData
    } catch (err) {
      console.error('获取动画数据失败:', err)
      const status = animationStatuses.value.get(url)
      if (status) {
        status.error = err instanceof Error ? err.message : '获取失败'
        status.loading = false
      }
      return null
    }
  }

  // 批量获取多个动画数据
  const getMultipleAnimationData = async (urlsToGet: string[]) => {
    try {
      const promises = urlsToGet.map((url) => getAnimationData(url))
      const results = await Promise.all(promises)

      // 返回 URL 和数据的映射
      const dataMap = new Map<string, any>()
      urlsToGet.forEach((url, index) => {
        dataMap.set(url, results[index])
      })

      return dataMap
    } catch (err) {
      console.error('批量获取动画数据失败:', err)
      return new Map()
    }
  }

  // 获取所有动画数据
  const getAllAnimationData = async () => {
    return await getMultipleAnimationData(urls)
  }

  // 获取加载进度
  const getLoadingProgress = computed(() => {
    const total = urls.length
    const loaded = Array.from(animationStatuses.value.values()).filter(
      (status) => status.cached,
    ).length
    return {
      total,
      loaded,
      progress: total > 0 ? (loaded / total) * 100 : 0,
    }
  })

  // 获取错误信息
  const getErrors = computed(() => {
    const errors: Array<{ url: string; error: string }> = []
    animationStatuses.value.forEach((status, url) => {
      if (status.error) {
        errors.push({ url, error: status.error })
      }
    })
    return errors
  })

  // 重试加载失败的动画
  const retryFailedAnimations = async () => {
    const failedUrls = Array.from(animationStatuses.value.values())
      .filter((status) => status.error)
      .map((status) => status.url)

    if (failedUrls.length === 0) {
      console.log('没有失败的动画需要重试')
      return true
    }

    console.log(`重试 ${failedUrls.length} 个失败的动画`)
    return await getMultipleAnimationData(failedUrls)
  }

  return {
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    isCached: computed(() => isCached.value),
    animationStatuses: computed(() => animationStatuses.value),
    loadingProgress: getLoadingProgress,
    errors: getErrors,
    preload,
    checkCache,
    getAnimationData,
    getMultipleAnimationData,
    getAllAnimationData,
    retryFailedAnimations,
    clearExpiredLottieCache,
    clearAllLottieCache,
  }
}
