<template>
  <div class="animation-container" :class="{ 'slide-up': !showAnimation }">
    <!-- 三个半圆光圈背景 -->
    <div class="halo halo-1"></div>
    <div class="halo halo-2"></div>
    <div class="halo halo-3"></div>

    <g-lottie
      v-if="lottieOptions.animationData"
      :key="animationKey"
      :options="lottieOptions"
      class="lottie-animation"
      @anim-created="animCreated"
    >
    </g-lottie>
    <div class="blur-transition"></div>
  </div>
</template>
<script setup lang="ts">
import { useLottiePreload } from '@/views/aiEn/chat/theme/lottiePreload/useLottiePreload'
import { useAiSettingStore } from '@/stores/modules/aiEnSetting'

const props = defineProps<{
  showAnimation: boolean
}>()
const aiSettingStore = useAiSettingStore()

// 动态动画数组
const lottieUrls = [
  'https://qm-cloud.oss-cn-chengdu.aliyuncs.com/aiEn/otherType/aiTeacher_dynamic.json',
  'https://qm-cloud.oss-cn-chengdu.aliyuncs.com/aiEn/otherType/aiTeacher_dynamic2.json',
]

// 静态动画数组
const staticAnimationUrls = [
  'https://qm-cloud.oss-cn-chengdu.aliyuncs.com/aiEn/otherType/aiTeacher_static1.json',
]

// 合并所有URL用于预加载
const allUrls = [...lottieUrls, ...staticAnimationUrls]

// 使用组合式函数
const { getAnimationData, loadingProgress, animationStatuses } =
  useLottiePreload(allUrls)

const animRef = ref<any>(null)
const animationLoading = ref(true)
const animationKey = ref(0) // 用于强制重新渲染
const currentAnimationType = ref<'dynamic' | 'static'>('dynamic') // 当前动画类型

// 响应式的 lottie 选项
const lottieOptions = ref({
  animationData: null, // 使用预加载的数据而不是 path
  loop: true,
  renderer: 'svg',
  autoplay: true,
  speed: 20,
})

// 随机获取数组中的元素
const getRandomElement = <T,>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)]
}

// 获取随机动态动画
const getRandomDynamicAnimation = async () => {
  const randomUrl = getRandomElement(lottieUrls)
  const animationData = await getAnimationData(randomUrl)
  return { url: randomUrl, data: animationData }
}

// 获取随机静态动画
const getRandomStaticAnimation = async () => {
  const randomUrl = getRandomElement(staticAnimationUrls)
  const animationData = await getAnimationData(randomUrl)
  return { url: randomUrl, data: animationData }
}

// 页面加载时获取动画数据
onMounted(async () => {
  try {
    // 初始化时随机获取一个动态动画
    const { data: animationData } = await getRandomDynamicAnimation()
    if (animationData) {
      // 更新 lottie 选项，使用预加载的数据
      lottieOptions.value.animationData = animationData
      currentAnimationType.value = 'dynamic'
      animationLoading.value = false
    } else {
      console.error('无法获取动画数据')
      animationLoading.value = false
    }
  } catch (error) {
    console.error('获取动画数据失败:', error)
    animationLoading.value = false
  }
})

// 重新初始化当前动画
const reinitializeAnimation = (urlIndex: number) => {
  if (animRef.value) {
    try {
      if (currentAnimationType.value === 'static') {
        // 静态动画：只暂停，不销毁，保持实例
        animRef.value.pause()
        animRef.value.goToAndStop(0, true)
      } else {
        // 动态动画：暂停后销毁
        animRef.value.pause()
        animRef.value.destroy()
        animRef.value = null
      }
    } catch (error) {
      console.log('处理动画实例时出错:', error)
      animRef.value = null
    }
  }

  // 强制重新渲染
  animationKey.value = urlIndex
}

function animCreated(anim) {
  anim.setSpeed(1)
  animRef.value = anim
}

// 暂停动画时切换到随机静态动画
async function pauseAnimation() {
  // 切换到随机静态动画
  const { data: animationData } = await getRandomStaticAnimation()
  if (animationData) {
    // 先更新动画数据，再重新初始化
    lottieOptions.value.animationData = animationData
    currentAnimationType.value = 'static'
    reinitializeAnimation(100) // 使用特殊key值表示静态动画
  }
}

// 播放动画时切换到随机动态动画
async function playAnimation() {
  // 切换到随机动态动画
  const { data: animationData } = await getRandomDynamicAnimation()
  if (animationData) {
    // 先更新动画数据，再重新初始化
    lottieOptions.value.animationData = animationData
    currentAnimationType.value = 'dynamic'
    reinitializeAnimation(200) // 使用特殊key值表示动态动画
  }
}
watch(
  () => aiSettingStore.isVoicePlaying,
  (val) => {
    if (!val) {
      pauseAnimation()
    } else {
      playAnimation()
    }
  },
  {
    immediate: true,
  },
)

defineExpose({
  pauseAnimation,
  playAnimation,
})
</script>
<style lang="scss" scoped>
.animation-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 200px; // 设置最小高度
  transition:
    opacity 0.6s ease-in-out,
    min-height 0.6s ease-in-out;

  // 淡出隐藏
  &.slide-up {
    opacity: 0;
    min-height: 0;
  }

  // 半圆光圈基础样式
  .halo {
    position: absolute;
    border-radius: 50%;
    clip-path: polygon(0 0, 100% 0, 100% 50%, 0 50%);
    border: 2px solid #ebf0ff;
    background: transparent;
    left: 50%;
    transform: translateX(-50%);
  }

  // 第一个半圆（最大）- 贴到顶部
  .halo-1 {
    width: 100vw;
    height: 100vw;
    max-width: 400px;
    max-height: 400px;
    top: -44px;
    border-color: #ebf0ff;
  }

  // 第二个半圆（中等）- 同心圆，半径减少50px
  .halo-2 {
    width: calc(100vw - 100px);
    height: calc(100vw - 100px);
    max-width: 300px;
    max-height: 300px;
    top: 0px;
    border-color: #ebf0ff;
  }

  // 第三个半圆（最小）- 同心圆，半径再减少50px
  .halo-3 {
    width: calc(100vw - 200px);
    height: calc(100vw - 200px);
    max-width: 200px;
    max-height: 200px;
    top: 44px;
    border-color: #ebf0ff;
  }

  // lottie 动画样式
  .lottie-animation {
    position: absolute;
    top: -44px;
  }

  .blur-transition {
    position: absolute;
    bottom: 35px;
    width: 100%;
    height: 20px;
    pointer-events: none;
    background: linear-gradient(
      to bottom,
      RGBA(235, 241, 251, 1) 0%,
      RGBA(235, 241, 251, 1) 100%
    );
    filter: blur(5px);
  }
}
</style>
