import { useUserStore } from '@/stores/modules/user'
import axios from 'axios'
import handleData from './handleData'
import loading from './loading'
const CancelToken = axios.CancelToken
const currentRequestList = []
let cancelArr = []

/**
 * @description axios初始化
 */
const server = axios.create({
  timeout: 30 * 1000,
  headers: {
    // 配后端数据的接收方式application/json;charset=UTF-8 或 application/x-www-form-urlencoded;charset=UTF-8
    'Content-Type': 'application/json;charset=UTF-8',
  },
  showTip: true, // 显示提示信息
  showLoading: false, // 默认不显示loading弹窗
  returnAll: false, // false-只返回data数据 true-返回全部数据(包括code,msg...)
  replace: false, // 是否替换旧的请求，相同url新请求替换旧请求,用于tab快速切换重复请求
  delay: true, // 默认给每个接口添加200ms延迟，在某些情况可能有会影响，可以关闭
  isLogin: true, //是否需要验证登录code码
})

/**
 * @description axios请求拦截器
 */
server.interceptors.request.use(
  async (config) => {
    let { token, jztToken, version, platform } = $(storeToRefs(useUserStore()))
    if (config.showLoading) {
      const timer = setTimeout(() => {
        loading.open()
      }, 100)
      config.loadTimer = timer
    }

    if ($g.tool.paramObj(location.hash)?.token) {
      token = $g.tool.paramObj(location.hash).token
    }
    // 版本号
    if ($g.tool.paramObj(location.hash)?.version) {
      version = $g.tool.paramObj(location.hash).version
    }
    config.headers['token'] = token
    config.headers['version'] = version || '1.2.2'
    config.headers['platform'] = config.platform || platform

    // 检查是否为需要token的API
    const apiUrls = [
      import.meta.env.VITE_APP_BASE_API4,
      import.meta.env.VITE_APP_BASE_API5,
    ]
    if (apiUrls.some((apiUrl) => config.url.includes(apiUrl))) {
      config.headers.token = jztToken
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

/**
 * @description axios响应拦截器
 */
server.interceptors.response.use(
  (response) => {
    // 删除取消请求
    let index = cancelArr.findIndex((item) => {
      return item.key == response?.config?.url
    })
    cancelArr.splice(index, 1)
    deleteCurrentRequest(response?.config)
    if (response.config.showLoading) {
      clearTimeout(response.config.loadTimer)

      setTimeout(() => {
        loading.close()
      }, 200)
    }
    return handleData(response)
  },
  (error) => {
    const { response, config } = error
    if (axios.isCancel(error)) return Promise.reject('取消请求')
    return Promise.reject(error)
  },
)

/**
 * @description axios请求兼容 争对业务处理 取消重复请求
 */
function request(config) {
  config.method = config.method || 'get'
  if (config.method.toLowerCase() === 'get') {
    /* 删除分页接口多余参数 */
    if ($g.tool.typeOf(config.data) == 'object') {
      delete config.data.total
    }

    config.params = config.data
    delete config.data
  }
  const params = getAxiosParams(config)

  const item = {
    url: config.url,
    params,
  }

  currentRequestList.push(item)
  return server({
    ...config,
    cancelToken: new CancelToken(function executor(c) {
      // 如果replace为true，取消cancelArr重复请求，只保留最后一次请求
      if (config.replace) {
        // 取消请求 并删除item
        cancelArr = cancelArr.filter((item) => {
          if (item.key == config.url) {
            item.c() // 取消请求
            return false // 从数组中删除该项
          }
          return true // 保留其他项
        })
        cancelArr.push({
          key: config.url,
          c,
        })
      }
    }),
  })
}

const methods = ['get', 'post', 'delete', 'put', 'patch']
methods.forEach((type) => {
  request[type] = (url, data, options) => {
    return request({ url, method: type, data, ...options })
  }
})

function getAxiosParams(config) {
  const dataType = ['post']
  return dataType.includes(config.method) ? config.data : config.params
}

function deleteCurrentRequest(config = {}) {
  $g._.remove(currentRequestList, (n) => {
    return n.url == config.url
  })
}

export default request
