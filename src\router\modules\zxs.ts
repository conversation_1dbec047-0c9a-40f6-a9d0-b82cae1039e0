import Layout from '@/layout/index.vue'

/**
 * 成天学习
 * meta.fullPage  默认所有都为false，在permissions.ts中取反
 * meta.debugQuery  从debugging 页面点击跳转时，可以携带的query参数，便于调试
 * meta.keepAliveArr  跳转到指定页面时，对本页面进行缓存,值为指定页面的路由name组成的数组
 */
export default [
  {
    path: '/zxs',
    name: 'ZXS',
    component: Layout,
    redirect: '/zxs/home/<USER>',
    meta: { title: '智习室APP' },
    children: [
      {
        path: 'home',
        name: 'Home',
        meta: { title: '首页' },
        redirect: '/zxs/home/<USER>',
        children: [
          {
            path: 'nbxHome',
            name: 'NbxHome',
            component: () => import('@/views/zxs/home/<USER>'),
            meta: {
              title: '内部校首页',
              keepAliveArr: [
                'MistakeMain',
                'UnitMain',
                'ActivityIndex',
                'ChangePwdIndex',
              ],
            },
          },
          {
            path: 'hxnHome',
            name: 'HxnHome',
            component: () => import('@/views/zxs/home/<USER>'),
            meta: {
              title: '好学鸟首页',
              keepAliveArr: [
                'MistakeMain',
                'UnitMain',
                'ActivityIndex',
                'ChangePwdIndex',
              ],
            },
          },
        ],
      },
      {
        path: 'unitPass',
        name: 'UnitPass',
        meta: { title: '单元闯关' },
        redirect: '/zxs/unitPass/unitMain',
        children: [
          {
            path: 'unitMain',
            name: 'UnitMain',
            component: () => import('@/views/zxs/unitPass/unitMain/index.vue'),
            meta: {
              title: '首页',
              keepAliveArr: ['UnitExercise', 'UnitReport', 'UnitStudy'],
            },
          },
          {
            path: 'unitExercise',
            name: 'UnitExercise',
            component: () =>
              import('@/views/zxs/unitPass/unitExercise/index.vue'),
            meta: {
              title: '做题页',
            },
          },
          {
            path: 'unitStudy',
            name: 'UnitStudy',
            component: () => import('@/views/zxs/unitPass/unitStudy/index.vue'),
            meta: {
              title: '小节学习',
            },
          },
          {
            path: 'unitReport',
            name: 'UnitReport',
            component: () =>
              import('@/views/zxs/unitPass/unitReport/index.vue'),
            meta: {
              title: '报告页',
            },
          },
        ],
      },
      {
        path: 'aiStudentPartner',
        name: 'AiStudentPartner',
        redirect: '/zxs/aiStudentPartner/main',
        meta: {
          title: 'AI学伴',
        },
        children: [
          {
            path: 'main',
            name: 'AiStudentPartnerMain',
            component: () => import('@/views/zxs/aiStudentPartner/index.vue'),
            meta: {
              title: '首页',
            },
          },
          {
            path: 'more',
            name: 'AiStudentPartnerMore',
            component: () =>
              import('@/views/zxs/aiStudentPartner/more/index.vue'),
            meta: {
              title: '查看更多',
            },
          },
          {
            path: 'targetScore',
            name: 'TargetScore',
            component: () =>
              import('@/views/zxs/aiStudentPartner/targetScore/index.vue'),
            meta: {
              title: '目标成绩',
            },
          },
          {
            path: 'recommendSchool',
            name: 'RecommendSchool',
            component: () =>
              import('@/views/zxs/aiStudentPartner/recommendSchool/index.vue'),
            meta: {
              title: '智推院校',
            },
          },
          {
            path: 'recommendMajor',
            name: 'RecommendMajor',
            component: () =>
              import('@/views/zxs/aiStudentPartner/recommendMajor/index.vue'),
            meta: {
              title: '智推专业',
            },
          },
        ],
      },
      {
        path: 'syncTrain',
        name: 'SyncTrain',
        meta: { title: '校本同步' },
        redirect: '/zxs/syncTrain/index',
        children: [
          {
            path: 'index',
            name: 'SyncTrainIndex',
            component: () => import('@/views/zxs/syncTrain/index.vue'),
            meta: {
              title: '本周同步',
            },
          },
          {
            path: 'chapterList',
            name: 'ChapterList',
            component: () =>
              import('@/views/zxs/syncTrain/chapterList/index.vue'),
            meta: {
              title: '查看全部',
            },
          },
          {
            path: 'weekTask',
            name: 'WeekTask',
            component: () => import('@/views/zxs/syncTrain/weekTask/index.vue'),
            meta: {
              title: '周任务',
            },
          },
          {
            path: 'preview',
            name: 'SyncTrainPreview',
            component: () => import('@/views/zxs/syncTrain/preview/index.vue'),
            meta: {
              title: '导学案预览',
            },
          },
          {
            path: 'doTask',
            name: 'DoTask',
            component: () => import('@/views/zxs/syncTrain/doTask/index.vue'),
            meta: {
              title: '训练页',
              keepAliveArr: ['AiTeacherMain'],
            },
          },
          {
            path: 'taskReport',
            name: 'TaskReport',
            component: () =>
              import('@/views/zxs/syncTrain/taskReport/index.vue'),
            meta: {
              title: '任务报告',
            },
          },
        ],
      },
      {
        path: 'mistake',
        name: 'Mistake',
        redirect: '/zxs/mistake/main',
        meta: {
          title: '启鸣错题本',
        },
        children: [
          {
            path: 'main',
            name: 'MistakeMain',
            component: () => import('@/views/zxs/mistake/index.vue'),
            meta: {
              title: '启鸣错题本首页',
            },
          },
          {
            path: 'zxsMistake',
            name: 'ZxsMistake',
            component: () => import('@/views/zxs/mistake/zxsMistake/index.vue'),
            meta: {
              title: '学科错题查询',
            },
          },

          {
            path: 'mistakeList',
            name: 'MistakeList',
            component: () =>
              import('@/views/zxs/mistake/mistakeList/index.vue'),
            meta: {
              title: '错题本列表',
            },
          },
          {
            path: 'mistakePractice',
            name: 'MistakePractice',
            component: () =>
              import('@/views/zxs/mistake/mistakePractice/index.vue'),
            meta: {
              title: '练习',
            },
          },
          {
            path: 'questionCard',
            name: 'QuestionCard',
            component: () =>
              import('@/views/zxs/mistake/questionCard/index.vue'),
            meta: {
              title: '答题卡',
            },
          },
        ],
      },
      {
        path: 'aiTeacher',
        name: 'AiTeacher',
        redirect: '/zxs/aiTeacher/aiTeacherMain',
        meta: {
          title: 'AI讲解',
        },
        children: [
          {
            path: 'aiTeacherMain',
            name: 'AiTeacherMain',
            component: () => import('@/views/zxs/aiTeacher/index.vue'),
            meta: {
              title: 'AI讲解首页',
              keepAliveArr: ['QuestionDetail'],
              debugQuery: { questionId: 973379 },
            },
          },
          {
            path: 'questionDetail',
            name: 'QuestionDetail',
            component: () =>
              import('@/views/zxs/aiTeacher/questionDetail/index.vue'),
            meta: {
              title: '查看原题',
              debugQuery: { questionId: 973379 },
            },
          },
        ],
      },
      {
        path: 'evaluation',
        name: 'Evaluation',
        redirect: '/zxs/evaluation/report',
        meta: {
          title: '测评',
        },
        children: [
          {
            path: 'evaluationReport',
            name: 'EvaluationReport',
            component: () => import('@/views/zxs/evaluation/report/index.vue'),
            meta: {
              title: '完整报告',
              keepAliveArr: [''],
              debugQuery: { student_report_id: 13201 },
            },
          },
        ],
      },
      {
        path: 'activity',
        name: 'Activity',
        redirect: '/zxs/activity/activityIndex',
        meta: {
          title: '活动页',
        },
        children: [
          {
            path: 'activityIndex',
            name: 'ActivityIndex',
            component: () => import('@/views/zxs/activityIndex/index.vue'),
            meta: {
              title: '活动页',
            },
          },
        ],
      },
      {
        path: 'activityPublic',
        name: 'ActivityPublic',
        redirect: '/zxs/activityPublic/activityPublicIndex',
        meta: {
          title: '活动页',
        },
        children: [
          {
            path: 'activityPublicIndex',
            name: 'ActivityPublicIndex',
            component: () => import('@/views/zxs/activityIndex/index.vue'),
            meta: {
              title: '活动页',
              public: true,
            },
          },
        ],
      },
      {
        path: 'changePwd',
        name: 'ChangePwd',
        redirect: '/zxs/changePwd/index',
        meta: {
          title: '修改密码',
        },
        children: [
          {
            path: 'index',
            name: 'ChangePwdIndex',
            component: () => import('@/views/zxs/home/<USER>/index.vue'),
            meta: {
              title: '修改密码',
            },
          },
        ],
      },
    ],
  },
]
