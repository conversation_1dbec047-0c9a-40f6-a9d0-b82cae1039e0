---
type: 'always_apply'
---

# Vue3 项目开发规范

> **【重要提醒】** 每次提问需获取 mcp 智能调用规则，判断问题与项目关系后再决定改动

## 技术栈配置

### 核心技术栈

- **主要技术**：Vue 3 + TypeScript + yarn@1.22.22
- **常用库**：lodash(`$g._`)、VueUse、TailwindCSS
- **自动引入**：vue、pinia、vue-router、@vueuse/core 等无需手动引入

### API 数据访问规范

- **核心规则**：**严禁使用 .data 属性访问数据**
- API 响应数据直接返回 data 字段内容，直接使用返回数据对象
- ✅ `const userList = await fetchUserList()`
- ❌ `userList.data` (严禁此写法)

### 资源引入规范

- **图片资源**：必须使用`$g.tool.getFileUrl()`方法
- 支持相对/绝对路径，自动处理资源路径和缓存
- ❌ 禁止直接 import 图片：`import avatarImg from '@/assets/avatar.png'`

### 图标系统

- **unplugin-icons**：标签格式`svg-图标前缀-图标名称`
- **g-icon 组件**：支持 remixicon，配置 name、size、color

## Vue 组件开发规范

### 基础要求

1. 统一使用 Composition API + script setup 语法糖
2. 组件命名：PascalCase 大驼峰（如：UserProfile.vue）
3. 页面组件：放到当前页面同级的 components 目录内

### 组件标准结构

```vue
<template>
  <div class="组件名-kebab-case">
    <!-- HTML结构 -->
  </div>
</template>

<script setup lang="ts">
// 按顺序组织：类型定义 > Props > Emits > 响应式数据 > 计算属性 > 函数 > 生命周期钩子
</script>
```

### 响应式数据管理

- 基础数据类型：使用`$ref`语法糖
- 数组类型：`$ref`结合泛型定义
- 传递给子组件：使用`$$refs`函数包装

### 类型命名约定

- **Interface 接口**：I 前缀 + 大驼峰命名 (IUserInfo)
- **Type 类型**：T 前缀 + 大驼峰命名 (TButtonSize)
- **Enum 枚举**：中文命名，每个枚举值右侧用注释说明含义

```ts
enum 用户状态 {
  激活 = 'active', // 正常
  禁用 = 'disabled', // 禁用
}
```

### 函数定义规范

- **命名规则**：动词开头

  - 事件处理：`handleXxx`
  - 验证：`validateXxx`
  - 数据获取：`fetchXxx`
  - 布尔判断：`isXxx`/`hasXxx`/`canXxx`

- **异步函数规范**：
  - 声明为 async
  - 返回值类型为 Promise 包装类型
  - 使用 try-catch 处理异常
  - 错误时返回 null 或默认值

### 变量命名规范

- 英文命名 + 中文注释
- 布尔类型：`isLoggedIn`、`isLoading`
- 数值类型：`currentPage`、`pageSize`
- 常量：全大写+下划线 `API_BASE_URL`

### 注释规范

- **$ref 变量**：三星号普通注释，简明说明用途
- **函数注释**：
  - 简单函数：一行注释说明功能
  - 中等函数：功能说明+参数+返回值
  - 复杂函数：详细说明+参数+返回值+使用示例

## TailwindCSS 与样式规范

### 间距系统

- **强制使用 px 单位**：w-100px、h-200px、mt-20px、px-24px
- **禁止默认单位**：避免 w-24、h-12、mt-5、px-6 等

### 颜色系统

- **优先级**：主题色 > 自定义颜色 > 透明度
- **基础颜色**：bg-[#fff]、text-[#333]、border-[#e5e5e5]
- **状态颜色**：success(#28a745)、error(#dc3545)、warning(#ffc107)

### SCSS 深度选择器

- 外层使用组件类名包裹
- 内层使用:deep()选择器，括号内不放内容

## 核心开发要点

### 计算属性与侦听器

- **computed**：根据依赖自动更新，具有缓存特性
- **watch**：侦听响应式数据变化，执行回调函数

### 生命周期钩子

- **onMounted**：初始化、获取数据、事件监听、库初始化
- **onUnmounted**：清理定时器、移除监听器、取消请求

### 必须遵循的规则

1. 每次开发前判断问题是否与项目相关
2. 查看 tailwind 配置文件
3. 使用 script setup 语法
4. 使用$ref 响应式语法糖
5. 函数命名必须动词开头
6. TailwindCSS 必须使用 px 单位

### 代码质量要求

- 类型安全：明确类型定义
- 注释规范：中文注释说明
- 命名规范：英文命名+中文注释
- 结构清晰：标准模板结构
- 性能优化：合理使用计算属性
- 错误处理：异步函数错误处理

## 问题解决规范

### 解决问题时

- 全面阅读相关代码文件，理解所有代码的功能和逻辑。
- 分析导致错误的原因，提出解决问题的思路。
- 与用户进行多次交互，根据反馈调整解决方案。
- 善用 Vue DevTools 进行调试和性能分析。
- 当一个 bug 经过两次调整仍未解决时，你将启动系统二思考模式：
  1. 系统性分析 bug 产生的根本原因
  2. 提出可能的假设
  3. 设计验证假设的方法
  4. 提供三种不同的解决方案，并详细说明每种方案的优缺点
  5. 让用户根据实际情况选择最适合的方案
