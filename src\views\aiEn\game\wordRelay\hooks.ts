// import { useWebSocket } from '@vueuse/core'

export function useGame() {
  // 初始化单词列表
  const wordList = [
    'apple',
    'elephant',
    'tiger',
    'rabbit',
    'tree',
    'eagle',
    'goat',
    'trumpet',
  ]
  //已使用单词
  const usedWords: any = ref([])
  //时间限制秒数
  const countdown = ref(15)
  //真实结束时间戳
  const overTime = ref(0)
  // 初始化对话列表
  const conversation = ref<any>([])
  // 当前需要接龙的单词
  const currentWord = ref('')
  //游戏结束标识
  const gameOver = ref(false)
  //是否胜利
  const isWin = ref(false)
  //提示次数
  const hintChances = ref(3)
  //当前接龙玩家
  const currentPlayer = ref('system')
  const timer: any = ref(null)

  // 开始倒计时
  const startCountdown = () => {
    if (timer.value) clearTimeout(timer.value)
    countdown.value = 15
    overTime.value = new Date().getTime() + 15 * 1000
    poll()
  }

  //计时轮询
  const poll = () => {
    timer.value = setTimeout(() => {
      const dataTime = new Date().getTime()
      if (countdown.value > 0 && overTime.value > dataTime) {
        countdown.value--
        poll()
      } else {
        countdown.value = 0
        clearTimeout(timer.value)
        gameOver.value = true
        isWin.value = currentPlayer.value === ''
        $g.showToast(
          `${currentPlayer.value === 'user' ? '您已' : '电脑'}超时，游戏结束。`,
        )
      }
    }, 1000)
  }

  // 检查输入的单词是否正确
  const checkWord = async (inputSendWord: string) => {
    //单词检查
    let isRight = false
    // await new Promise((resolve) =>
    //   setTimeout(() => {
    isRight = true
    //     resolve(true)
    //   }, 2000),
    // )
    if (!isRight) {
      gameOver.value = true
      isWin.value = true
      $g.showToast(
        `${currentPlayer.value === 'user' ? '您' : '电脑'}单词拼写错误，游戏结束。`,
      )
    }
    return isRight
  }

  // 切换玩家
  const switchPlayer = () => {
    currentPlayer.value = currentPlayer.value === 'user' ? 'system' : 'user'
  }

  //消息添加
  const messageListAdd = (data) => {
    conversation.value.push(data)
  }

  //获取1-6秒随机数
  function getRandomNumber() {
    return (Math.floor(Math.random() * 6) + 1) * 1000
  }

  // 电脑回合
  const computerTurn = async (isInit = '') => {
    // 电脑回合开始，倒计时启动
    startCountdown()
    const lastChar = currentWord.value[currentWord.value.length - 1]
    const availableWords = wordList.filter(
      (word) => word[0] === lastChar && !usedWords.value.includes(word),
    )
    // 添加 1-6 秒延时
    await new Promise((resolve) => setTimeout(resolve, getRandomNumber()))
    if (availableWords.length > 0 || isInit) {
      const randomIndex = Math.floor(Math.random() * availableWords.length)
      const computerWord = isInit || availableWords[randomIndex]
      const isRight = await checkWord(computerWord)
      if (!isRight) return
      usedWords.value.push(computerWord)
      messageListAdd({
        sender: 'system',
        text: computerWord,
      })
      nextTick(() => {
        $g.bus.emit('wordListBottom')
      })
      currentWord.value = computerWord
      //切换接龙用户
      switchPlayer()
      userTurn()
    } else {
      // gameOver.value = true
      // isWin.value = true
      // $g.showToast('电脑没有合适的单词，你赢了！')
    }
  }

  //用户回合
  const userTurn = () => {
    //开启用户回答倒计时
    startCountdown()
  }

  //用户消息发送
  async function userSend(inputSendWord: string) {
    const isRight = await checkWord(inputSendWord)
    if (!isRight) return
    //模拟提交延时
    // await new Promise((resolve) => setTimeout(resolve, 18000))
    if (gameOver.value) return
    if (
      inputSendWord[0] === currentWord.value[currentWord.value.length - 1] &&
      !usedWords.value.includes(inputSendWord)
    ) {
      // 正确答案，更新当前单词
      usedWords.value.push(inputSendWord)
      messageListAdd({
        sender: 'user',
        text: inputSendWord,
      })
      nextTick(() => {
        $g.bus.emit('wordListBottom')
      })
      currentWord.value = inputSendWord
      //切换用户
      switchPlayer()
      //电脑执行接龙
      computerTurn()
    } else {
      gameOver.value = true
      isWin.value = false
      if (usedWords.value.includes(inputSendWord)) {
        $g.showToast('单词重复，游戏结束，你输了！')
      } else {
        $g.showToast('答案错误，游戏结束，你输了！')
      }
    }
  }

  // function

  // 初始化游戏
  const initGame = () => {
    const randomIndex = Math.floor(Math.random() * wordList.length)
    computerTurn(wordList[randomIndex])
  }

  //状态重置
  const iniStatus = () => {
    currentPlayer.value = ''
    clearTimeout(timer.value)
  }

  watch(
    () => gameOver.value,
    () => {
      if (gameOver.value) {
        iniStatus()
      }
    },
  )

  onMounted(() => {
    initGame()
  })

  onUnmounted(() => {
    iniStatus()
  })

  // 通过 provide 提供给子组件的数据
  provide('worldGame', {
    userSend, // 发送数据
    gameOver, //游戏结束
  })

  return {
    countdown,
    gameOver,
    currentPlayer,
    hintChances,
    isWin,
    conversation,
  }
}
