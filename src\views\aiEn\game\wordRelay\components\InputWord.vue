<template>
  <div class="flex items-center">
    <van-button
      type="primary"
      size="small"
      :disabled="currentPlayer != 'user' || isHintDisabled"
      @click="useHint"
    >
      提示
    </van-button>
    <van-field
      ref="inputRef"
      v-model="inputWord"
      class="flex-1 br-[13px] mx-10px"
      :placeholder="currentPlayer != 'user' ? '输入框已禁用' : '请输入接龙单词'"
      :disabled="currentPlayer != 'user'"
      autocomplete="off"
      type="url"
      @keyup.enter="inputSend"
    ></van-field>
    <van-button
      type="primary"
      size="small"
      :disabled="currentPlayer != 'user'"
      :loading="sendLoading"
      @click="inputSend"
    >
      发送
    </van-button>
    <van-button
      type="primary"
      size="small"
      :disabled="currentPlayer != 'user'"
      class="ml-10px"
      @click="showTranslateDialog = true"
    >
      语音
    </van-button>
    <van-popup v-model:show="showHintPopup" round closeable position="bottom">
      <div>
        <div
          class="py-16px border-b-[1px] border-[#efefef] px-10px text-[16px]"
        >
          接龙提示（还剩{{ hintChances }}次提示机会）
        </div>
        <div class="flex items-center justify-center py-40px">
          <div
            v-for="(item, index) in chooseWordList"
            :key="item"
            :class="{
              '!bg-theme-primary !text-[white]': chooseWord == item,
              'mx-20px': index == 1,
            }"
            class="bg-[#f5f5f5] br-[13px] px-15px py-2px"
            @click="chooseWord = item"
          >
            {{ item }}
          </div>
        </div>
        <van-button
          type="primary"
          class="w-full !br-[0px]"
          @click="confirmSelection"
        >
          立即使用
        </van-button>
      </div>
    </van-popup>
    <TranslateDialog v-model:show="showTranslateDialog" dialogType="onlyEn">
      <template #otheruse>
        <div class="absolute top-[190px]" @click="useKeyBoard">111</div>
      </template>
    </TranslateDialog>
  </div>
</template>

<script setup lang="ts">
import TranslateDialog from '@/views/aiEn/home/<USER>/components/AudioSendBar/components/TranslateDialog.vue'

const props = defineProps<{
  currentPlayer: string
}>()

const { userSend, gameOver } = inject<any>('worldGame')
//提示次数
let hintChances = $ref(3)
// 新增变量控制提示按钮禁用状态
let isHintDisabled = $ref(false)
let inputWord = $ref('')
//提示弹框
let showHintPopup = $ref(false)
//提示弹框选择单词
let chooseWord: any = $ref('')
//提示弹框选择单词列表
let chooseWordList: any = $ref([])
//发送按钮loading
let sendLoading = $ref(false)
//语音弹框
let showTranslateDialog = $ref(false)
const inputRef = $ref<any>(null)

//发送单词
async function inputSend() {
  try {
    if (!inputWord.trim()) return
    inputWord = inputWord.trim()
    isHintDisabled = true
    sendLoading = true
    nextTick(() => {
      inputWord = ''
    })
    await userSend(inputWord)
    inputRef.blur()
  } catch (error) {
    console.log(`⚡[ error ] >`, error)
  } finally {
    isHintDisabled = false
    sendLoading = false
  }
}

//选择提示单词
function confirmSelection() {
  if (!chooseWord) return $g.showToast('请选择单词')
  isHintDisabled = true
  inputWord = chooseWord
  showHintPopup = false
  chooseWord = ''
  chooseWordList = []
}

//使用提示
const useHint = () => {
  showHintPopup = true
  if (!chooseWordList.length) {
    hintChances--
  } else {
    chooseWordList = ['dog', 'did', 'data']
  }
}

function useKeyBoard() {
  showTranslateDialog = false
  setTimeout(() => {
    inputRef.focus()
  }, 300)
}

watch(
  () => gameOver,
  (newVal) => {
    if (newVal) {
      showHintPopup = false
      inputRef.blur()
    }
  },
)
</script>

<style lang="scss" scoped></style>
