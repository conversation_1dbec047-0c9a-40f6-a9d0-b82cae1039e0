<template>
  <div>
    <g-navbar customTitle="单词接龙" customBackGround="transparent"> </g-navbar>
    <div
      class="px-12px pb-40px pt-36px overflow-auto no-bar"
      :style="{
        height: `calc(100vh - ${$g.navBarTotalHeight}px - 16px)`,
      }"
    >
      <div>
        {{ matchTime }}
      </div>
      <div class="flex justify-center mt-30px">
        <van-button
          type="primary"
          class="w-[247px] h-[48px] br-[18px] text-[18px]"
          size="small"
          @click="isMatching ? cancelMatch() : startMatch()"
        >
          {{ isMatching ? '取消匹配' : '开始匹配' }}
        </van-button>
      </div>
    </div>
    <div
      class="h-[100vh] w-full fixed top-0 left-0 bg-[rgba(0,0,0,0.7)] z-[-1] flex flex-col justify-center"
      :class="{ '!z-[999]': isMatchSuccess }"
    >
      <div class="text-white text-28px w-full text-center">匹配成功</div>
      <div class="mt-60px mb-20px w-full relative h-[291px]">
        <transition name="van-slide-left">
          <div
            v-show="isMatchSuccess"
            class="p-img w-[231px] h-[259px] absolute top-0 left-0 z-[1] pt-42px text-[white]"
          >
            <div
              class="ml-40px w-64px h-64px br-[50%] border border-[white]"
            ></div>
            <div class="mt-10px text-center w-[64%] text-[20px]">AI</div>
            <div class="text-center w-[64%] text-[42px]">45/50</div>
          </div>
        </transition>
        <transition name="van-slide-right">
          <div
            v-show="isMatchSuccess"
            class="k-img w-[231px] h-[259px] mt-32px absolute top-0 right-0 pt-42px flex flex-col items-end text-white"
          >
            <div
              class="mr-40px w-64px h-64px br-[50%] border border-[white]"
            ></div>
            <div class="mt-10px text-center w-[60%] text-[20px]">AI</div>
            <div class="text-center w-[60%] text-[42px]">45/50</div>
          </div>
        </transition>
      </div>
      <div class="text-[white] text-center text-[100px]">{{ jumpTime }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
let isMatching = $ref(true) //匹配中
let matchTime = $ref(15) //匹配时间
let matchTimer: any = $ref(null) //匹配定时器
let matchResolve: any = null // 新增
let isCancelled = $ref(false) //取消匹配
let isMatchSuccess = $ref(false) //匹配成功
let jumpTime = $ref(3) //跳转开始时间

//获取随机数
function getRandomNumber(min, max) {
  let num = Math.floor(Math.random() * (max - min + 1)) + min
  return num * 1000
}

//开始匹配
async function startMatch() {
  isCancelled = false
  isMatching = true
  matchTimer = setInterval(() => {
    matchTime--
  }, 1000)
  await new Promise((resolve) => {
    matchResolve = resolve // 暴露 resolve
    setTimeout(
      () => {
        clearInterval(matchTimer)
        matchTimer = null
        isMatching = false
        resolve(true)
      },
      getRandomNumber(2, 13),
    )
  })
  //匹配成功
  if (!isCancelled) {
    isMatchSuccess = true
    const jumpTimer = setInterval(() => {
      jumpTime--
      if (jumpTime === 0) {
        clearInterval(jumpTimer)
        toGame()
      }
    }, 1000)
  }
}

//跳转游戏
function toGame() {
  router.replace({ name: 'AIEnGameWordRelay' })
}

//取消匹配
function cancelMatch() {
  isCancelled = true
  if (matchTimer) {
    clearInterval(matchTimer)
    matchTimer = null
  }
  isMatching = false
  matchTime = 15
  if (matchResolve) {
    matchResolve() // 提前结束Promise
    matchResolve = null
  }
}

const router = useRouter()

onMounted(() => {
  startMatch()
})
</script>

<style lang="scss" scoped>
.p-img {
  background-image: url('@/assets/img/aiEn/game/p.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.k-img {
  background-image: url('@/assets/img/aiEn/game/k.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>
