// IndexedDB 数据库管理
class LottieCacheDB {
  private dbName = 'LottieCacheDB'
  private version = 1
  private storeName = 'lottie_animations'

  private async openDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version)

      request.onerror = () => reject(request.error)
      request.onsuccess = () => resolve(request.result)

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: 'url' })
          store.createIndex('timestamp', 'timestamp', { unique: false })
        }
      }
    })
  }

  async set(url: string, data: any): Promise<void> {
    const db = await this.openDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)

      const cacheData = {
        url,
        data,
        timestamp: Date.now(),
        expiresIn: 24 * 60 * 60 * 1000, // 24小时过期
      }

      const request = store.put(cacheData)
      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  async get(url: string): Promise<any | null> {
    const db = await this.openDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const request = store.get(url)

      request.onsuccess = () => {
        const cacheData = request.result
        if (!cacheData) {
          resolve(null)
          return
        }

        const now = Date.now()
        if (now - cacheData.timestamp > cacheData.expiresIn) {
          // 过期了，删除缓存
          this.delete(url)
          resolve(null)
          return
        }

        resolve(cacheData.data)
      }

      request.onerror = () => reject(request.error)
    })
  }

  async delete(url: string): Promise<void> {
    const db = await this.openDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      const request = store.delete(url)

      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  async clearExpired(): Promise<void> {
    const db = await this.openDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      const index = store.index('timestamp')
      const now = Date.now()

      const request = index.openCursor()
      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result
        if (cursor) {
          const cacheData = cursor.value
          if (now - cacheData.timestamp > cacheData.expiresIn) {
            cursor.delete()
          }
          cursor.continue()
        } else {
          resolve()
        }
      }
      request.onerror = () => reject(request.error)
    })
  }

  // 新增：清除所有缓存
  async clearAll(): Promise<void> {
    const db = await this.openDB()
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      const request = store.clear()

      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }
}

const lottieDB = new LottieCacheDB()

// 预加载 lottie 动画文件
export const preloadLottieAnimation = async (url: string): Promise<any> => {
  try {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`Failed to fetch: ${response.status}`)
    }
    const animationData = await response.json()

    // 将数据存储到 IndexedDB 中
    await lottieDB.set(url, animationData)

    return animationData
  } catch (error) {
    console.error('预加载 lottie 动画失败:', error)
    throw error
  }
}

// 获取缓存的动画数据
export const getCachedLottieAnimation = async (
  url: string,
): Promise<any | null> => {
  try {
    return await lottieDB.get(url)
  } catch (error) {
    console.error('获取缓存的 lottie 动画失败:', error)
    return null
  }
}

// 清除过期的缓存
export const clearExpiredLottieCache = async (): Promise<void> => {
  try {
    await lottieDB.clearExpired()
    console.log('清除过期缓存成功', lottieDB)
  } catch (error) {
    console.error('清除过期缓存失败:', error)
  }
}

// 新增：清除所有缓存
export const clearAllLottieCache = async (): Promise<void> => {
  try {
    await lottieDB.clearAll()
    console.log('清除所有缓存成功')
  } catch (error) {
    console.error('清除所有缓存失败:', error)
  }
}
