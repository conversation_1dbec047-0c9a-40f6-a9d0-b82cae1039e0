import axios from 'axios'
import EventEmitter from 'eventemitter3'
import { getAIEnOssSignature } from '@/api/aiEn'

class OSS extends EventEmitter {
  api
  params
  OSSConfig
  CancelTokenArr = []
  // 允许上传的文件类型
  allowFileExtension
  // 允许文件大小
  allowFileSize
  // 走云点播
  cloudOnDemand
  uploader
  constructor() {
    super()
  }

  async setOSSConfig(config) {
    const { params, cloudOnDemand, api } = config
    this.params = params
    this.cloudOnDemand = cloudOnDemand
    this.api = api
    await this.getSignature()
  }

  /* 获取oss签名 */
  async getSignature(form?) {
    try {
      const getOssApi = this.api || getAIEnOssSignature
      const data = await getOssApi({ ...this.params })
      this.OSSConfig = {
        OSSAccessKeyId: data.accessId,
        policy: data.policy,
        signature: data.signature,
        dir: data.uploadDir,
        host: data.host,
        file_name: $g.tool.uuid(32),
        absolute_path: data.absolutePath,
      }
      this.allowFileSize = data.allowFileSize
      this.allowFileExtension = data.allowFileExtension
        ? String(
            data.allowFileExtension.map((e) => {
              return '.' + e
            }),
          )
        : ''
    } catch (error) {
      return { error, form }
    }
  }

  /* 单个上传文件至oss,基础上传 */
  async uploadFile(fileObj, form, config = {}) {
    const { file, id, name } = fileObj

    this.params = {
      ...this.params,
      fileSize: file.size,
      fileName: name,
    }
    let lastTime = 0 //上一次计算时间
    let lastSize = 0 //上一次计算的文件大小
    const type = $g.tool.getExt(name)
    await this.getSignature({ id })
    const dir = this.OSSConfig.dir + this.OSSConfig.file_name + '.' + type
    const formData = this.handleFormData({ file, type, name, dir })
    const CancelToken = axios.CancelToken
    return new Promise((resolve, reject) => {
      axios
        .post(this.OSSConfig.host, formData, {
          timeout: 0, // 0表示无超时时间
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          onUploadProgress: (event: any) => {
            const nowTime = new Date().getTime()
            const intervalTime = (nowTime - lastTime) / 1000
            const intervalSize = event.loaded - lastSize

            lastTime = nowTime
            lastSize = event.loaded

            let speed = intervalSize / intervalTime
            let units = 'b/s' //单位名称
            if (speed / 1024 > 1) {
              speed = speed / 1024
              units = 'k/s'
            }
            if (speed / 1024 > 1) {
              speed = speed / 1024
              units = 'M/s'
            }
            const val = Math.floor((event.loaded / event.total) * 100)
            this.emit(
              'onProgress',
              { val, speed: speed.toFixed(1) + units },
              id,
            )
          },
          cancelToken: new CancelToken(
            function executor(c) {
              // @ts-ignore
              this.CancelTokenArr.push({ id, c })
            }.bind(this),
          ),
          ...config,
        })
        .then((res) => {
          // 返回给后端地址
          let resource_url = dir
          const fullUrl = this.OSSConfig.host + '/' + dir
          if (this.OSSConfig.absolute_path) {
            resource_url = this.OSSConfig.host + '/' + dir
          }
          resolve({ resource_url, fullUrl, dir })
        })
        .catch((err) => {
          reject({ file, err, form, isCancel: axios.isCancel(err) })
        })
    })
  }

  /* 处理上传数据 */
  handleFormData({ file, type, name, dir }) {
    const formData = new FormData()
    formData.set(
      'Content-Disposition',
      `attachment;filename="${name || this.OSSConfig.file_name}"`,
    )
    formData.set('OSSAccessKeyId', this.OSSConfig.OSSAccessKeyId)
    formData.set('policy', this.OSSConfig.policy)
    formData.set('signature', this.OSSConfig.signature)
    formData.set('dir', this.OSSConfig.dir)
    formData.set('success_action_status', '200')
    formData.set('key', dir)
    formData.set('file', file)
    return formData
  }

  /* 取消上传 */
  cancelUpload(id) {
    if (!this.CancelTokenArr.length) return
    if (id) {
      const item: any = this.CancelTokenArr.find((v) => (v as any).id === id)
      console.log(item)
      item?.c()
    } else {
      this.CancelTokenArr.forEach((e) => {
        ;(e as any).c()
      })
    }
  }
}
export default OSS
