<template>
  <div>
    <g-navbar customTitle="发言反馈" customBackGround="transparent"></g-navbar>
    <g-loading v-if="loading" class="h-[200px]"></g-loading>
    <div
      v-else
      class="px-[12px] py-[12px] overflow-y-auto no-bar"
      :style="{
        height: `calc(100vh - ${useSettingStore().navBarTotalHeight}px - ${useSettingStore().navigationHeight}px)`,
      }"
    >
      <div class="mb-[24px]">
        <div class="relative w-fit">
          <img
            src="@/assets/img/aiEn/grammar.png"
            class="w-[173px] h-[36px] object-contain"
          />
          <div
            class="absolute text-[14px] font-500 top-[-1px] text-[#FFF] right-[15px]"
          >
            语法测评<span class="text-[24px] ml-[8px] mr-[4px]">{{
              grammarData?.score || 0
            }}</span
            ><span class="text-[12px]">分</span>
          </div>
        </div>
        <div
          class="bg-[#FFF] rounded-r-[12px] rounded-b-[12px] py-[20px] px-[12px]"
        >
          <div
            v-if="grammarData?.grammarAssessDiagnosis?.length"
            class="flex items-center"
          >
            <img
              src="@/assets/img/aiEn/grammarTest.png"
              class="w-[20px] h-[20px] translate-y-[1px] mr-[4px]"
            />
            <div class="text-[16px] font-500 text-[#141619]">语法诊断</div>
          </div>
          <div
            v-if="grammarData?.grammarAssessDiagnosis?.length"
            class="mt-[16px]"
          >
            <div
              v-for="(
                grammar, grammarIndex
              ) in grammarData?.grammarAssessDiagnosis"
              :key="grammarIndex"
              class="mb-[16px]"
            >
              <div class="font-500 text-[#141619]">
                <span v-if="grammarData?.grammarAssessDiagnosis?.length > 1"
                  >{{ grammarIndex + 1 }}.</span
                >{{ grammar.type }}(扣 {{ grammar.deductScore }} 分)
              </div>
              <div class="flex my-[10px]">
                <img
                  src="@/assets/img/aiEn/errorPoint.png"
                  class="w-[16px] h-[16px] translate-y-[1px] mr-[4px]"
                /><span class="text-[13px] text-[#5C5F66]"
                  >错误点：{{ grammar.errorPoint }}</span
                >
              </div>
              <div class="flex">
                <img
                  src="@/assets/img/aiEn/rightPoint.png"
                  class="w-[16px] h-[16px] translate-y-[1px] mr-[4px]"
                /><span class="text-[13px] text-[#5C5F66]"
                  >正确时：{{ grammar.correctForm }}</span
                >
              </div>
            </div>
          </div>
          <div class="flex pt-[8px]">
            <img
              src="@/assets/img/aiEn/rightSentence.png"
              class="w-[20px] h-[20px] translate-y-[2px] mr-[4px]"
            />
            <div class="text-[16px] font-500 text-[#141619]">
              正确句子：{{ grammarData?.correctSentence || '-' }}
            </div>
          </div>
          <div class="flex mt-[24px]">
            <img
              src="@/assets/img/aiEn/praise.png"
              class="w-[20px] h-[20px] translate-y-[2px] mr-[4px]"
            />
            <div class="text-[16px] font-500 text-[#141619]">鼓励反馈</div>
          </div>
          <div class="text-[13px] ml-[24px] text-[#5C5F66] mt-[10px]">
            {{ grammarData?.encouragement || '-' }}
          </div>
        </div>
      </div>
      <div>
        <div class="relative w-fit">
          <img
            src="@/assets/img/aiEn/pronounce.png"
            class="w-[173px] h-[36px] object-contain"
          />
          <div
            class="absolute text-[14px] font-500 top-[-1px] text-[#FFF] right-[15px]"
          >
            口语测评<span class="text-[24px] ml-[8px] mr-[4px]">{{
              speakScore
            }}</span
            ><span class="text-[12px]">分</span>
          </div>
        </div>
        <div class="bg-[#FFF] rounded-r-[12px] rounded-b-[12px] p-[16px]">
          <div class="flex items-center">
            <div class="mr-[50px]">
              <div
                v-for="(progress, progressIndex) in progressList"
                :key="progressIndex"
                class="mb-[12px]"
              >
                <div class="text-[13px] text-[#141619]">
                  {{ progress.label }}：{{ progress.value }}
                </div>
                <van-progress
                  class="w-[135px] mt-[4px]"
                  :percentage="progress.value"
                  stroke-width="6"
                  :show-pivot="false"
                  color="linear-gradient( 45deg, #4A84FF 0%, #8283FF 100%)"
                />
              </div>
            </div>
            <van-circle
              v-model:current-rate="currentRate"
              :size="110"
              :rate="20"
              layer-color="#F2F5FF"
              :stroke-width="60"
              start-position="left"
            >
              <div class="flex h-full w-full justify-center items-center">
                <div>
                  <div class="text-[13px] text-[#5C5F66]">
                    <span class="text-[20px] font-500 text-[#141619]">{{
                      speedOfSpeech
                    }}</span>
                    词/分
                  </div>
                  <div class="text-[13px] font-500 text-[#141619]">语速</div>
                </div>
              </div>
            </van-circle>
          </div>
          <div
            class="w-full h-[1px] bg-[#8590A6]/[0.2] mt-[8px] mb-[20px]"
          ></div>
          <div class="flex items-center">
            <div
              v-for="(state, stateIndex) in stateArr"
              :key="stateIndex"
              class="flex items-center mr-[15px]"
            >
              <div
                :class="state.borderClass"
                class="w-[10px] mr-[3px] h-[10px] rounded-full border-[2px]"
              ></div>
              <span class="text-[10px] font-500" :class="state.nameClass">{{
                state.name
              }}</span>
            </div>
          </div>
          <div class="flex mt-[12px] flex-wrap">
            <span
              v-for="(wordItem, wordIndex) in wordList"
              :key="wordIndex"
              class="text-[14px] font-500"
              :class="classMap[wordItem.assessType]"
            >
              <span v-if="wordItem.assessType && wordIndex">&nbsp;</span
              >{{ wordItem.word }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getGrammarTestApi, getSpeakTestApi } from '@/api/aiEn'
import { useSettingStore } from '@/stores/modules/setting'
let progressList = $ref<any>([
  {
    label: '完整度',
    value: 0,
  },
  {
    label: '流利度',
    value: 0,
  },
  {
    label: '标准度',
    value: 0,
  },
])
let grammarData = $ref<any>(null)
let speedOfSpeech = $ref<any>(null)
let loading = $ref<any>(false)
let stateArr = [
  {
    name: '很完美',
    nameClass: 'text-theme-success',
    borderClass: 'border-theme-success',
  },
  {
    name: '小瑕疵',
    nameClass: 'text-theme-warning',
    borderClass: 'border-theme-warning',
  },
  {
    name: '待提高',
    nameClass: 'text-theme-error',
    borderClass: 'border-theme-error',
  },
  {
    name: '有漏读',
    nameClass: 'text-[#7A8499]',
    borderClass: 'border-[#7A8499]',
  },
]
let currentRate = $ref<any>(0)
let contentHtml = $ref<any>('')
let wordList = $ref<any>([])
let speakScore = $ref<any>(0)
const route = useRoute()
let classMap = {
  1: 'text-theme-success', //很完美
  2: 'text-theme-warning', //小瑕疵
  3: 'text-theme-error', //待提高
  4: 'text-[#7A8499]', //漏读
}
async function getGrammarTest() {
  try {
    loading = true
    const res = await getGrammarTestApi({
      conversationId: route.query.conversationId,
      messageId: route.query.messageId,
    })
    grammarData = res
    loading = false
  } catch (err) {
    loading = false
  }
}
async function getSpeakTest() {
  try {
    const res = await getSpeakTestApi({
      conversationId: route.query.conversationId,
      messageId: route.query.messageId,
    })
    progressList[0].value = res.integrity
    progressList[1].value = res.fluency
    progressList[2].value = res.pronunciation
    speedOfSpeech = res.speedOfSpeech
    speakScore = res.score || 0
    wordList = res.spokenAssessWordsVOList
  } catch (err) {}
}
onMounted(() => {
  getGrammarTest()
  getSpeakTest()
})
</script>

<style lang="scss" scoped></style>
