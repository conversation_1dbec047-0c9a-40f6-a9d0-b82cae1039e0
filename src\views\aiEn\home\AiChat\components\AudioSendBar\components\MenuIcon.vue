<template>
  <div class="menu-icon">
    <div
      v-for="item in data"
      :key="item.img"
      class="w-44px h-52px flex items-center justify-center mb-12px"
      @click="handleClick(item)"
    >
      <img :src="item.img" />
    </div>
  </div>
</template>

<script setup lang="ts">
const data = $ref([
  {
    img: $g.tool.getFileUrl('aiEn/scene.png'), // 情景对话
    type: 'dialog',
    url: '',
  },
])

const emits = defineEmits(['openSceneDialog'])

function handleClick(item: any) {
  emits('openSceneDialog', item)
}
</script>

<style scoped lang="scss"></style>
