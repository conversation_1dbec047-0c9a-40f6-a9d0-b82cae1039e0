<template>
  <div class="markdown-render" :class="{ streaming: isStreaming }">
    <div
      ref="renderContainer"
      class="markdown-content"
      v-html="renderedContent"
    />
  </div>
</template>

<script setup lang="ts">
import MarkdownIt from 'markdown-it'
import { useTableTouchHandler } from './hooks/useTableTouchHandler'
import { useMarkdownBuffer } from './hooks/useMarkdownBuffer'

interface IProps {
  /** markdown内容 */
  content?: string
  /** 是否为流式模式 */
  streaming?: boolean
  /** 渲染配置 */
  options?: IMarkdownOptions
  /** 是否启用缓冲功能 */
  enableBuffer?: boolean
}

interface IMarkdownOptions {
  /** 是否启用链接 */
  linkify?: boolean
  /** 是否启用换行 */
  breaks?: boolean
}

let props = withDefaults(defineProps<IProps>(), {
  content: '',
  streaming: false,
  enableBuffer: false,
  options: () => ({
    linkify: true,
    breaks: true,
  }),
})

// 渲染容器引用
const renderContainer = $ref<HTMLDivElement>()

// 是否正在流式输入
let isStreaming = $ref(false)

// 使用缓冲hook（仅在启用时使用）
const {
  renderableContent: bufferContent,
  addContent,
  flushBuffer,
  clearBuffer,
} = useMarkdownBuffer({
  debounceDelay: 400,
  minBufferSize: 35,
  enableSmartDetection: true,
})

// 最终渲染的HTML内容
let renderedContent = $ref('')

// markdown-it实例
const md = new MarkdownIt({
  html: true,
  linkify: props.options.linkify,
  breaks: props.options.breaks,
})

/**
 * 渲染markdown内容
 * @param content 要渲染的内容
 */
function renderMarkdown(content: string): string {
  try {
    // 清理现有的光标元素
    let cleanContent = content.replace(
      /<span class="typing-cursor"><\/span>/g,
      '',
    )

    // 如果是流式模式，在内容后添加光标
    let markdownContent = cleanContent
    if (isStreaming) {
      markdownContent += '<span class="typing-cursor"></span>'
    }

    // 空内容处理
    if (!cleanContent.trim()) {
      return isStreaming
        ? '<span class="typing-cursor"></span>'
        : '<p>&nbsp;</p>'
    }

    let htmlContent = md.render(markdownContent)

    // 非流式模式下移除光标
    if (!isStreaming) {
      htmlContent = htmlContent.replace(
        /<span class="typing-cursor"><\/span>/g,
        '',
      )
    }

    // 给表格添加滚动容器包装
    htmlContent = htmlContent.replace(
      /<table[^>]*>/g,
      '<div class="table-wrapper"><table>',
    )
    htmlContent = htmlContent.replace(/<\/table>/g, '</table></div>')

    return htmlContent.trim() || '<p>&nbsp;</p>'
  } catch (error) {
    console.warn('Markdown渲染失败:', error)
    return content
  }
}

// 主要的内容监听 - 简化逻辑
watch(
  () => [props.content, props.streaming, props.enableBuffer] as const,
  ([newContent, streaming, enableBuffer]) => {
    isStreaming = streaming

    if (streaming && enableBuffer) {
      // 流式模式且启用缓冲
      const incrementalContent = newContent.slice(
        renderedContent.replace(/<[^>]*>/g, '').length,
      )
      if (incrementalContent) {
        addContent(incrementalContent)
      }
    } else {
      // 非流式模式或禁用缓冲：直接渲染
      if (enableBuffer) {
        clearBuffer()
      }
      renderedContent = renderMarkdown(newContent)
    }
  },
  { immediate: true },
)

// 仅在启用缓冲时监听缓冲内容
watch(bufferContent, (newBufferContent) => {
  if (props.enableBuffer && newBufferContent) {
    renderedContent = renderMarkdown(newBufferContent)
  }
})

// 流式结束时的处理
watch(
  () => props.streaming,
  (newStreaming) => {
    if (!newStreaming && props.enableBuffer) {
      flushBuffer()
      nextTick(() => {
        renderedContent = renderMarkdown(bufferContent.value)
      })
    }
  },
)

// 使用表格触摸处理Hook
const { bindTableTouchEvents } = useTableTouchHandler()

// 绑定表格触摸事件
function bindTouchEvents(): void {
  if (renderContainer) {
    bindTableTouchEvents(renderContainer)
  }
}

onMounted(() => {
  nextTick(() => {
    bindTouchEvents()
  })
})

watch(
  () => renderedContent,
  () => {
    nextTick(() => {
      bindTouchEvents()
    })
  },
)
</script>

<style lang="scss" scoped>
.markdown-render {
  position: relative;

  .markdown-content {
    line-height: 1.5;
    word-wrap: break-word;
    overflow-x: auto;
    max-width: 100%;
    font-size: 16px;
    font-weight: 500;

    // markdown基础样式
    :deep() {
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin: 12px 0 6px 0;
        font-weight: bold;
        line-height: 1.3;
      }

      h1 {
        font-size: 22px;
      }
      h2 {
        font-size: 20px;
      }
      h3 {
        font-size: 18px;
      }
      h4 {
        font-size: 16px;
      }
      h5 {
        font-size: 15px;
      }
      h6 {
        font-size: 14px;
      }

      p {
        margin: 6px 0;
        line-height: 1.5;
        font-size: 16px;
        font-weight: 500;
      }

      // 代码样式
      code {
        background: #f5f5f5;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Courier New', Courier, monospace;
        font-size: 14px;
        color: #d14;
      }

      pre {
        background: #f8f8f8;
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        padding: 12px;
        margin: 12px 0;
        overflow-x: auto;
        font-size: 14px;

        code {
          background: none;
          padding: 0;
          color: inherit;
        }
      }

      // 列表样式
      ul,
      ol {
        margin: 6px 0;
        padding-left: 20px;

        li {
          margin: 2px 0;
          font-size: 16px;
          line-height: 1.5;
          font-weight: 500;
        }
      }

      // 链接样式
      a {
        color: #0366d6;
        text-decoration: none;
        font-size: 16px;
        font-weight: 500;

        &:hover {
          text-decoration: underline;
        }
      }

      // 引用样式
      blockquote {
        border-left: 3px solid #dfe2e5;
        padding: 0 12px;
        margin: 12px 0;
        color: #6a737d;
        font-size: 16px;
        line-height: 1.5;
        font-weight: 500;
      }

      // 表格样式 - 响应式处理
      .table-wrapper {
        overflow-x: auto;
        margin: 12px 0;
        border-radius: 6px;
        border: 1px solid #dfe2e5;

        // 滚动条样式优化
        &::-webkit-scrollbar {
          height: 8px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 4px;

          &:hover {
            background: #a8a8a8;
          }
        }
      }

      table {
        border-collapse: collapse;
        margin: 0;
        width: auto;
        min-width: 100%;
        border-radius: 0;
        border: none;

        th,
        td {
          border: none;
          border-right: 1px solid #dfe2e5;
          padding: 12px 16px;
          text-align: left;
          white-space: nowrap;
          min-width: 100px;
          display: table-cell;

          &:last-child {
            border-right: none;
          }
        }

        th {
          background: #f6f8fa;
          font-weight: bold;
          border-bottom: 2px solid #dfe2e5;
          font-size: 15px;
        }

        td {
          font-size: 14px;
        }

        tr:not(:last-child) td {
          border-bottom: 1px solid #f1f3f4;
        }

        // 小屏幕适配
        @media (max-width: 768px) {
          th,
          td {
            padding: 10px 12px;
            min-width: 80px;
            font-size: 13px;
          }

          th {
            font-size: 14px;
          }
        }
      }

      // 分割线
      hr {
        border: none;
        height: 1px;
        background: #e1e4e8;
        margin: 16px 0;
      }

      // 强调样式
      strong {
        font-weight: bold;
      }

      em {
        font-style: italic;
      }
    }
  }

  // 光标样式
  :deep(.typing-cursor) {
    display: inline-block;
    width: 16px !important;
    height: 16px !important;
    min-width: 16px;
    min-height: 16px;
    max-width: 16px;
    max-height: 16px;
    background-image: url('https://qm-cloud.oss-cn-chengdu.aliyuncs.com/public/otherType/ty-private-cursor.apng');
    background-size: 16px 16px !important;
    background-repeat: no-repeat;
    background-position: center;
    margin-left: 2px;
    vertical-align: middle;
    font-size: 0;
    line-height: 1;
  }

  // 流式模式的特殊样式
  &.streaming {
    .markdown-content {
      transition: none;
      position: relative;
    }
  }
}
</style>
