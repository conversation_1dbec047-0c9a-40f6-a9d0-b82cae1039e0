<template>
  <div>
    <g-navbar customTitle="我的" customBackGround="transparent"> </g-navbar>
    <div class="px-[12px] pb-[12px] mt-[16px]">
      <div class="flex items-center">
        <div class="w-fit rounded-full mr-[12px]">
          <img
            :src="useAiStore?.aiEnUserInfo?.headPicture"
            class="w-[48px] rounded-full h-[48px] object-contain"
          />
        </div>
        <span class="text-[22px] font-500 text-[#141619]">{{
          useAiStore?.aiEnUserInfo?.userName
        }}</span>
      </div>
      <div class="bg-[#FFF] mt-[16px] br-[12px] py-[16px] pr-[16px]">
        <div class="flex items-center">
          <img
            src="@/assets/img/aiEn/mine/title.png"
            class="w-[3px] h-[14px]"
          />
          <span class="text-[15px] font-500 text-[#141619] ml-[11px]"
            >AI老师设置</span
          >
        </div>
        <div class="px-[16px]">
          <div class="w-full h-[1px] bg-[#8590A6]/[0.2] my-[15px]"></div>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <img
                src="@/assets/img/aiEn/mine/horn.png"
                class="w-[20px] h-[20px]"
              />
              <sapn class="text-[15px] text-[#141619] ml-[12px]"
                >语音自动播放</sapn
              >
            </div>
            <van-switch
              v-model="useAiStore.config.voiceAutoPlayStatus"
              size="20px"
            />
          </div>
          <div class="w-full h-[1px] bg-[#8590A6]/[0.2] my-[15px]"></div>
          <div class="flex items-center mb-[21px]">
            <img
              src="@/assets/img/aiEn/mine/speed.png"
              class="w-[20px] h-[20px]"
            />
            <sapn class="text-[15px] text-[#141619] ml-[12px]">语音速度</sapn>
          </div>
          <div class="px-[11px]">
            <van-slider
              v-model="voiceSpeed"
              :button-size="20"
              step="1"
              :barHeight="10"
              min="1"
              max="5"
              @change="onSpeedChange"
            />
          </div>
          <div class="flex mt-[10px] justify-between px-[4px]">
            <span
              v-for="(num, numIndex) in speedList"
              :key="numIndex"
              class="text-[13px] font-500"
              :class="{
                'text-[#6666FF]': num.value == voiceSpeed,
              }"
              >{{ num.label }}</span
            >
          </div>
        </div>
      </div>
      <div class="bg-[#FFF] mt-[16px] br-[12px] py-[16px] pr-[16px]">
        <div class="flex items-center">
          <img
            src="@/assets/img/aiEn/mine/title.png"
            class="w-[3px] h-[14px]"
          />
          <span class="text-[15px] font-500 text-[#141619] ml-[11px]"
            >学习设置</span
          >
        </div>
        <div class="px-[16px]">
          <div class="w-full h-[1px] bg-[#8590A6]/[0.2] my-[15px]"></div>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <img
                src="@/assets/img/aiEn/mine/textbook.png"
                class="w-[20px] h-[20px]"
              />
              <sapn class="text-[15px] text-[#141619] ml-[12px]">教材选择</sapn>
            </div>
            <span class="text-[15px] text-[#7A8499]">{{
              useAiStore.study?.sysTextbookVersionName
            }}</span>
          </div>
          <div class="w-full h-[1px] bg-[#8590A6]/[0.2] my-[15px]"></div>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <img
                src="@/assets/img/aiEn/mine/grade.png"
                class="w-[20px] h-[20px]"
              />
              <sapn class="text-[15px] text-[#141619] ml-[12px]">年级选择</sapn>
            </div>
            <span class="text-[15px] text-[#7A8499]">{{
              useAiStore.study.sysGradeName
            }}</span>
          </div>
        </div>
      </div>
      <div class="bg-[#FFF] mt-[16px] br-[12px] p-[16px]">
        <div class="flex items-center justify-between" @click="onSecret">
          <div class="flex items-center">
            <img
              src="@/assets/img/aiEn/mine/secret.png"
              class="w-[20px] h-[20px]"
            />
            <sapn class="text-[15px] text-[#141619] ml-[12px]">协议管理</sapn>
          </div>
          <img
            src="@/assets/img/aiEn/mine/right.png"
            class="w-[18px] h-[18px]"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAiSettingStore } from '@/stores/modules/aiEnSetting'
import { editConfigApi } from '@/api/aiEn'
const useAiStore = useAiSettingStore()
const router = useRouter()
let { config } = storeToRefs(useAiSettingStore())
function onSecret() {
  router.push({ name: 'SecretMain' })
}
let voiceSpeed = $ref<any>('3')
function onSpeedChange(speedValue) {
  config.value.voiceSpeed = speedList.find(
    (item) => item.value == speedValue,
  )?.label
  editConfig()
}
onMounted(() => {
  voiceSpeed = speedList.find(
    (item) => item.label == useAiStore.config.voiceSpeed,
  )?.value
})
//因为滑块步长是相等的，所以建立滑块和值的一一对应关系
let speedList = $ref<any>([
  { label: '0.5', value: '1' },
  { label: '0.8', value: '2' },
  { label: '1', value: '3' },
  { label: '1.2', value: '4' },
  { label: '1.5', value: '5' },
])
async function editConfig() {
  const res = await editConfigApi({
    type: 'AI_TEACHER',
    config: useAiStore.config,
  })
}
watch(
  () => [useAiStore.config.voiceAutoPlayStatus],
  () => {
    if (
      useAiStore.config.voiceSpeed != null &&
      useAiStore.config.voiceAutoPlayStatus != null
    ) {
      editConfig()
    }
  },
)
</script>

<style scoped></style>
