<template>
  <div>
    <div class="flex text-16px">
      <div ref="menuRef" class="relative">
        <Transition name="slide-up">
          <div v-if="showMenu" class="absolute bottom-[44px] left-0">
            <MenuIcon @open-scene-dialog="openSceneDialog" />
          </div>
        </Transition>
        <div
          v-if="mode == 'free'"
          class="circle-icon gradient-text text-center leading-[44px]"
          :data-text="showMenu ? 'X' : 'Say'"
          @click="toggleMenu"
        >
          {{ showMenu ? 'X' : 'Say' }}
        </div>
        <div
          v-if="mode == 'theme'"
          class="circle-icon"
          @click="showSceneDialog = true"
        >
          <img
            class="w-[24px] h-[24px]"
            :src="$g.tool.getFileUrl('aiEn/sceneInit.png')"
          />
        </div>
      </div>
      <div class="flex-1 relative mx-8px h-[44px]">
        <div
          v-if="isAliRealTimeRecording"
          class="absolute -top-[30px] -left-[8px] -right-[8px] flex justify-center items-center text-[12px] text-[#7A8499]"
        >
          <Transition name="fade-in">
            <div
              v-if="!willCancel"
              class="bg-[#000]/40 rounded-[12px] text-white px-10px py-2px"
            >
              松开发送，上滑取消
            </div>
          </Transition>
        </div>

        <div
          ref="recordButtonRef"
          class="flex items-center gap-x-[8px] record-btn"
          @touchstart.stop="handlePressStart"
          @mousedown.stop="handlePressStart"
          @touchend.stop.prevent="handlePressEnd"
          @mouseup.stop.prevent="handlePressEnd"
          @touchmove.stop.prevent="checkPosition"
          @mousemove.stop.prevent="checkPosition"
        >
          <div class="flex-1 h-full flex items-center justify-center">
            <span v-if="!isAliRealTimeRecording" class="pointer-events-none">
              长按发言</span
            >
            <WaveAnimation v-else class="h-[16px]" />
          </div>
        </div>
      </div>
      <div class="circle-icon" @click="openTranslateDialog">
        <img
          class="w-[24px] h-[24px]"
          :src="$g.tool.getFileUrl('aiEn/translate.png')"
        />
      </div>
    </div>
  </div>
  <SceneDialog v-model:show="showSceneDialog" @start-learn="handleStartLearn" />
  <TranslateDialog
    v-model:show="showTranslateDialog"
    :teach-me-info="teachMeInfo"
    :dialog-type="dialogType"
    :message-id="messageId"
    :conversationId="conversationId"
    @send-audio="handleSendAudio"
    @reset-dialog-type="dialogType = 'translate'"
  />
</template>

<script lang="ts" setup>
import { onClickOutside } from '@vueuse/core'
import { useAliASR } from './hooks/useAliAudio'
import { useAiSettingStore } from '@/stores/modules/aiEnSetting'

import WaveAnimation from './components/WaveAnimation.vue'
import MenuIcon from './components/MenuIcon.vue'
import SceneDialog from './components/SceneDialog.vue'
import TranslateDialog from './components/TranslateDialog.vue'

const props = withDefaults(
  defineProps<{
    isAiConnecting: boolean // 是否正在连接AI
    messageId?: number | string
  }>(),
  {
    isAiConnecting: false,
  },
)

const aiSettingStore = useAiSettingStore()
const emits = defineEmits(['sendAudio', 'startLearn'])

let mode = inject('chatMode') // 自由对话free、主题对话theme
let conversationId: any = inject('currentConversationId')
const recordButtonRef = ref<HTMLElement | null>(null)
const willCancel = ref(false)
const showMenu = ref(false)
const showSceneDialog = ref(false)
const showTranslateDialog = ref(false)
let dialogType = $ref('translate')
// 添加点击外部关闭菜单的功能
const menuRef = ref<HTMLElement | null>(null)
// 教我回答-去回答
let teachMeInfo = $ref<any>()
let teachMeHandler // 用于保存回调引用

// 长按标识
let longPressTimer: any = $ref(null)
let isLongPress = $ref(false)

const asrOptions = reactive({
  from: 'en',
  to: 'zh',
  needUpload: true,
  conversationId: null,
  messageId: null as any,
  isCancel: false,
})

const {
  deviceMicMsg,
  isAliRealTimeRecording,
  startAliRealTimeRecording,
  stopAliRealTimeRecording,
} = useAliASR(
  {
    onRecordComplete: async (data) => {
      await nextTick()
      // 处理录音完成，包含文本和音频URL
      if (data?.audioUrl && data?.sentences?.length) {
        emits('sendAudio', {
          convertType: 2,
          content: data?.sentences?.[0]?.fromContent,
          audio: data.audioUrl,
        })
      }
    },
    onerror: () => {
      $g.showToast('录音出错，请重试')
    },
  },
  asrOptions,
)

onClickOutside(menuRef, () => (showMenu.value = false))

const disbaledFlag = $computed(() => {
  return {
    playing: aiSettingStore.isVoicePlaying,
    connecting: props.isAiConnecting,
    all: aiSettingStore.isVoicePlaying || props.isAiConnecting,
  }
})

/**
 * 切换菜单显示
 */
function toggleMenu(event: Event) {
  event.stopPropagation() // 阻止事件冒泡

  if (disbaledFlag.all) {
    const msg = disbaledFlag.playing ? '音频正在播放中' : 'AI正在连接中'
    $g.showToast(msg + '，请稍后再试')
    return
  }
  showMenu.value = !showMenu.value
}

/**
 * 开始录音
 */
function handlePressStart(event) {
  if (event && event.cancelable) event.preventDefault()
  isLongPress = false
  longPressTimer = setTimeout(() => {
    isLongPress = true
    startRecord()
  }, 400) // 400ms为长按阈值，可根据体验调整
}
async function startRecord() {
  asrOptions.conversationId = conversationId.value
  asrOptions.messageId = props.messageId
  if (disbaledFlag.all) {
    const msg = disbaledFlag.playing ? '音频正在播放中' : 'AI正在连接中'
    $g.showToast(msg + '，请稍后再试')
    return
  }
  willCancel.value = false
  startAliRealTimeRecording()
}

/**
 * 检查位置
 */
function checkPosition(event: MouseEvent | TouchEvent) {
  if (!isAliRealTimeRecording.value || !recordButtonRef.value) return

  let clientX: number, clientY: number

  if ('touches' in event) {
    // 触摸事件
    clientX = event.touches[0]?.clientX
    clientY = event.touches[0]?.clientY
  } else {
    // 鼠标事件
    clientX = event.clientX
    clientY = event.clientY
  }

  // 获取坐标位置的元素
  let elementAtPoint
  if (clientX && clientY) {
    elementAtPoint = document.elementFromPoint(clientX, clientY)
  }

  // 判断是否在按钮内
  willCancel.value = elementAtPoint
    ? !recordButtonRef.value.contains(elementAtPoint)
    : true
}

/**
 * 结束录音
 */
function handlePressEnd() {
  clearTimeout(longPressTimer)
  if (isLongPress) {
    endRecord()
  } else {
    $g.showToast('请长按发言')
  }
}
async function endRecord() {
  if (disbaledFlag.all) return
  asrOptions.isCancel = willCancel.value
  willCancel.value = false

  if (asrOptions.isCancel) {
    $g.showToast('您什么都没说哦~')
  }
  stopAliRealTimeRecording()
}

/**
 * 情景对话打开弹窗
 */
function openSceneDialog() {
  showSceneDialog.value = true
  showMenu.value = false
}

function openTranslateDialog() {
  if (disbaledFlag.all) {
    const msg = disbaledFlag.playing ? '音频正在播放中' : 'AI正在连接中'
    $g.showToast(msg + '，请稍后再试')
    return
  }
  showTranslateDialog.value = true
}

function handleStartLearn() {
  emits('startLearn')
}

function handleSendAudio(data) {
  emits('sendAudio', data)
}

function handleVisibilityChange() {
  if (document.visibilityState === 'hidden') {
    // 停止录音，重置UI
    stopAliRealTimeRecording()
    willCancel.value = false
    isLongPress = false
  }
}

onMounted(() => {
  teachMeHandler = (msg) => {
    teachMeInfo = msg || {}
    if ($g.tool.isTrue(teachMeInfo)) {
      dialogType = 'teachMeAnswer'
      showTranslateDialog.value = true
    }
  }
  $g.bus.on('aiEn_teachMe', teachMeHandler)
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

// 组件卸载时清理
onUnmounted(() => {
  $g.bus.off('aiEn_teachMe', teachMeHandler)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  if (isAliRealTimeRecording.value) {
    stopAliRealTimeRecording()
  }
})
</script>

<style scoped>
.circle-icon {
  width: 44px;
  height: 44px;
  border-radius: 100%;
  background: #fff;
  box-shadow: 0px 0 6px 0px rgba(122, 133, 153, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}
.gradient-text {
  position: relative;
  font-weight: 500;
  display: inline-block;
}

.gradient-text::before {
  content: attr(data-text);
  position: absolute;
  inset: 0;
  background: linear-gradient(to right, #4a84ff, #8283ff);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
}
.record-btn {
  background: linear-gradient(to right, #4a84ff, #8283ff);
  position: relative;
  color: #fff;
  border-radius: 22px;
  height: 100%;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease-out;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.fade-in-enter-active,
.fade-in-leave-active {
  transition: all 0.2s ease-out;
}

.fade-in-enter-from,
.fade-in-leave-to {
  opacity: 0;
}
</style>
