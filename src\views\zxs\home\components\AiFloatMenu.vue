<template>
  <van-floating-bubble
    v-model:offset="offset"
    axis="xy"
    magnetic="x"
    :gap="0"
    teleport="#app"
    style="
      overflow: visible;
      opacity: 1 !important;
      --van-floating-bubble-size: fit-content;
      --van-floating-bubble-background: transparent;
    "
    @offset-change="handleOffsetChange"
  >
    <div
      ref="menuContainer"
      class="menu-container"
      :class="{ menus_checked: checked }"
    >
      <!-- 主菜单按钮 -->
      <div
        class="menu menu_main"
        @touchmove="handleTouchMove"
        @touchend="handleTouchMoveEnd"
        @click="
          () => {
            checked = !checked
            showTips = false
          }
        "
      >
        <img
          class="select-none pointer-events-none"
          :src="$g.tool.getFileUrl('home/AITools.png')"
        />
      </div>

      <!-- 子菜单按钮 -->
      <template v-for="(menu, index) in menus" :key="menu.id">
        <div
          class="menu menu_item"
          :style="{
            '--dir': dir,
            '--sin-x': getSinX(index),
            '--cos-y': getCosY(index),
          }"
          @touchmove.stop
          @click="handleMenuItemClick(menu)"
        >
          <img :src="menu.img" />
        </div>
      </template>

      <!-- 悬浮通知 -->
      <div v-if="showTips" class="ai-tips" :style="{ '--dir': dir }">
        <img
          class="ai-tips-bg"
          :style="{
            transform: `rotateY(${dir === 1 ? 180 : 0}deg)`,
          }"
          :src="$g.tool.getFileUrl('home/AITips.png')"
        />
        <div
          class="p-[5px_15px_20px_15px] pointer-events-auto cursor-pointer text-[white]"
          @click="handleTipsClick"
        >
          <div>HI，{{ userInfo.studentName }}</div>
          <div>AI老师为你整理了最近的学情， 点击进行查看</div>
        </div>
      </div>
    </div>
  </van-floating-bubble>
</template>
<script setup>
import { onClickOutside } from '@vueuse/core'
import { getStudentExamAnalysis } from '@/api/home'
import { getJZTKey } from '@/api/common'
import androidJump from '../androidJump'

const props = defineProps({
  userInfo: {
    type: Object,
    default: () => {},
  },
  platform: {
    type: String,
    default: '',
  },
})

const offset = $ref({
  x: undefined,
  y: undefined,
})
// 是否选中菜单
let checked = $ref(false)

// 是否显示提示信息
let showTips = $ref(false)

// 弹出方向  1向X轴正方向弹出，-1向X轴负方向弹出
let dir = $ref(-1)

const menuContainer = ref()
onClickOutside(menuContainer, () => (checked = false))

// 菜单列表
const menus = $ref([
  {
    id: 'AITeacher',
    img: $g.tool.getFileUrl('home/AITeacher.png'),
    url: `${
      import.meta.env.VITE_APP_THREE_LANDSCAPE_URL
    }/#/student/learningSituationAi`,
  },
  {
    id: 'AIVolunteer',
    img: $g.tool.getFileUrl('home/AIVolunteer.png'),
    url: '',
    flutter: 'launchInNewWebView',
  },
  {
    id: 'AIPhoto',
    img: $g.tool.getFileUrl('home/AIPhoto.png'),
    url: '',
    flutter: 'TO_PHOTOGRAPH_CAMERA',
  },
  {
    id: 'AICorrect',
    img: $g.tool.getFileUrl('home/AICorrect.png'),
    url: '',
    flutter: 'TO_CORRECT_CAMERA',
  },
  {
    id: 'AIFinger',
    img: $g.tool.getFileUrl('home/AIFinger.png'),
    url: '',
    flutter: 'TO_FINGER_CAMERA',
  },
  {
    id: 'AIRecord',
    img: $g.tool.getFileUrl('home/AIRecord.png'),
    url: `${import.meta.env.VITE_JZT_APP_URL}/#/jzt/fingerSearch/history`,
  },
])

/** 获取某个菜单的偏移角度的正弦值 */
function getSinX(index) {
  const rad = Math.PI / (menus.length - 1)
  return Math.sin(index * rad)
}
/** 获取某个菜单的偏移角度的余弦值 */
function getCosY(index) {
  const rad = Math.PI / (menus.length - 1)
  return Math.cos(index * rad)
}

function handleOffsetChange({ x, y }) {
  dir = x < 100 ? 1 : -1
  adjustBubblePosition()
}
//调整气泡悬浮的位置
function adjustBubblePosition() {
  let menuHeight = menuContainer.value.clientHeight
  let menuWidth = menuContainer.value.clientWidth
  // 底部距离
  if (
    offset.y > window.innerHeight - menuHeight * 2 ||
    offset.y === undefined
  ) {
    offset.y = window.innerHeight - menuHeight * 2
  }
  // 顶部距离
  if (offset.y < menuHeight) {
    offset.y = menuHeight
  }
  // 右边距离
  if (offset.x !== undefined && offset.x !== 0) {
    offset.x = window.innerWidth - menuWidth
  }
}

// 折叠屏展开折叠时触发此事件，且展开折叠过程有动画过渡
useEventListener(window, 'resize', useDebounceFn(adjustBubblePosition, 300))

async function fetchStudentExamAnalysis() {
  try {
    let res = await getStudentExamAnalysis()
    showTips = res
  } catch (err) {
    console.log('获取拆卷考试情况出错', err)
  }
}
/**
 * 菜单项点击
 */
async function handleMenuItemClick({ id, url, flutter }) {
  showTips = false
  if (flutter) {
    const env = import.meta.env.VITE_APP_ENV === 'production'
    const url = env
      ? `https://qiming-volunteer.qimingdaren.com/#/career/ai-chat?user_id=${props.userInfo.idNum}&username=${props.userInfo.studentName}`
      : `https://test-qiming-volunteer.qimingdaren.com/#/career/ai-chat?user_id=12&username=${props.userInfo.studentName}`
    let params = {
      url,
    }
    if (props.platform == 'QMYX') {
      params.autoPrefix = false
    }

    $g.flutter(flutter, flutter == 'launchInNewWebView' ? params : {})
    return
  }

  // 跳转三端项目
  if (id === 'AITeacher') {
    androidJump({
      data: {
        redirect_url: url,
        landscape: true,
      },
      tokenName: 'jztToken',
    })
  }
  // 跳转金字塔项目页面
  if (id === 'AIRecord') {
    if (!$g.inApp) {
      let key = await getJZTKey()
      window.open(`${url}?encryptedStr=${key}`)
    } else {
      androidJump({
        data: { redirect_url: `${url}` },
      })
    }
  }
}

/** 提示信息点击 */
function handleTipsClick() {
  showTips = false
  androidJump({
    data: {
      redirect_url: `${
        import.meta.env.VITE_APP_THREE_LANDSCAPE_URL
      }/#/student/learningSituationAi`,
      landscape: true,
    },
    tokenName: 'jztToken',
  })
}

// 拖动的时候，隐藏提示信息和菜单
let checkedCopy = undefined
let showTipsCopy = undefined
function handleTouchMove() {
  if (checkedCopy === undefined && showTipsCopy === undefined) {
    checkedCopy = checked
    showTipsCopy = showTips
  }
  checked = false
  showTips = false
}
function handleTouchMoveEnd() {
  if (checkedCopy !== undefined && showTipsCopy !== undefined) {
    checked = checkedCopy
    showTips = showTipsCopy
  }
  checkedCopy = showTipsCopy = undefined
}

onMounted(() => {
  fetchStudentExamAnalysis()
  adjustBubblePosition()
})
</script>

<style scoped>
.menu-container {
  position: relative;
  width: 72px;
  height: 72px;
}

.menu-container .menu_main {
  z-index: 2;
  width: 100%;
  height: 100%;
  /* filter: drop-shadow(0 0 3px #4d82f2); */
}

/* 点击悬浮球主菜单按钮后样式 */
.menus_checked .menu_main {
  /* background-color: #4d82f2; */
}

.menu {
  display: block;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  will-change: transform;
  transition-timing-function: cubic-bezier(0.55, 0.42, 0.16, 0.86);
  transition-duration: 200ms;
}

.menu_item {
  width: 47px;
  height: 47px;
  /* filter: drop-shadow(0 0 3px #759cf0); */
  transform: translate(-50%, -50%);
}

.menus_checked .menu_item {
  transform: translate(
    calc(-50% + calc(var(--sin-x) * 75px * var(--dir))),
    calc(-50% - calc(var(--cos-y) * 75px))
  );
}

.ai-tips {
  font-size: 11px;
  line-height: 14px;
  color: #333333;
  position: absolute;
  z-index: 3;
  white-space: nowrap;
  top: 0;
  left: 50%;
  transform: translate(calc(-50% + calc(50%) * var(--dir)), calc(-100% + 10px));
  pointer-events: none;
}

.ai-tips-bg {
  position: absolute;
  z-index: -1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
</style>
