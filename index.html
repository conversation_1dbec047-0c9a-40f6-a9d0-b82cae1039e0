<!doctype html>
<html lang="en">
  <head>
    <script>
      window._time = new Date().getTime()
    </script>
    <meta charset="UTF-8" />
    <meta name="description" content="2c5ade56282fc2e55fb219ab11b369f9" />
    <link rel="icon" href="/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"
    />

    <meta
      name="format-detection"
      content="telephone=no, email=no, date=no, address=no"
    />

    <title>智习室APP</title>

    <!-- remixicon地址 -->
    <link
      href="https://frontend-cdn.qimingdaren.com/ct/remixicon/remixicon.css"
      rel="stylesheet"
    />
    <!-- vMdEditor所需资源start -->
    <link
      href="//frontend-cdn.qimingdaren.com/cdn/jquery/katex-v3/katex.min.css"
      rel="stylesheet"
    />
    <!-- vMdEditor所需资源end -->

    <link rel="stylesheet" href="/static/css/loading.css" />
    <link rel="stylesheet" href="/static/css/error.css" />
    <!-- 检测错误提示，放到顶部，捕获页面中后续js加载出现的错误 -->
    <script src="/js/error.js"></script>
  </head>

  <body>
    <!-- 页面加载初始loading -->
    <div
      class="app-loading"
      id="appLoadingId"
      style="background-color: white; display: none"
    >
      <div class="app-loading-wrap">
        <img src="/static/img/logo.png" class="app-loading-logo" alt="Logo" />
        <div class="app-loading-dots" style="height: 82px">
          <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
        </div>
      </div>
    </div>

    <!-- 顶部安全距离适配 -->
    <div
      id="TopAdaptationDistance"
      style="position: relative; z-index: 99"
    ></div>

    <!-- 页面挂载点 -->
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>

    <!-- mathjax初始配置 -->
    <script src="/js/mathjax.js"></script>
    <script
      src="https://frontend-store.oss-cn-chengdu.aliyuncs.com/cdn/mathjax/tex-chtml.js"
      id="MathJax-script"
      async
    ></script>
    <!-- vMdEditor所需资源start -->
    <script src="//frontend-cdn.qimingdaren.com/cdn/jquery/katex-v3/katex.min.js"></script>
    <!-- <script src="//frontend-cdn.qimingdaren.com/cdn/jquery/mermaid.min.js"></script> -->
    <!-- vMdEditor所需资源end -->
    <script>
      // 延迟1秒判断#app是否有内容
      setTimeout(function () {
        // 检查#app元素是否为空
        if (!document.getElementById('app').innerHTML.trim()) {
          let loadingElement = document.getElementById('appLoadingId')
          if (loadingElement) loadingElement.style.display = 'block'
        }
      }, 300)
    </script>
  </body>
</html>
