import { computed, ref, onUnmounted } from 'vue'
import type { Ref } from 'vue'
import { useTTSWebSocket } from './useTTSWebSocket'
import type { ISegmentCallbacks } from './useTTSWebSocket'
import { useAiSettingStore } from '@/stores/modules/aiEnSetting'
import { useAudioAssembler } from './useAudioAssembler'
import type { IAudioAssemblerOptions } from './useAudioAssembler'

// 引入新的任务级TTS WebSocket（这里会在函数内部重新初始化）

/** 流式输出任务配置 */
export interface IStreamingTaskOptions {
  /** 是否自动切换到此任务（停止其他播放） */
  autoSwitch?: boolean
  /** 文本块大小 */
  chunkSize?: number
  /** 输出间隔（毫秒） */
  delay?: number
  /** 是否自动播放TTS */
  autoPlay?: boolean
}

/** 流式输出任务实例 */
export interface IStreamingTask {
  /** 任务ID */
  taskId: string
  /** 是否正在流式输出 */
  isStreaming: Ref<boolean>
  /** 输出进度（0-100） */
  progress: Ref<number>
  /** 当前累积文本 */
  currentText: Ref<string>
  /** 添加文本块 */
  addText: (text: string) => void
  /** 完成流式输出 */
  finish: () => void
  /** 取消流式输出 */
  cancel: () => void
}

export interface IStreamTTSPlayerOptions {
  /** 是否自动根据标点符号切割 */
  autoSegment?: boolean
  /** 切割触发符号 */
  segmentTriggers?: string[]
  /** 文本缓冲区大小（字符数） */
  bufferSize?: number
  /** 任务开始回调 */
  onTaskStart?: (taskId: string) => void
  /** 段落就绪回调 */
  onSegmentReady?: (segment: string, index: number, taskId: string) => void
  /** 播放开始回调 */
  onPlayStart?: (index: number, taskId: string) => void
  /** 播放结束回调 */
  onPlayEnd?: (index: number, taskId: string) => void
  /** 转换完成回调 */
  onConvertComplete?: (index: number, success: boolean, taskId: string) => void
  /** 任务完成回调 */
  onTaskComplete?: (taskId: string, summary: ITaskSummary) => void
  /** 任务播放状态变化回调 */
  onTaskPlayStateChange?: (taskId: string, isPlaying: boolean) => void
  /** 音频组装配置 */
  audioAssembler?: IAudioAssemblerOptions
  /** 调试日志开关 */
  showDebugLog?: boolean
}

export interface ITaskSummary {
  /** 任务ID */
  taskId: string
  /** 总段落数 */
  totalSegments: number
  /** 播放完成的段落数 */
  completedSegments: number
  /** 总播放时长（估算） */
  totalDuration: number
  /** 是否成功完成 */
  success: boolean
}

export interface IStreamSegment {
  /** 段落索引 */
  index: number
  /** 文本内容 */
  text: string
  /** 所属任务ID */
  taskId: string
  /** 转换状态 */
  status:
    | 'pending'
    | 'converting'
    | 'ready'
    | 'playing'
    | 'completed'
    | 'failed'
  /** 音频URL */
  audioUrl?: string
  /** 音频大小 */
  audioSize?: number
}

export interface ITaskInfo {
  /** 任务ID */
  id: string
  /** 任务状态 */
  status: 'inputting' | 'processing' | 'completed' | 'cancelled'
  /** 开始时间 */
  startTime: number
  /** 结束时间 */
  endTime?: number
  /** 段落列表 */
  segments: IStreamSegment[]
  /** 是否已完成输入 */
  inputFinished: boolean
  /** TTS任务实例 */
  ttsTask?: any
  /** 总段落数（输入完成后确定） */
  totalSegments?: number
  /** 已播放完成的段落数 */
  completedPlayCount: number
}

export function useStreamTTSPlayer(options: IStreamTTSPlayerOptions = {}) {
  const {
    autoSegment = true,
    segmentTriggers = ['。', '！', '？', '!', '?', '.', '\n', ',', '，'],
    bufferSize = 45,
    onTaskStart,
    onSegmentReady,
    onPlayStart,
    onPlayEnd,
    onConvertComplete,
    onTaskComplete,
    onTaskPlayStateChange,
    audioAssembler,
    showDebugLog = false,
  } = options

  // 初始化带有调试日志开关的TTS WebSocket客户端
  const ttsClient = useTTSWebSocket(showDebugLog)

  // Store
  const aiSettingStore = useAiSettingStore()

  // 音频组装器
  const audioAssemblerInstance = audioAssembler
    ? useAudioAssembler({ ...audioAssembler, showDebugLog })
    : null

  // 状态管理
  const tasks = new Map<string, ITaskInfo>()
  const taskBuffers = new Map<string, string>()
  const timeoutTimers = new Map<string, number>()
  let currentPlayingTask: string | null = null
  let currentPlayingIndex = -1
  let currentAudio: HTMLAudioElement | null = null // 当前播放的音频元素
  let nextSegmentIndex = 0
  let debugLogs: string[] = []

  // 流式输出状态管理
  const streamingTasks = new Map<
    string,
    {
      isStreaming: Ref<boolean>
      progress: Ref<number>
      currentText: Ref<string>
      totalText: string
      currentIndex: number
      options: Required<IStreamingTaskOptions>
      timer?: number
    }
  >()

  /** 添加调试日志 */
  function addDebugLog(message: string): void {
    const timestamp = new Date().toLocaleTimeString()
    debugLogs.push(`[${timestamp}] ${message}`)
    if (import.meta.env.VITE_APP_ENV !== 'production' && showDebugLog) {
      console.log(`🔊 TTS: ${message}`)
    }
  }

  /** 开始任务 */
  function startTask(taskId: string): void {
    addDebugLog(`开始任务: ${taskId}`)

    // 如果任务已存在，先清理
    if (tasks.has(taskId)) {
      addDebugLog(`任务 ${taskId} 已存在，先清理旧任务`)
      cancelTask(taskId)
    }

    // 创建TTS任务连接
    const ttsTask = ttsClient.createTask(taskId)

    const taskInfo: ITaskInfo = {
      id: taskId,
      status: 'inputting',
      startTime: Date.now(),
      segments: [],
      inputFinished: false,
      ttsTask: ttsTask,
      completedPlayCount: 0,
    }

    tasks.set(taskId, taskInfo)
    taskBuffers.set(taskId, '')

    addDebugLog(`任务 ${taskId} 初始化完成`)
    onTaskStart?.(taskId)
  }

  /** 为任务添加文本 */
  function addTextToTask(taskId: string, text: string): void {
    if (!text.trim()) return

    const currentBuffer = taskBuffers.get(taskId) || ''
    const newBuffer = currentBuffer + text
    taskBuffers.set(taskId, newBuffer)

    addDebugLog(
      `任务 ${taskId} 添加文本: "${text}" (缓冲区: ${newBuffer.length}字符)`,
    )

    // 清除之前的定时器
    const existingTimer = timeoutTimers.get(taskId)
    if (existingTimer) {
      clearTimeout(existingTimer)
    }

    // 检查是否需要切割
    if (autoSegment) {
      checkAndSegmentTask(taskId)
    }

    // 设置新的超时定时器
    startTimeoutTimer(taskId)
  }

  /** 启动超时定时器 */
  function startTimeoutTimer(taskId: string): void {
    const timer = window.setTimeout(() => {
      const buffer = taskBuffers.get(taskId)?.trim()
      if (buffer) {
        addDebugLog(`任务 ${taskId} 超时切割 (3秒无新内容)`)
        forceSegmentTask(taskId)
      }
    }, 3000)

    timeoutTimers.set(taskId, timer)
  }

  /** 检查并切割任务 */
  function checkAndSegmentTask(taskId: string): void {
    const textBuffer = taskBuffers.get(taskId) || ''
    if (!textBuffer.trim()) return

    let hasSegmented = false

    // 1. 检查是否包含强切割触发符（句号、问号等）
    for (const trigger of segmentTriggers) {
      const triggerIndex = textBuffer.indexOf(trigger)
      if (triggerIndex !== -1) {
        const segmentText = textBuffer.substring(0, triggerIndex + 1).trim()
        if (segmentText) {
          createSegmentForTask(taskId, segmentText)
          const remainingText = textBuffer.substring(triggerIndex + 1)
          taskBuffers.set(taskId, remainingText)
          addDebugLog(
            `任务 ${taskId} 强切割触发: "${segmentText}" (${segmentText.length}字符)`,
          )
          hasSegmented = true
        }
        break
      }
    }

    if (hasSegmented) return

    // 2. 中等长度时，检查弱切割符（分号等）
    const mediumThreshold = Math.floor(bufferSize * 0.6) // 48字符
    if (textBuffer.length >= mediumThreshold) {
      const weakTriggers = [';', '；']

      for (const trigger of weakTriggers) {
        const triggerIndex = textBuffer.indexOf(trigger)
        if (triggerIndex !== -1 && triggerIndex >= 20) {
          // 至少20字符才切割
          const segmentText = textBuffer.substring(0, triggerIndex + 1).trim()
          if (segmentText) {
            createSegmentForTask(taskId, segmentText)
            const remainingText = textBuffer.substring(triggerIndex + 1)
            taskBuffers.set(taskId, remainingText)
            addDebugLog(
              `任务 ${taskId} 中等长度弱切割: "${segmentText}" (${segmentText.length}字符)`,
            )
            hasSegmented = true
          }
          break
        }
      }
    }

    if (hasSegmented) return

    // 3. 检查缓冲区是否超出大小限制，进行智能切割
    if (textBuffer.length >= bufferSize) {
      let cutIndex = textBuffer.length

      // 优先在合适的英文词汇连接处切割
      const goodCutPoints = [
        ' and ',
        ' or ',
        ' but ',
        ' with ',
        ' that ',
        ' which ',
        ' when ',
        ' where ',
        ' how ',
        '; ',
      ]
      let bestCutIndex = -1

      for (const cutPoint of goodCutPoints) {
        const index = textBuffer.lastIndexOf(cutPoint, bufferSize)
        if (index > bestCutIndex && index >= bufferSize * 0.5) {
          bestCutIndex = index + cutPoint.length
        }
      }

      if (bestCutIndex > 0) {
        cutIndex = bestCutIndex
      } else {
        // 如果没有找到合适的切割点，在空格处切割
        for (let i = bufferSize - 1; i >= bufferSize * 0.7; i--) {
          const char = textBuffer[i]
          if (char === ' ') {
            cutIndex = i + 1
            break
          }
        }
      }

      if (cutIndex > 0 && cutIndex < textBuffer.length) {
        const segmentText = textBuffer.substring(0, cutIndex).trim()
        if (segmentText) {
          createSegmentForTask(taskId, segmentText)
          const remainingText = textBuffer.substring(cutIndex)
          taskBuffers.set(taskId, remainingText)
          addDebugLog(
            `任务 ${taskId} 缓冲区智能切割: "${segmentText}" (${segmentText.length}字符)`,
          )
        }
      }
    }
  }

  /** 过滤TTS不支持的字符 */
  function filterTTSText(text: string): string {
    return (
      text
        // 过滤emoji表情
        .replace(
          /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu,
          '',
        )
        // 过滤其他特殊符号
        .replace(/[🎉🎊🔥✨💡⭐]/g, '')
        // 清理多余空格
        .replace(/\s+/g, ' ')
        .trim()
    )
  }

  /** 为任务创建段落 */
  function createSegmentForTask(taskId: string, text: string): void {
    const taskInfo = tasks.get(taskId)
    if (!taskInfo) return

    // 过滤TTS不支持的字符
    const filteredText = filterTTSText(text)

    // 如果过滤后文本为空，跳过创建
    if (!filteredText) {
      addDebugLog(`任务 ${taskId} 文本过滤后为空，跳过创建: "${text}"`)
      return
    }

    // 避免重复创建相同内容的段落
    const existingSegment = taskInfo.segments.find(
      (seg) => seg.text === filteredText,
    )
    if (existingSegment) {
      addDebugLog(`任务 ${taskId} 段落 "${filteredText}" 已存在，跳过创建`)
      return
    }

    const segment: IStreamSegment = {
      index: nextSegmentIndex++,
      text: filteredText,
      taskId,
      status: 'pending',
    }

    taskInfo.segments.push(segment)
    addDebugLog(
      `为任务 ${taskId} 创建段落 ${segment.index}: "${filteredText}"${text !== filteredText ? ` (原文: "${text}")` : ''}`,
    )
    onSegmentReady?.(filteredText, segment.index, taskId)

    // 立即发送段落到TTS任务
    sendSegmentToTTS(segment)
  }

  /** 发送段落到TTS */
  async function sendSegmentToTTS(segment: IStreamSegment): Promise<void> {
    const taskInfo = tasks.get(segment.taskId)

    addDebugLog(
      `🔍 检查任务信息: taskId=${segment.taskId}, taskInfo存在=${!!taskInfo}, ttsTask存在=${!!taskInfo?.ttsTask}`,
    )

    if (!taskInfo) {
      addDebugLog(`❌ 任务 ${segment.taskId} 不存在`)
      return
    }

    if (!taskInfo.ttsTask) {
      addDebugLog(`❌ 任务 ${segment.taskId} 的ttsTask不存在`)
      return
    }

    segment.status = 'converting'
    addDebugLog(`开始转换段落 ${segment.index}: "${segment.text}"`)

    // 获取语音速度配置
    const speed = aiSettingStore.config.voiceSpeed
      ? Number(aiSettingStore.config.voiceSpeed)
      : 1.0

    addDebugLog(
      `🎵 准备发送段落到WebSocket: index=${segment.index}, speed=${speed}, text长度=${segment.text.length}`,
    )

    const callbacks: ISegmentCallbacks = {
      onStart: () => {
        addDebugLog(`段落 ${segment.index} 开始转换`)
      },
      onAudioReady: (audioUrl) => {
        segment.audioUrl = audioUrl
        segment.status = 'ready'
        addDebugLog(`段落 ${segment.index} 音频就绪`)

        // 音频准备好后直接尝试播放，不检查voiceAutoPlayStatus
        // voiceAutoPlayStatus只影响任务是否创建，不影响音频准备好后的播放
        checkAndStartNextSegment(segment.taskId)
      },
      onComplete: () => {
        // 转换完成但还未播放，不要标记为completed
        addDebugLog(`段落 ${segment.index} 转换完成`)
        onConvertComplete?.(segment.index, true, segment.taskId)

        // 检查任务是否完成（这里不检查，只在播放完成时检查）
        // checkTaskCompletion(segment.taskId)
      },
      onError: (error) => {
        segment.status = 'failed'
        addDebugLog(`段落 ${segment.index} 转换失败: ${error}`)
        onConvertComplete?.(segment.index, false, segment.taskId)

        // 转换失败也视为已处理，增加完成计数
        const taskInfo = tasks.get(segment.taskId)
        if (taskInfo) {
          taskInfo.completedPlayCount++
          addDebugLog(
            `任务 ${segment.taskId} 失败段落计数: ${taskInfo.completedPlayCount}/${taskInfo.totalSegments || '未知'}`,
          )

          // 检查任务是否完成
          if (
            taskInfo.totalSegments &&
            taskInfo.completedPlayCount >= taskInfo.totalSegments
          ) {
            taskInfo.status = 'completed'
            taskInfo.endTime = Date.now()

            const summary: ITaskSummary = {
              taskId: segment.taskId,
              totalSegments: taskInfo.totalSegments,
              completedSegments: taskInfo.completedPlayCount,
              totalDuration: taskInfo.endTime - taskInfo.startTime,
              success: taskInfo.completedPlayCount > 0,
            }

            addDebugLog(`🎉 任务 ${segment.taskId} 完成（含失败段落）！`)
            onTaskPlayStateChange?.(segment.taskId, false)
            onTaskComplete?.(segment.taskId, summary)

            // 触发音频组装
            if (audioAssemblerInstance) {
              addDebugLog(`🎵 触发音频组装: ${segment.taskId}`)
              audioAssemblerInstance.startAssembleTask(segment.taskId, taskInfo)
            }
          }
        }
      },
    }

    try {
      addDebugLog(`🚀 调用 ttsTask.addSegment...`)
      await taskInfo.ttsTask.addSegment(segment.text, callbacks, speed)
      addDebugLog(`✅ ttsTask.addSegment 调用成功`)
    } catch (error) {
      segment.status = 'failed'
      addDebugLog(`❌ 段落 ${segment.index} 发送失败: ${error}`)
      onConvertComplete?.(segment.index, false, segment.taskId)
    }
  }

  /** 检查并开始播放下一个段落 */
  function checkAndStartNextSegment(taskId: string): void {
    const taskInfo = tasks.get(taskId)
    if (!taskInfo) {
      addDebugLog(`任务 ${taskId} 不存在，无法检查播放`)
      return
    }

    // 🔒 关键检查：如果全局有任何播放状态，都不能开始新的播放
    if (currentPlayingTask !== null) {
      addDebugLog(
        `❌ 全局播放锁定：任务 ${currentPlayingTask} 正在播放，任务 ${taskId} 等待`,
      )
      return
    }

    // 检查是否有任何段落处于playing状态（双重保险）
    const globalPlayingSegment = Array.from(tasks.values())
      .flatMap((task) => task.segments)
      .find((seg) => seg.status === 'playing')

    if (globalPlayingSegment) {
      addDebugLog(
        `❌ 发现播放中段落 ${globalPlayingSegment.index}，任务 ${taskId} 等待`,
      )
      return
    }

    // 找到应该播放的下一个段落（按索引顺序）
    const sortedSegments = taskInfo.segments.sort((a, b) => a.index - b.index)

    // 找到最后一个已完成的段落索引
    let lastCompletedIndex = -1
    for (const seg of sortedSegments) {
      if (seg.status === 'completed') {
        lastCompletedIndex = seg.index
      } else {
        break
      }
    }

    // 找到下一个应该播放的段落
    const nextSegment = sortedSegments.find(
      (seg) => seg.index === lastCompletedIndex + 1 && seg.status === 'ready',
    )

    if (nextSegment) {
      addDebugLog(`🎵 找到下一个播放段落: ${nextSegment.index}，开始播放`)
      playSegment(nextSegment)
    } else {
      // 检查是否有跳过的ready段落（可能是第一个段落）
      const firstReadySegment = sortedSegments.find(
        (seg) => seg.status === 'ready',
      )
      if (firstReadySegment && lastCompletedIndex === -1) {
        addDebugLog(`🎵 开始播放第一个ready段落: ${firstReadySegment.index}`)
        playSegment(firstReadySegment)
      } else {
        addDebugLog(
          `任务 ${taskId} 暂无可播放段落 (lastCompleted: ${lastCompletedIndex})`,
        )
      }
    }
  }

  /** 播放段落 */
  function playSegment(segment: IStreamSegment): void {
    if (segment.status !== 'ready' || !segment.audioUrl) {
      addDebugLog(`段落 ${segment.index} 无法播放 (状态: ${segment.status})`)
      return
    }

    // 停止当前播放
    if (currentPlayingTask && currentPlayingTask !== segment.taskId) {
      stopCurrentPlayback()
    }

    segment.status = 'playing'
    currentPlayingTask = segment.taskId
    currentPlayingIndex = segment.index

    addDebugLog(`开始播放段落 ${segment.index}`)
    onPlayStart?.(segment.index, segment.taskId)
    onTaskPlayStateChange?.(segment.taskId, true)

    // 创建音频元素播放
    const audio = new Audio(segment.audioUrl)
    currentAudio = audio // 保存当前音频元素引用

    // 设置播放倍数
    const playbackRate = Number(aiSettingStore.config.voiceSpeed) || 1.0
    audio.playbackRate = playbackRate
    addDebugLog(`🎵 设置播放倍数: ${playbackRate}`)

    audio.play()

    audio.onended = () => {
      segment.status = 'completed'
      addDebugLog(`段落 ${segment.index} 播放完成`)
      onPlayEnd?.(segment.index, segment.taskId)

      // 重置当前播放状态
      currentPlayingTask = null
      currentPlayingIndex = -1
      currentAudio = null

      // 检查是否需要播放下一个段落
      const taskInfo = tasks.get(segment.taskId)
      if (taskInfo) {
        addDebugLog(`🔍 查找下一个段落，当前段落index: ${segment.index}`)
        addDebugLog(
          `📋 任务段落状态: ${taskInfo.segments.map((seg) => `[${seg.index}:${seg.status}]`).join(', ')}`,
        )

        // 更新任务的已播放完成计数
        taskInfo.completedPlayCount++
        addDebugLog(
          `任务 ${segment.taskId} 已播放完成 ${taskInfo.completedPlayCount} 个段落`,
        )

        // 使用统一的播放控制逻辑
        checkAndStartNextSegment(segment.taskId)

        // 用计数方式判断任务是否完成
        if (
          taskInfo.totalSegments &&
          taskInfo.completedPlayCount >= taskInfo.totalSegments
        ) {
          // 任务播放完成，设置状态并调用完成回调
          taskInfo.status = 'completed'
          taskInfo.endTime = Date.now()

          const summary: ITaskSummary = {
            taskId: segment.taskId,
            totalSegments: taskInfo.totalSegments,
            completedSegments: taskInfo.completedPlayCount,
            totalDuration: taskInfo.endTime - taskInfo.startTime,
            success: taskInfo.completedPlayCount > 0,
          }

          addDebugLog(
            `🎉 任务 ${segment.taskId} 播放完成！(${taskInfo.completedPlayCount}/${taskInfo.totalSegments})`,
          )
          onTaskPlayStateChange?.(segment.taskId, false)
          onTaskComplete?.(segment.taskId, summary)

          // 触发音频组装
          if (audioAssemblerInstance) {
            addDebugLog(`🎵 触发音频组装: ${segment.taskId}`)
            audioAssemblerInstance.startAssembleTask(segment.taskId, taskInfo)
          }
        } else {
          const totalInfo = taskInfo.totalSegments
            ? `/${taskInfo.totalSegments}`
            : '/未知'
          addDebugLog(
            `任务 ${segment.taskId} 播放进度: ${taskInfo.completedPlayCount}${totalInfo}`,
          )
        }
      }
    }

    audio.onerror = () => {
      segment.status = 'failed'
      addDebugLog(`段落 ${segment.index} 播放失败`)
      currentPlayingTask = null
      currentPlayingIndex = -1
      currentAudio = null
      onTaskPlayStateChange?.(segment.taskId, false)
    }
  }

  /** 停止当前播放 */
  function stopCurrentPlayback(): void {
    if (currentPlayingTask) {
      addDebugLog(`🛑 立即停止当前播放任务: ${currentPlayingTask}`)

      // 立即停止音频播放
      if (currentAudio) {
        addDebugLog(`🔇 强制停止音频播放`)
        currentAudio.pause()
        currentAudio.currentTime = 0
        currentAudio = null
      }

      // 将正在播放的段落状态重置为ready，以便重新播放
      const currentTask = tasks.get(currentPlayingTask)
      if (currentTask && currentPlayingIndex >= 0) {
        const playingSegment = currentTask.segments.find(
          (seg) =>
            seg.index === currentPlayingIndex && seg.status === 'playing',
        )
        if (playingSegment) {
          playingSegment.status = 'ready'
          addDebugLog(`🔄 段落 ${playingSegment.index} 状态重置为ready`)
        }
      }

      currentPlayingTask = null
      currentPlayingIndex = -1
    }
  }

  /** 完成任务输入 */
  function finishTask(taskId: string): void {
    const taskInfo = tasks.get(taskId)
    if (!taskInfo) {
      addDebugLog(`任务 ${taskId} 不存在，无法完成`)
      return
    }

    // 清理时间阈值定时器
    const timer = timeoutTimers.get(taskId)
    if (timer) {
      clearTimeout(timer)
      timeoutTimers.delete(taskId)
    }

    // 处理剩余缓冲区文本 - 应用切割策略
    const remainingText = taskBuffers.get(taskId)?.trim()
    if (remainingText) {
      addDebugLog(
        `处理剩余文本: "${remainingText}" (${remainingText.length}字符)`,
      )

      // 将剩余文本重新放入缓冲区，然后强制切割处理
      taskBuffers.set(taskId, remainingText)
      forceSegmentRemainingText(taskId)
      taskBuffers.set(taskId, '')
    }

    taskInfo.inputFinished = true
    taskInfo.status = 'processing'
    // 设置总段落数
    taskInfo.totalSegments = taskInfo.segments.length
    addDebugLog(`完成任务 ${taskId} 输入，总段落数: ${taskInfo.totalSegments}`)

    // 完成TTS任务
    if (taskInfo.ttsTask) {
      taskInfo.ttsTask.completeTask()
    }
  }

  /** 强制切割剩余文本 */
  function forceSegmentRemainingText(taskId: string): void {
    const textBuffer = taskBuffers.get(taskId) || ''
    if (!textBuffer.trim()) return

    addDebugLog(`开始强制切割剩余文本: ${textBuffer.length}字符`)

    // 递归切割长文本
    let remainingBuffer = textBuffer
    let segmentCount = 0

    while (remainingBuffer.trim() && segmentCount < 10) {
      // 最多切割10次，防止无限循环
      const initialLength = remainingBuffer.length

      // 临时设置缓冲区并尝试切割
      taskBuffers.set(taskId, remainingBuffer)

      let hasSegmented = false

      // 1. 检查强切割符
      for (const trigger of segmentTriggers) {
        const triggerIndex = remainingBuffer.indexOf(trigger)
        if (triggerIndex !== -1) {
          const segmentText = remainingBuffer
            .substring(0, triggerIndex + 1)
            .trim()
          if (segmentText) {
            createSegmentForTask(taskId, segmentText)
            remainingBuffer = remainingBuffer.substring(triggerIndex + 1).trim()
            addDebugLog(
              `强制切割-强触发: "${segmentText}" (${segmentText.length}字符)`,
            )
            hasSegmented = true
          }
          break
        }
      }

      if (hasSegmented) {
        segmentCount++
        continue
      }

      // 2. 检查弱切割符（中等长度）
      const mediumThreshold = Math.floor(bufferSize * 0.6)
      if (remainingBuffer.length >= mediumThreshold) {
        const weakTriggers = [';', '；']

        for (const trigger of weakTriggers) {
          const triggerIndex = remainingBuffer.indexOf(trigger)
          if (triggerIndex !== -1 && triggerIndex >= 20) {
            const segmentText = remainingBuffer
              .substring(0, triggerIndex + 1)
              .trim()
            if (segmentText) {
              createSegmentForTask(taskId, segmentText)
              remainingBuffer = remainingBuffer
                .substring(triggerIndex + 1)
                .trim()
              addDebugLog(
                `强制切割-弱触发: "${segmentText}" (${segmentText.length}字符)`,
              )
              hasSegmented = true
            }
            break
          }
        }
      }

      if (hasSegmented) {
        segmentCount++
        continue
      }

      // 3. 智能缓冲区切割
      if (remainingBuffer.length >= bufferSize) {
        let cutIndex = -1

        // 优先在合适的英文词汇连接处切割
        const goodCutPoints = [
          ' and ',
          ' or ',
          ' but ',
          ' with ',
          ' that ',
          ' which ',
          ' when ',
          ' where ',
          ' how ',
          '; ',
        ]

        for (const cutPoint of goodCutPoints) {
          const index = remainingBuffer.lastIndexOf(cutPoint, bufferSize)
          if (index > cutIndex && index >= bufferSize * 0.5) {
            cutIndex = index + cutPoint.length
          }
        }

        if (cutIndex <= 0) {
          // 如果没有找到合适的切割点，在空格处切割
          for (let i = bufferSize - 1; i >= bufferSize * 0.7; i--) {
            if (remainingBuffer[i] === ' ') {
              cutIndex = i + 1
              break
            }
          }
        }

        if (cutIndex > 0 && cutIndex < remainingBuffer.length) {
          const segmentText = remainingBuffer.substring(0, cutIndex).trim()
          if (segmentText) {
            createSegmentForTask(taskId, segmentText)
            remainingBuffer = remainingBuffer.substring(cutIndex).trim()
            addDebugLog(
              `强制切割-智能切割: "${segmentText}" (${segmentText.length}字符)`,
            )
            hasSegmented = true
          }
        }
      }

      if (hasSegmented) {
        segmentCount++
        continue
      }

      // 4. 如果文本还很长但无法切割，强制在中间切割
      if (remainingBuffer.length > bufferSize) {
        const cutIndex = Math.floor(bufferSize * 0.8)
        const segmentText = remainingBuffer.substring(0, cutIndex).trim()
        if (segmentText) {
          createSegmentForTask(taskId, segmentText)
          remainingBuffer = remainingBuffer.substring(cutIndex).trim()
          addDebugLog(
            `强制切割-硬切割: "${segmentText}" (${segmentText.length}字符)`,
          )
          segmentCount++
          continue
        }
      }

      // 如果没有切割且长度没变化，直接处理剩余部分
      if (remainingBuffer.length === initialLength) {
        if (remainingBuffer.trim()) {
          createSegmentForTask(taskId, remainingBuffer.trim())
          addDebugLog(
            `强制切割-最终段落: "${remainingBuffer.trim()}" (${remainingBuffer.trim().length}字符)`,
          )
        }
        break
      }
    }

    addDebugLog(`强制切割完成，共创建 ${segmentCount} 个额外段落`)
  }

  /** 取消任务 */
  function cancelTask(taskId: string): void {
    const taskInfo = tasks.get(taskId)
    if (!taskInfo) return

    addDebugLog(`取消任务 ${taskId}`)

    // 清理定时器
    const timer = timeoutTimers.get(taskId)
    if (timer) {
      clearTimeout(timer)
      timeoutTimers.delete(taskId)
    }

    // 如果这是当前播放的任务，停止播放
    if (currentPlayingTask === taskId) {
      stopCurrentPlayback()
      onTaskPlayStateChange?.(taskId, false)
    }

    // 取消TTS任务
    if (taskInfo.ttsTask) {
      taskInfo.ttsTask.cancelTask()
    }

    // 清理音频URL
    taskInfo.segments.forEach((segment) => {
      if (segment.audioUrl) {
        ttsClient.revokeAudioUrl(segment.audioUrl)
      }
    })

    // 清理任务信息
    tasks.delete(taskId)
    taskBuffers.delete(taskId)
    taskInfo.status = 'cancelled'
  }

  /** 播放指定任务 */
  function playTask(taskId: string): void {
    const taskInfo = tasks.get(taskId)
    if (!taskInfo) {
      addDebugLog(`任务 ${taskId} 不存在，无法播放`)
      return
    }

    // 停止当前播放的任务
    if (currentPlayingTask && currentPlayingTask !== taskId) {
      stopCurrentPlayback()
    }

    // 找到第一个就绪的段落开始播放
    const firstReadySegment = taskInfo.segments.find(
      (seg) => seg.status === 'ready',
    )
    if (firstReadySegment) {
      playSegment(firstReadySegment)
    } else {
      addDebugLog(`任务 ${taskId} 没有就绪的段落可播放`)
    }
  }

  /** 强制播放指定任务（立即停止当前播放） */
  function forcePlayTask(taskId: string): void {
    addDebugLog(`🔥 强制播放任务: ${taskId}`)

    // 停止所有其他任务
    stopAllPlayback()

    // 开始播放指定任务
    playTask(taskId)
  }

  /** 切换到指定任务（取消其他任务的连接和状态） */
  function switchToTask(taskId: string): void {
    addDebugLog(`🔄 切换到任务: ${taskId}`)

    // 取消所有其他任务（除了目标任务）
    const taskIdsToCancel = Array.from(tasks.keys()).filter(
      (id) => id !== taskId,
    )

    taskIdsToCancel.forEach((cancelTaskId) => {
      addDebugLog(`❌ 取消其他任务: ${cancelTaskId}`)
      cancelTask(cancelTaskId)
    })

    // 停止当前播放
    stopAllPlayback()

    // 开始播放指定任务
    playTask(taskId)
  }

  /** 停止所有播放 */
  function stopAllPlayback(): void {
    addDebugLog(`🛑 停止所有播放`)

    if (currentPlayingTask) {
      const stoppedTaskId = currentPlayingTask
      stopCurrentPlayback()
      onTaskPlayStateChange?.(stoppedTaskId, false)
    }
  }

  /** 检查任务是否正在播放 */
  function isTaskPlaying(taskId: string): boolean {
    return currentPlayingTask === taskId
  }

  /** 获取当前播放任务的详细状态 */
  function getCurrentPlayingStatus(): {
    taskId: string | null
    segmentIndex: number
    isPlaying: boolean
  } {
    return {
      taskId: currentPlayingTask,
      segmentIndex: currentPlayingIndex,
      isPlaying: currentPlayingTask !== null,
    }
  }

  /** 强制切割任务的当前缓冲区 */
  function forceSegmentTask(taskId: string): void {
    const textBuffer = taskBuffers.get(taskId)?.trim()
    if (textBuffer) {
      createSegmentForTask(taskId, textBuffer)
      taskBuffers.set(taskId, '')
      addDebugLog(`任务 ${taskId} 强制切割完成`)
    }
  }

  /** 清理所有任务和状态 */
  function clearAll(): void {
    addDebugLog('清理所有状态...')

    // 停止当前播放
    stopAllPlayback()

    // 清理所有定时器
    timeoutTimers.forEach((timer) => clearTimeout(timer))
    timeoutTimers.clear()

    // 取消所有TTS任务
    ttsClient.cancelAllTasks()

    // 清理所有音频URL
    tasks.forEach((taskInfo) => {
      taskInfo.segments.forEach((segment) => {
        if (segment.audioUrl) {
          ttsClient.revokeAudioUrl(segment.audioUrl)
        }
      })
    })

    // 重置所有状态
    tasks.clear()
    taskBuffers.clear()
    currentPlayingTask = null
    currentPlayingIndex = -1
    currentAudio = null
    nextSegmentIndex = 0
    debugLogs = []

    addDebugLog('✅ 已清理所有状态')
  }

  // 计算属性
  const segments = computed(() => {
    const allSegments: IStreamSegment[] = []
    tasks.forEach((task) => {
      allSegments.push(...task.segments)
    })
    return allSegments.sort((a, b) => a.index - b.index)
  })

  const isPlaying = computed(() => currentPlayingTask !== null)
  const totalSegments = computed(() => segments.value.length)
  const completedSegments = computed(
    () => segments.value.filter((seg) => seg.status === 'completed').length,
  )

  // 配置相关计算属性
  const voiceAutoPlayStatus = computed(
    () => aiSettingStore.config.voiceAutoPlayStatus,
  )
  const voiceSpeed = computed(() => Number(aiSettingStore.config.voiceSpeed))
  const isConnected = ref(true) // 新架构连接状态由各任务独立管理

  // 组件卸载时清理
  onUnmounted(() => {
    clearAll()
  })

  /** 启动流式输出任务 */
  function startStreamingTask(
    options: IStreamingTaskOptions = {},
  ): IStreamingTask {
    const taskId = `stream_${Date.now()}`

    // 如果启用自动切换，停止其他任务
    if (options.autoSwitch) {
      addDebugLog(`🔄 流式任务自动切换：停止所有其他任务`)
      stopAllPlayback()

      // 取消其他任务的连接
      const otherTaskIds = Array.from(tasks.keys()).filter(
        (id) => id !== taskId,
      )
      otherTaskIds.forEach((id) => {
        addDebugLog(`❌ 取消任务: ${id}`)
        cancelTask(id)
      })
    }

    // 创建响应式状态
    const isStreaming = ref(true)
    const progress = ref(0)
    const currentText = ref('')

    // 默认配置
    const finalOptions: Required<IStreamingTaskOptions> = {
      autoSwitch: options.autoSwitch ?? true,
      chunkSize: options.chunkSize ?? 8,
      delay: options.delay ?? 50,
      autoPlay: options.autoPlay ?? true,
    }

    // 存储流式任务信息
    streamingTasks.set(taskId, {
      isStreaming,
      progress,
      currentText,
      totalText: '',
      currentIndex: 0,
      options: finalOptions,
    })

    // 启动TTS任务
    startTask(taskId)
    addDebugLog(`🚀 启动流式任务: ${taskId}`)

    return {
      taskId,
      isStreaming,
      progress,
      currentText,
      addText: (text: string) => addTextToStreamingTask(taskId, text),
      finish: () => finishStreamingTask(taskId),
      cancel: () => cancelStreamingTask(taskId),
    }
  }

  /** 为流式任务添加文本 */
  function addTextToStreamingTask(taskId: string, text: string): void {
    const streamingTask = streamingTasks.get(taskId)
    if (!streamingTask || !streamingTask.isStreaming.value) {
      addDebugLog(`❌ 流式任务 ${taskId} 不存在或已停止`)
      return
    }

    // 添加文本到总文本
    streamingTask.totalText += text
    addDebugLog(
      `📝 流式任务 ${taskId} 添加文本: "${text}" (总长度: ${streamingTask.totalText.length})`,
    )

    // 如果这是第一次添加文本，开始输出处理
    if (streamingTask.currentIndex === 0 && !streamingTask.timer) {
      processStreamingOutput(taskId)
    }
  }

  /** 处理流式输出 */
  function processStreamingOutput(taskId: string): void {
    const streamingTask = streamingTasks.get(taskId)
    if (!streamingTask || !streamingTask.isStreaming.value) {
      return
    }

    const { totalText, currentIndex, options } = streamingTask

    if (currentIndex >= totalText.length) {
      // 等待更多文本或完成
      streamingTask.timer = window.setTimeout(() => {
        processStreamingOutput(taskId)
      }, options.delay)
      return
    }

    // 计算本次输出的文本块
    const endIndex = Math.min(
      currentIndex + options.chunkSize,
      totalText.length,
    )
    const chunk = totalText.substring(currentIndex, endIndex)

    // 更新当前文本和进度
    streamingTask.currentText.value += chunk
    streamingTask.currentIndex = endIndex

    // 计算进度（基于当前已输出的文本）
    streamingTask.progress.value =
      (endIndex / Math.max(totalText.length, endIndex)) * 100

    // 添加到TTS任务
    if (options.autoPlay) {
      addTextToTask(taskId, chunk)
    }

    addDebugLog(
      `📤 流式输出: ${taskId}, 进度: ${streamingTask.progress.value.toFixed(1)}%, 块: "${chunk}"`,
    )

    // 继续下一个块
    streamingTask.timer = window.setTimeout(() => {
      processStreamingOutput(taskId)
    }, options.delay)
  }

  /** 完成流式任务 */
  function finishStreamingTask(taskId: string): void {
    const streamingTask = streamingTasks.get(taskId)
    if (!streamingTask) {
      return
    }

    addDebugLog(`🏁 完成流式任务: ${taskId}`)

    // 清理定时器
    if (streamingTask.timer) {
      clearTimeout(streamingTask.timer)
    }

    // 确保所有文本都已输出
    const remainingText = streamingTask.totalText.substring(
      streamingTask.currentIndex,
    )
    if (remainingText) {
      streamingTask.currentText.value += remainingText
      if (streamingTask.options.autoPlay) {
        addTextToTask(taskId, remainingText)
      }
    }

    // 完成TTS任务
    finishTask(taskId)

    // 更新状态
    streamingTask.progress.value = 100
    streamingTask.isStreaming.value = false

    // 清理任务
    setTimeout(() => {
      streamingTasks.delete(taskId)
      addDebugLog(`🧹 清理流式任务: ${taskId}`)
    }, 1000)
  }

  /** 取消流式任务 */
  function cancelStreamingTask(taskId: string): void {
    const streamingTask = streamingTasks.get(taskId)
    if (!streamingTask) {
      return
    }

    addDebugLog(`❌ 取消流式任务: ${taskId}`)

    // 清理定时器
    if (streamingTask.timer) {
      clearTimeout(streamingTask.timer)
    }

    // 取消TTS任务
    cancelTask(taskId)

    // 更新状态
    streamingTask.isStreaming.value = false

    // 清理任务
    streamingTasks.delete(taskId)
  }

  /** 模拟流式输入处理完整文本 */
  function simulateStreamingInput(taskId: string, content: string): void {
    addDebugLog(`🔧 模拟流式输入处理: ${content.length}字符`)

    // 按小块逐步添加文本，复用现有的分段逻辑
    const chunkSize = 20 // 每次添加20个字符，模拟流式输入
    let processed = 0

    while (processed < content.length) {
      const chunk = content.substring(processed, processed + chunkSize)
      addTextToTask(taskId, chunk)
      processed += chunkSize
    }

    addDebugLog(`🎯 模拟流式输入完成，共处理 ${content.length} 字符`)
  }

  /** 智能播放（自动切换其他任务） */
  function playWithSmartSwitch(taskId: string, content: string): void {
    addDebugLog(`🔄 智能播放: ${taskId}`)

    // 停止所有其他任务
    stopAllPlayback()

    // 取消其他任务连接
    const otherTaskIds = Array.from(tasks.keys()).filter((id) => id !== taskId)
    otherTaskIds.forEach((id) => {
      addDebugLog(`❌ 取消其他任务: ${id}`)
      cancelTask(id)
    })

    // 启动新任务
    startTask(taskId)

    // 模拟流式输入，复用现有分段逻辑
    simulateStreamingInput(taskId, content)

    // 完成任务（处理剩余缓冲区文本）
    finishTask(taskId)

    // 开始播放
    playTask(taskId)
  }

  return {
    // 状态
    segments,
    currentPlayingIndex: $$(currentPlayingIndex),
    debugLogs: $$(debugLogs),
    isConnected,

    // 计算属性
    isPlaying,
    totalSegments,
    completedSegments,

    // 配置状态
    voiceAutoPlayStatus,
    voiceSpeed,

    // 任务管理方法
    startTask,
    addTextToTask,
    finishTask,
    cancelTask,
    clearAll,

    // 播放控制方法
    playTask,
    /** 强制播放指定任务（立即停止当前播放） */
    forcePlayTask,
    /** 切换到指定任务（取消其他任务的连接和状态） */
    switchToTask,
    /** 停止所有播放 */
    stopAllPlayback,
    /** 检查任务是否正在播放 */
    isTaskPlaying,
    /** 获取当前播放任务的详细状态 */
    getCurrentPlayingStatus,

    // 工具方法
    addDebugLog,
    forceSegmentTask,
    checkAndSegmentTask,

    // 流式输出管理方法
    startStreamingTask,
    playWithSmartSwitch,

    // 音频组装相关
    audioAssembler: audioAssemblerInstance,
  }
}
