import type { Ref } from 'vue'

/** 方法注册信息 */
export interface IMethodInfo {
  /** 方法函数 */
  method: (...args: any[]) => any
  /** 注册的组件实例ID */
  componentId: string
  /** 注册的路由路径 */
  routePath: string
  /** 方法描述 */
  description?: string
}

/** 重复注册策略 */
export type TDuplicateStrategy = 'overwrite' | 'ignore' | 'error'

/** 方法注册选项 */
export interface IMethodOptions {
  /** 是否在组件卸载时自动注销 */
  autoUnregister?: boolean
  /** 重复注册策略 */
  duplicateStrategy?: TDuplicateStrategy
  /** 方法描述 */
  description?: string
}

/** 全局方法注册器 - 单例模式，优化性能 */
class MethodRegistry {
  private static instance: MethodRegistry
  private methods = new Map<string, IMethodInfo>()
  private methodKeys = ref<string[]>([])

  private constructor() {}

  static getInstance(): MethodRegistry {
    if (!MethodRegistry.instance) {
      MethodRegistry.instance = new MethodRegistry()
    }
    return MethodRegistry.instance
  }

  getReactiveKeys(): Readonly<Ref<readonly string[]>> {
    return readonly(this.methodKeys)
  }

  /** 优化：只在必要时更新方法键 */
  private updateMethodKeys(): void {
    this.methodKeys.value = Array.from(this.methods.keys())
  }

  register<T extends (...args: any[]) => any>(
    key: string,
    method: T,
    componentId: string,
    routePath: string,
    description?: string,
    duplicateStrategy: TDuplicateStrategy = 'overwrite',
  ): void {
    if (this.methods.has(key)) {
      const existingInfo = this.methods.get(key)!
      switch (duplicateStrategy) {
        case 'overwrite':
          console.warn(
            `方法 "${key}" 已存在 (组件: ${existingInfo.componentId})，将被覆盖`,
          )
          break
        case 'ignore':
          console.log(`方法 "${key}" 已存在，忽略本次注册`)
          return
        case 'error':
          throw new Error(
            `方法 "${key}" 已存在 (组件: ${existingInfo.componentId})，不允许重复注册`,
          )
      }
    }

    const methodInfo: IMethodInfo = {
      method,
      componentId,
      routePath,
      description,
    }

    this.methods.set(key, methodInfo)
    this.updateMethodKeys()
    console.log(
      `方法 "${key}" 注册成功 (组件: ${componentId}, 路由: ${routePath})`,
    )
  }

  unregister(key: string): void {
    if (this.methods.has(key)) {
      this.methods.delete(key)
      this.updateMethodKeys()
      console.log(`方法 "${key}" 注销成功`)
    }
  }

  /** 优化：合并重复的过滤逻辑 */
  private filterAndDeleteMethods(
    filterFn: (info: IMethodInfo, key: string) => boolean,
    logPrefix: string,
  ): string[] {
    const keysToDelete: string[] = []

    // 一次遍历完成过滤和删除
    for (const [key, info] of this.methods) {
      if (filterFn(info, key)) {
        keysToDelete.push(key)
        this.methods.delete(key)
      }
    }

    if (keysToDelete.length > 0) {
      this.updateMethodKeys()
      console.log(`${logPrefix} ${keysToDelete.length} 个方法:`, keysToDelete)
    }

    return keysToDelete
  }

  unregisterByComponent(componentId: string): void {
    this.filterAndDeleteMethods(
      (info) => info.componentId === componentId,
      `已注销组件 "${componentId}" 的`,
    )
  }

  unregisterByRoute(routePath: string): void {
    this.filterAndDeleteMethods(
      (info) => info.routePath === routePath,
      `已注销路由 "${routePath}" 的`,
    )
  }

  call<T = any>(key: string, ...args: any[]): T | undefined {
    const methodInfo = this.methods.get(key)
    if (!methodInfo) {
      console.warn(`方法 "${key}" 不存在`)
      return undefined
    }
    try {
      return methodInfo.method(...args) as T
    } catch (error) {
      console.error(`调用方法 "${key}" 时发生错误:`, error)
      return undefined
    }
  }

  has(key: string): boolean {
    return this.methods.has(key)
  }

  getKeys(): string[] {
    return Array.from(this.methods.keys())
  }

  getMethodInfo(key: string): IMethodInfo | undefined {
    return this.methods.get(key)
  }

  /** 优化：使用过滤而不是循环 */
  getMethodsByComponent(componentId: string): string[] {
    return Array.from(this.methods.entries())
      .filter(([, info]) => info.componentId === componentId)
      .map(([key]) => key)
  }

  getMethodsByRoute(routePath: string): string[] {
    return Array.from(this.methods.entries())
      .filter(([, info]) => info.routePath === routePath)
      .map(([key]) => key)
  }
}

/** 方法注册 Hook */
export function useSharedMethods() {
  const methodRegistry = MethodRegistry.getInstance()
  const route = useRoute()

  // 生成唯一的组件实例ID
  const componentId = `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

  // 当前路由路径
  const currentRoutePath = ref(route.path)

  /** 注册方法 */
  function registerMethod<T extends (...args: any[]) => any>(
    key: string,
    method: T,
    options: IMethodOptions = {},
  ): void {
    const { duplicateStrategy = 'overwrite', description } = options

    methodRegistry.register(
      key,
      method,
      componentId,
      currentRoutePath.value,
      description,
      duplicateStrategy,
    )
  }

  /** 注销方法 */
  function unregisterMethod(key: string): void {
    methodRegistry.unregister(key)
  }

  /** 调用方法 */
  function callMethod<T = any>(key: string, ...args: any[]): T | undefined {
    return methodRegistry.call<T>(key, ...args)
  }

  /** 检查方法是否存在 */
  function hasMethod(key: string): boolean {
    return methodRegistry.has(key)
  }

  /** 获取所有方法键 - 响应式版本 */
  function getMethodKeys(): Readonly<Ref<readonly string[]>> {
    return methodRegistry.getReactiveKeys()
  }

  /** 批量注册方法 */
  function registerMethods(
    methods: Record<string, (...args: any[]) => any>,
    options: IMethodOptions = {},
  ): void {
    Object.entries(methods).forEach(([key, method]) => {
      registerMethod(key, method, options)
    })
  }

  /** 根据组件ID注销方法 */
  function unregisterByComponent(): void {
    methodRegistry.unregisterByComponent(componentId)
  }

  /** 根据路由路径注销方法 */
  function unregisterByRoute(routePath?: string): void {
    const targetPath = routePath || currentRoutePath.value
    methodRegistry.unregisterByRoute(targetPath)
  }

  /** 获取当前组件注册的方法 */
  function getComponentMethods(): string[] {
    return methodRegistry.getMethodsByComponent(componentId)
  }

  /** 获取方法详细信息 */
  function getMethodInfo(key: string): IMethodInfo | undefined {
    return methodRegistry.getMethodInfo(key)
  }

  // 监听路由变化，自动清理上一个路由的方法
  watch(
    () => route.path,
    (newPath, oldPath) => {
      if (oldPath && oldPath !== newPath) {
        // 清理上一个路由注册的方法
        methodRegistry.unregisterByRoute(oldPath)
        console.log(`路由从 ${oldPath} 切换到 ${newPath}，已清理上一路由的方法`)
      }
      currentRoutePath.value = newPath
    },
  )

  // 组件卸载时自动清理
  onUnmounted(() => {
    // 清理当前组件注册的所有方法
    unregisterByComponent()
    console.log(`组件 ${componentId} 卸载，已清理相关方法`)
  })

  return {
    // 方法相关
    registerMethod,
    unregisterMethod,
    callMethod,
    hasMethod,
    getMethodKeys,
    registerMethods,
    unregisterByComponent,
    unregisterByRoute,
    getComponentMethods,
    getMethodInfo,

    // 组件和路由信息
    componentId,
    currentRoutePath: readonly(currentRoutePath),

    // 调试信息
    debug: {
      getAllMethods: () => methodRegistry.getKeys(),
      getMethodInfo: (key: string) => methodRegistry.getMethodInfo(key),
    },
  }
}

/** 快捷方法：仅用于调用已注册的方法 */
export function useMethodCaller() {
  const methodRegistry = MethodRegistry.getInstance()

  return {
    call: <T = any>(key: string, ...args: any[]): T | undefined => {
      return methodRegistry.call<T>(key, ...args)
    },
    has: (key: string): boolean => {
      return methodRegistry.has(key)
    },
  }
}
