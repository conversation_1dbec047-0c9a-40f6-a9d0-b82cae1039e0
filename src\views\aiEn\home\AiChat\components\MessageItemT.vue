<template>
  <div class="message-item-t-wrapper">
    <!-- 老师消息内容 -->
    <div class="message-item-t">
      <!-- 使用markdown渲染组件 -->
      <MarkdownRender
        :content="message?.content || ''"
        :streaming="isStreaming"
      />
      <!-- 翻译区域 -->
      <div
        v-show="translationContent"
        :class="['translate-area', { 'translate-show': showTranslation }]"
      >
        <div class="translate-content">
          {{ translationContent }}
        </div>
      </div>
    </div>

    <!-- 操作工具栏 -->
    <MessageActionBar
      :class="{ 'action-bar-hidden': isStreaming }"
      :message="message"
      :show-translation="showTranslation"
      :has-translation-content="!!translationContent"
      :is-latest="props.isLatest"
      :expanded-message-ids="props.expandedMessageIds"
      mode="ai"
      @play="handlePlay"
      @like="handleLike"
      @dislike="handleDislike"
      @bar-translate="barToggleTranslation"
      @teach-me="handleTeachMe"
      @update-message="handleUpdateMessage"
      @expand-history="handleExpandHistory"
    />
  </div>
</template>

<script setup lang="ts">
import type { IMessage } from '../type'
import { EMessageStatus } from '../type'
import MessageActionBar from './MessageActionBar.vue'
import MarkdownRender from './MarkdownRender/index.vue'

interface IProps {
  /** 消息数据 */
  message?: IMessage
  /** 是否是最新消息 */
  isLatest?: boolean
  /** 当前展开工具栏的消息ID列表 */
  expandedMessageIds?: number[]
}

const props = withDefaults(defineProps<IProps>(), {
  isLatest: false,
  expandedMessageIds: () => [],
})
const emit = defineEmits<{
  play: [message?: IMessage]
  like: [message?: IMessage]
  dislike: [message?: IMessage]
  teachMe: [message?: IMessage]
  barTranslate: [message?: IMessage, showTranslation?: boolean]
  updateMessage: [message: IMessage]
  expandHistory: [messageId: number | null]
}>()

// 翻译显示状态
let showTranslation = $ref(false)
// 翻译内容
let translationContent = $ref('')

// 计算是否正在流式输出 - 简化响应式处理
const isStreaming = computed(() => {
  const status = props.message?.status
  const streaming = status === EMessageStatus.GENERATING
  return streaming
})

/** 切换翻译显示状态 */
function barToggleTranslation(message?: IMessage, translateResult?: any): void {
  // 如果传入了翻译结果，说明是新的翻译数据
  if (translateResult?.toContent) {
    translationContent = translateResult.toContent
    // 先让数据存在，等一下再打开动画
    setTimeout(() => {
      showTranslation = true
    }, 10)
  } else {
    // 没有传入翻译结果，只是切换显示状态
    showTranslation = !showTranslation
  }

  if (message) {
    emit('barTranslate', message, showTranslation)
  }
}

/** 播放处理 */
function handlePlay(message?: IMessage) {
  if (message) {
    emit('play', message)
  }
}

/** 点赞处理 */
function handleLike(message?: IMessage) {
  if (message) {
    emit('like', message)
  }
}

/** 踩处理 */
function handleDislike(message?: IMessage) {
  if (message) {
    emit('dislike', message)
  }
}

/** 教我回答处理 */
function handleTeachMe(message?: IMessage) {
  if (message) {
    emit('teachMe', message)
  }
}

/** 更新消息处理 */
function handleUpdateMessage(message: IMessage) {
  emit('updateMessage', message)
}

/** 处理展开历史消息工具栏 */
function handleExpandHistory(messageId: number | null) {
  emit('expandHistory', messageId)
}
</script>

<style scoped lang="scss">
.message-item-t-wrapper {
  .message-item-t {
    font-weight: 500;
    font-size: 16px;
    color: #141619;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    background: #fff;
    border-radius: 4px 12px 12px 12px;
    min-height: 40px;
    padding: 12px;
    color: #141619;
    margin-bottom: 16px;
    position: relative;

    .translate-area {
      overflow: hidden;
      max-height: 0;
      opacity: 0;
      transform: translateY(-10px);
      margin-top: 0;
      padding-top: 0;
      border-top: none;
      transition:
        max-height 0.3s ease-in-out,
        opacity 0.3s ease-in-out,
        transform 0.3s ease-in-out,
        margin-top 0.3s ease-in-out,
        padding-top 0.3s ease-in-out;

      &.translate-show {
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        margin-top: 8px;
        padding-top: 8px;
        max-height: 200px;
        opacity: 1;
        transform: translateY(0);
      }

      .translate-content {
        font-size: 13px;
        color: #5c5f66;
        line-height: 18px;
      }
    }

    .translate-toggle {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-top: 8px;
      cursor: pointer;
      font-size: 12px;
      color: #666;
      transition: color 0.2s ease;

      &:hover {
        color: #007bff;
      }

      .toggle-text {
        font-size: 12px;
      }
    }
  }

  // ActionBar状态控制
  .action-bar-hidden {
    opacity: 0;
    pointer-events: none;
  }
}
</style>
