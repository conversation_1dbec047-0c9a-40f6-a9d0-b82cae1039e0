import { nextTick } from 'vue'
import { getFreeChatWelcome, getThemeChatWelcome } from '@/api/aiEn'
import { EChatMode, EMessageStatus } from '../type'
import type { IApiMessage } from '../type'

// 欢迎语相关常量
const WELCOME_CACHE_DURATION = 10 * 60 * 1000 // 1分钟（测试用）
const WELCOME_CACHE_KEY = 'free_welcome_last_shown'

/** 欢迎语接口参数类型 */
interface IWelcomeParams {
  /** 对话ID */
  conversationId: string | number
  /** 欢迎语类型: 1-首次, 2-再次 (没有历史消息传首次,否则传再次) */
  welcomeType: 1 | 2
}

export function useWelcome() {
  /** 检查是否应该显示欢迎语（1分钟缓存机制） */
  function shouldShowWelcome(): boolean {
    try {
      const lastShown = localStorage.getItem(WELCOME_CACHE_KEY)

      if (!lastShown) return true

      const now = Date.now()
      const lastShownTime = parseInt(lastShown)

      return now - lastShownTime >= WELCOME_CACHE_DURATION
    } catch (error) {
      // localStorage 不可用时，降级到每次都显示
      console.warn('localStorage 不可用，欢迎语将每次显示:', error)
      return true
    }
  }

  /** 更新欢迎语显示缓存 */
  function updateWelcomeCache(): void {
    try {
      localStorage.setItem(WELCOME_CACHE_KEY, Date.now().toString())
    } catch (error) {
      console.warn('无法更新欢迎语缓存:', error)
    }
  }

  /** 欢迎语流式显示 */
  async function displayWelcomeMessageWithStream(
    welcomeMessageData: IApiMessage,
    options: {
      /** 消息列表引用 */
      messages: any[]
      /** 创建本地消息函数 */
      createLocalMessageFromApi: (data: IApiMessage) => any
      /** 更新消息函数 */
      updateMessageById: (id: number, updateFn: (msg: any) => void) => void
      /** 滚动到底部函数 */
      scrollToBottom: () => void
      /** TTS播放器 */
      streamTTSPlayer?: any
      /** 是否自动播放TTS */
      autoPlay?: boolean
    },
  ): Promise<void> {
    const {
      messages,
      createLocalMessageFromApi,
      updateMessageById,
      scrollToBottom,
      streamTTSPlayer,
      autoPlay = false,
    } = options

    // 将API消息转换为本地消息格式，初始内容为空
    const welcomeMessage = createLocalMessageFromApi({
      ...welcomeMessageData,
      content: '', // 初始内容为空，准备流式显示
    })

    // 设置消息状态为生成中
    welcomeMessage.status = EMessageStatus.GENERATING

    // 插入到消息列表
    messages.push(welcomeMessage)

    // 滚动到底部
    scrollToBottom()

    // 如果有TTS播放器且启用自动播放，开始TTS任务并立即播放
    const taskId = String(welcomeMessage.messageId)
    let ttsTaskStarted = false

    if (streamTTSPlayer && autoPlay) {
      try {
        // 直接使用智能播放，它内部会处理任务创建和管理
        streamTTSPlayer.playWithSmartSwitch(taskId, welcomeMessageData.content)
        ttsTaskStarted = true
        console.log(`欢迎语TTS开始播放: ${taskId}`)
      } catch (error) {
        console.error('启动欢迎语TTS播放失败:', error)
      }
    }

    // 模拟流式输出
    const fullContent = welcomeMessageData.content
    const chars = Array.from(fullContent) // 支持中文字符正确分割
    let currentContent = ''

    for (let i = 0; i < chars.length; i++) {
      currentContent += chars[i]

      // 更新消息内容
      updateMessageById(welcomeMessage.messageId, (message) => {
        message.content = currentContent
      })

      // 滚动到底部
      scrollToBottom()

      // 控制流式速度（每个字符间隔）
      await new Promise((resolve) => setTimeout(resolve, 10))
    }

    // 完成后更新状态
    updateMessageById(welcomeMessage.messageId, (message) => {
      message.status = EMessageStatus.SUCCESS
      message.translation = '翻译功能待实现'
    })

    // 如果启动了TTS播放，记录完成日志
    if (ttsTaskStarted) {
      console.log(`欢迎语流式显示完成，TTS继续播放: ${taskId}`)
    }
  }

  /** 处理欢迎语逻辑 - 主函数 */
  async function handleWelcomeMessage(
    conversationId: string,
    hasHistoryMessages: boolean = false,
    options: {
      /** 聊天模式 */
      mode: EChatMode
      /** 流式显示函数 */
      displayStream: (data: IApiMessage, autoPlay?: boolean) => Promise<void>
      /** 是否自动播放TTS */
      autoPlay?: boolean
    },
  ): Promise<void> {
    const { mode, displayStream, autoPlay = false } = options

    if (!conversationId) {
      return
    }

    // 主题对话每次都是全新的，始终显示首次欢迎语
    // 自由对话需要检查1分钟缓存
    if (mode === EChatMode.FREE && !shouldShowWelcome()) {
      return
    }

    const welcomeParams: IWelcomeParams = {
      conversationId,
      // 主题对话始终是首次，自由对话根据历史消息判断
      welcomeType: mode === EChatMode.THEME ? 1 : hasHistoryMessages ? 2 : 1,
    }

    try {
      let welcomeResult

      // 调用欢迎语接口获取内容
      if (mode === EChatMode.FREE) {
        welcomeResult = await getFreeChatWelcome(welcomeParams)
        // 只有自由对话需要更新缓存时间
        updateWelcomeCache()
      } else {
        welcomeResult = await getThemeChatWelcome(welcomeParams)
        // 主题对话无需缓存，每次都是新对话
      }

      // 如果有欢迎语消息，流式显示并传递autoPlay参数
      if (welcomeResult && welcomeResult.content) {
        await displayStream(welcomeResult, autoPlay)
      }
    } catch (error) {
      // 欢迎语失败不影响正常聊天流程
      console.warn('获取欢迎语失败:', error)
    }
  }

  return {
    shouldShowWelcome,
    updateWelcomeCache,
    displayWelcomeMessageWithStream,
    handleWelcomeMessage,
  }
}
