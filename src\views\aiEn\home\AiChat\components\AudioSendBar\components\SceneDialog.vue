<template>
  <van-popup
    v-model:show="showBottom"
    position="bottom"
    round
    teleport="#app"
    :closeable="activeSlider == 1"
    :style="{
      minHeight: '50vh',
      backgroundColor: '#F1F5FE',
    }"
    @close="handleClose"
    @open="openDialog"
  >
    <div class="scene-dialog overflow-hidden text-16px">
      <Transition
        :name="activeSlider == 1 ? 'slide-left' : 'slide-right'"
        mode="out-in"
      >
        <div
          v-if="activeSlider == 1"
          class="py-12px px-16px flex flex-col max-h-[90vh]"
        >
          <div class="pt-6px mb-18px font-500">情景对话</div>
          <div class="flex-1 overflow-y-auto no-bar grid grid-cols-2 gap-12px">
            <div
              v-for="item in sceneThemes"
              :key="item.chatThemeId"
              class="bg-white rounded-[12px] p-[4px_4px_12px_12px] text-14px"
              :class="{
                'min-h-[100px] pt-18px': !item.themeIcon,
              }"
              @click="chooseTheme(item)"
            >
              <div v-if="item.themeIcon" class="flex justify-end mb-8px">
                <g-img
                  width="34"
                  height="34"
                  :src="item.themeIcon"
                  radius="100%"
                  fit="cover"
                />
              </div>

              <div>{{ item.themeName }}</div>
              <div class="font-500 line-1">{{ item.themeNameEn }}</div>
            </div>
          </div>
        </div>
        <div v-else class="pt-20px flex flex-col max-h-[90vh]">
          <g-icon
            name="arrow-left"
            size="18"
            color="#999"
            class="pl-6px"
            @click="back"
          />
          <div class="flex-1 overflow-y-auto no-bar">
            <div class="flex flex-col items-center justify-center mb-7px">
              <g-img
                v-if="currentTheme?.themeIcon"
                width="44"
                height="44"
                :src="currentTheme.themeIcon"
                radius="100%"
                fit="cover"
                class="mb-9px"
              />
              <span class="text-15px">{{ currentTheme?.themeName }}</span>
              <span class="text-18px">{{ currentTheme?.themeNameEn }}</span>
              <div
                class="w-full h-1px mt-15px"
                style="background: var(--van-button-default-background)"
              ></div>
              <img class="h-26px" :src="$g.tool.getFileUrl('aiEn/ck.png')" />
            </div>
            <g-loading v-if="pointsLoading"></g-loading>
            <template v-if="!pointsLoading">
              <div
                v-for="point in examPoints"
                :key="point.chatThemeKnowledgeId"
                class="px-14px"
              >
                <div
                  class="flex items-center mx-2px py-13px border-b border-solid border-[#8590A6]/20"
                  @click="ckChecked = point.chatThemeKnowledgeId"
                >
                  <van-radio-group v-model="ckChecked">
                    <van-radio :name="point.chatThemeKnowledgeId"></van-radio>
                  </van-radio-group>
                  <div class="flex-1 ml-14px">
                    <div class="text-15px mb-7px">
                      {{ point.chineseContent }}
                    </div>
                    <span class="text-13px text-gray-dark">{{
                      point.englishContent
                    }}</span>
                  </div>
                </div>
              </div>
            </template>
          </div>

          <div class="flex-shrink-0 py-16px px-38px">
            <van-button
              type="primary"
              class="w-full rounded-[22px] h-44px text-16px"
              :disabled="!ckChecked"
              @click="handleStartLearn"
            >
              开始学习
            </van-button>
          </div>
        </div>
      </Transition>
      <template v-if="!sceneThemes?.length && !loading">
        <g-empty></g-empty>
      </template>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { getChatThemeList, getChatThemeKnowledgeList } from '@/api/aiEn'

const emits = defineEmits(['startLearn'])
let mode = inject('chatMode') // 自由对话free、主题对话theme

const route = useRoute()
const router = useRouter()
const showBottom = defineModel<boolean>('show')
// 当前激活的滑块
let activeSlider = $ref(1)
let ckChecked = $ref<any>(null)
let loading = $ref(true)
let pointsLoading = $ref(true)
let sceneThemes = $ref<any[]>([])
let currentTheme = $ref<any>(null)
let examPoints = $ref<any[]>([])

function openDialog() {
  fetchThemeList()
  if (route.query.chatThemeId) activeSlider = 2
}

function back() {
  activeSlider = 1
  ckChecked = null
}

/**
 * 获取主题列表
 */
function fetchThemeList() {
  getChatThemeList()
    .then((res) => {
      sceneThemes = res
      if (route.query.chatThemeId) {
        nextTick(() => {
          currentTheme = sceneThemes.find(
            (item) => item.chatThemeId == route.query.chatThemeId,
          )
          fetchExamPoints(route.query.chatThemeId as any)
          ckChecked = Number(route.query.chatThemeKnowledgeId)
        })
      }
      loading = false
    })
    .catch((err) => {
      sceneThemes = []
      loading = false
    })
}

/**
 * 获取考点列表
 */
function fetchExamPoints(chatThemeId: number) {
  getChatThemeKnowledgeList({ chatThemeId })
    .then((res) => {
      examPoints = res
      pointsLoading = false
    })
    .catch((err) => {
      examPoints = []
      activeSlider = 1
      pointsLoading = false
    })
}

function handleClose() {
  showBottom.value = false
  setTimeout(() => {
    if (mode == 'free') {
      activeSlider = 1
    } else {
      activeSlider = 2
    }
    pointsLoading = true
  }, 800)
}

function chooseTheme(item: any) {
  currentTheme = item
  fetchExamPoints(item.chatThemeId)
  activeSlider = 2
}

/**
 * 开始学习
 */
function handleStartLearn() {
  handleClose()
  if (mode == 'free') {
    router.push({
      name: 'AiEnThemeChat',
      query: {
        chatThemeId: currentTheme.chatThemeId,
        chatThemeKnowledgeId: ckChecked,
      },
    })
  } else {
    router.replace({
      query: {
        chatThemeId: currentTheme.chatThemeId,
        chatThemeKnowledgeId: ckChecked,
      },
    })
    emits('startLearn')
  }
}
</script>

<style scoped lang="scss"></style>
