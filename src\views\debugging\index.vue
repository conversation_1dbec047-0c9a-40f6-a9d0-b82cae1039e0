<template>
  <div class="p-20px bg-[#fff]">
    <van-divider :style="{ color: '#666', borderColor: '#666' }"
      >登录信息</van-divider
    >
    <van-field v-model="loginFormData.userName" label="账号" clearable />
    <van-field v-model="loginFormData.password" label="密码" clearable />
    <van-space align="center" wrap class="mt-12px">
      <van-button
        v-for="user in getStudentList()"
        :key="user.name"
        plain
        size="small"
        @click="changeStudent(user)"
        >{{ user.name }}</van-button
      >
    </van-space>
    <van-button type="primary" block size="small" class="my-20px" @click="login"
      >登录</van-button
    >

    <van-divider :style="{ color: '#666', borderColor: '#666' }"
      >路由页面</van-divider
    >

    <Tree
      v-for="node in treeData"
      :key="node.name"
      v-model:is-open="node.isOpen"
      :node="node"
      :is-checked="node.isChecked"
      :tree-data="treeData"
      expand-all
      @change="handleNodeClick"
    />
  </div>
</template>

<script setup lang="ts" name="Debugging">
import Tree from './components/Tree.vue'
import { useRouterStore } from '@/stores/modules/router'
import { useUserStore } from '@/stores/modules/user'

const router = useRouter()

let { routerArr } = $(storeToRefs(useRouterStore()))
let treeData = $ref($g._.cloneDeep(routerArr))

const { getTokenTest } = useUserStore()

// 登录表单数据
const loginFormData = reactive({
  userName: '3530018328',
  password: '123456a!=&',
})

function generateRandomNumber(min, max) {
  let randomNumber = Math.floor(Math.random() * (max - min + 1)) + min // 生成指定范围内的随机整数
  return randomNumber.toString().padStart(3, '0') // 使用padStart函数填充0，确保数字有3位
}
function changeStudent(item) {
  let idNum = item.userName
  if (item.schoolId) {
    switch (item.schoolId) {
      case '1442':
        idNum = `3530018${generateRandomNumber(300, 400)}`
        break
    }
  }
  loginFormData.userName = idNum
  login()
}

function getStudentList(): any {
  const colors = ['primary', 'success', 'info', 'warning', 'danger']
  const studentList = [
    { name: '吴文俊', userName: '3530018328', color: '' },
    { name: '张沁铃', userName: '3530015740', color: '' },
    { name: '周凌毅', userName: '3520013412', color: '' },
    { name: '常雨雯', userName: '3530021411', color: '' },
    { name: '李永付', userName: '3630010101', color: '' },
    { name: '杜鹏程', userName: '3630010100', color: '' },
    { name: '刘粤成(高三)', userName: '3430065533', color: '' },
    { name: '泸州天立随机账号', userName: '', schoolId: '1442', color: '' },
  ]
  studentList.forEach((e, i) => {
    e.color = colors[i % colors.length]
  })
  return studentList
}

// 节点跳转点击
function handleNodeClick(name, node) {
  router.push({
    name,
    query: node.meta.debugQuery,
  })
}

function login() {
  // 清楚所有本地存储
  localStorage.clear()
  sessionStorage.clear()
  getTokenTest(loginFormData)
}
</script>
<style lang="scss"></style>
