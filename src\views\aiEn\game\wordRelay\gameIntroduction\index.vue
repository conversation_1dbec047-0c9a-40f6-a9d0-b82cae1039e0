<template>
  <div>
    <g-navbar customTitle="单词接龙" customBackGround="transparent"> </g-navbar>
    <div
      class="px-12px pb-40px pt-36px overflow-auto no-bar"
      :style="{
        height: `calc(100vh - ${$g.navBarTotalHeight}px - 16px)`,
      }"
    >
      <div class="big-background-style px-16px pt-44px pb-20px relative">
        <div
          class="absolute button-style py-9px px-24px top-[-20px] left-[16px] br-[18px] !rounded-bl-[4px] text-[18px] text-[white]"
        >
          核心规则
        </div>
        <div class="card-style w-full p-20px !rounded-bl-[8px]">
          <div class="text-18px text-[#6666FF]">1.闪电接龙</div>
          <div class="flex mt-10px text-14px text-[#5C5F66]">
            <div class="mr-4px">·</div>
            <div>系统为你匹配实时对手</div>
          </div>
          <div class="flex mt-6px text-14px text-[#5C5F66]">
            <div class="mr-4px">·</div>
            <div>
              你必须用 <span class="text-[#6666FF]">新单词的首字母</span> 接住
              <span class="text-[#6666FF]">对方单词的尾字母</span>
            </div>
          </div>
        </div>
        <div class="card-style w-full p-20px !rounded-br-[8px] my-20px">
          <div class="text-18px text-[#6666FF]">2.三次生命线</div>
          <div class="flex mt-10px text-14px text-[#5C5F66]">
            <div class="mr-4px">·</div>
            <div>当你卡壳时，点击 💡 提示按钮</div>
          </div>
          <div class="flex mt-6px text-14px text-[#5C5F66]">
            <div class="mr-4px">·</div>
            <div>每次获得 <span class="text-[#6666FF]">3个可选单词</span></div>
          </div>
          <div class="flex mt-6px text-14px text-[#5C5F66]">
            <div class="mr-4px">·</div>
            <div>
              全程仅能使用
              <span class="text-[#6666FF]">3次提示</span>（珍惜机会）
            </div>
          </div>
        </div>
        <div class="card-style w-full p-20px !rounded-bl-[8px]">
          <div class="text-18px text-[#6666FF]">3.生死时速</div>
          <div class="flex mt-10px text-14px text-[#5C5F66]">
            <div class="mr-4px">·</div>
            <div class="text-[#6666FF]">15秒超时判负！</div>
          </div>
          <div class="flex mt-6px text-14px text-[#5C5F66]">
            <div class="mr-4px">·</div>
            <div>文字输入：键盘实时联想加速</div>
          </div>
          <div class="flex mt-6px text-14px text-[#5C5F66]">
            <div class="mr-4px">·</div>
            <div>语音输入：点击 🎤 按钮秒说单词</div>
          </div>
        </div>
      </div>
      <div class="flex justify-center mt-30px">
        <van-button
          type="primary"
          class="w-[247px] h-[48px] br-[18px] text-[18px]"
          size="small"
          @click="goToGame"
        >
          开始游戏
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const router = useRouter()
function goToGame() {
  router.push({ name: 'WordRelayGameMatching' })
}
</script>

<style lang="scss" scoped>
.button-style {
  box-shadow: 10px 10px 30px 0px rgba(113, 101, 249, 0.3);
  background: linear-gradient(180deg, #7165f9 0%, #5e56df 100%);
}

.card-style {
  box-shadow: 8px 8px 30px 0px rgba(69, 63, 140, 0.12);
  background: linear-gradient(180deg, #fbfcfe 0%, #eef1fa 100%);
  border: 1px solid white;
  border-radius: 32px;
}

.big-background-style {
  box-shadow: 8px 8px 30px 0px rgba(69, 63, 140, 0.12);
  background: linear-gradient(
    180deg,
    rgba(254, 254, 254, 0.7) 0%,
    rgba(242, 245, 252, 0.7) 100%
  );
  border-radius: 20px;
  border: 1px solid white;
}
</style>
