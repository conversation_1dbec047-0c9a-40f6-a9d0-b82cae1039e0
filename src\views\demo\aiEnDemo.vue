<template>
  <div class="h-full p-24px bg-[#f5f5f5]">
    <div class="max-w-800px mx-auto bg-white rounded-12px p-32px shadow-lg">
      <h1 class="text-32px font-bold mb-32px text-center text-[#333]">
        🎵 TTS 语音测试 Demo
      </h1>

      <!-- 语音设置 -->
      <div class="mb-24px p-16px bg-[#f8f9fa] rounded-8px">
        <h3 class="text-16px font-medium mb-12px text-[#333]">⚙️ 语音设置</h3>
        <div class="grid grid-cols-2 gap-16px">
          <div class="flex items-center gap-8px">
            <label class="text-14px text-[#666]">语音速度：</label>
            <select
              v-model="selectedSpeed"
              class="px-8px py-4px border border-[#ddd] rounded-4px text-14px"
            >
              <option value="0.5">0.5x (慢速)</option>
              <option value="0.8">0.8x (略慢)</option>
              <option value="1.0">1.0x (正常)</option>
              <option value="1.2">1.2x (略快)</option>
              <option value="1.5">1.5x (快速)</option>
              <option value="2.0">2.0x (超快)</option>
            </select>
          </div>
          <div class="flex items-center gap-8px">
            <label class="text-14px text-[#666]">自动播放：</label>
            <input v-model="autoPlay" type="checkbox" class="w-16px h-16px" />
          </div>
          <div class="flex items-center gap-8px">
            <label class="text-14px text-[#666]">智能切换：</label>
            <input
              v-model="smartSwitch"
              type="checkbox"
              class="w-16px h-16px"
            />
            <span class="text-12px text-[#999]">
              (点击其他消息时自动取消当前任务)
            </span>
          </div>
          <div class="flex items-center gap-8px">
            <label class="text-14px text-[#666]">显示详情：</label>
            <input
              v-model="showDetails"
              type="checkbox"
              class="w-16px h-16px"
            />
            <span class="text-12px text-[#999]">
              (显示段落处理和任务状态)
            </span>
          </div>
        </div>
      </div>

      <!-- 任务切换演示说明 -->
      <div
        v-if="smartSwitch"
        class="mb-24px p-16px bg-[#e3f2fd] rounded-8px border border-[#2196f3]"
      >
        <h3
          class="text-16px font-medium mb-8px text-[#1976d2] flex items-center gap-8px"
        >
          🔄 智能任务切换模式已启用
        </h3>
        <p class="text-14px text-[#1565c0] leading-relaxed">
          在此模式下，当您点击播放其他消息时，系统会自动：
          <span class="font-medium">取消当前任务的WebSocket连接</span> →
          <span class="font-medium">清理所有状态</span> →
          <span class="font-medium">立即切换到新任务</span>。
          试试先播放一个长消息，然后中途点击播放其他消息！
        </p>
      </div>

      <!-- 消息卡片列表 -->
      <div class="mb-24px">
        <h3 class="text-16px font-medium mb-16px text-[#333]">📨 消息列表</h3>
        <div class="space-y-16px">
          <div
            v-for="message in messages"
            :key="message.id"
            class="p-16px border rounded-12px relative"
            :class="{
              'bg-[#e3f2fd] border-[#2196f3]': message.type === 'user',
              'bg-[#f1f8e9] border-[#4caf50]': message.type === 'assistant',
              'border-[#ff9800]': message.isPlaying,
              'border-[#f44336]': message.hasError,
            }"
          >
            <!-- 消息类型标识 -->
            <div class="flex items-center justify-between mb-12px">
              <div class="flex items-center gap-8px">
                <span
                  class="px-8px py-2px rounded-4px text-12px font-medium text-white"
                  :class="{
                    'bg-[#2196f3]': message.type === 'user',
                    'bg-[#4caf50]': message.type === 'assistant',
                  }"
                >
                  {{ message.type === 'user' ? '👤 用户' : '🤖 AI助手' }}
                </span>
                <span class="text-12px text-[#666]">
                  {{ message.timestamp }}
                </span>
              </div>

              <!-- 播放状态指示器 -->
              <div class="flex items-center gap-8px">
                <span
                  v-if="message.isPlaying"
                  class="text-12px px-6px py-1px rounded-3px bg-[#ff9800] text-white"
                >
                  🎵 播放中
                </span>
                <span
                  v-else-if="message.hasError"
                  class="text-12px px-6px py-1px rounded-3px bg-[#f44336] text-white"
                >
                  ❌ 错误
                </span>
                <span
                  v-else-if="message.isCompleted"
                  class="text-12px px-6px py-1px rounded-3px bg-[#4caf50] text-white"
                >
                  ✅ 完成
                </span>
              </div>
            </div>

            <!-- 消息内容 -->
            <div class="text-14px text-[#333] leading-relaxed mb-16px">
              {{ message.content }}
              <!-- 流式输出光标 -->
              <span
                v-if="
                  currentStreamingTask?.isStreaming.value &&
                  message.id === currentStreamingTask?.taskId
                "
                class="inline-block w-2px h-16px bg-[#2196f3] ml-1px animate-pulse"
              ></span>
            </div>

            <!-- 流式输出进度条 -->
            <div
              v-if="
                currentStreamingTask?.isStreaming.value &&
                message.id === currentStreamingTask?.taskId
              "
              class="mb-16px p-12px bg-[#e3f2fd] rounded-6px border border-[#2196f3]"
            >
              <div class="flex items-center justify-between mb-8px">
                <span class="text-12px text-[#1976d2] font-medium"
                  >🚀 流式输出中...</span
                >
                <span class="text-12px text-[#1976d2]"
                  >{{ currentStreamingTask?.progress.value.toFixed(1) }}%</span
                >
              </div>
              <div class="w-full bg-[#bbdefb] rounded-full h-4px">
                <div
                  class="bg-[#2196f3] h-4px rounded-full transition-all duration-300 ease-out"
                  :style="{ width: `${currentStreamingTask?.progress.value}%` }"
                ></div>
              </div>
              <div class="text-10px text-[#1565c0] mt-4px">
                提示：可以在流式输出过程中点击其他消息测试任务切换功能
              </div>
            </div>

            <!-- 操作按钮区 -->
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-8px">
                <!-- 普通播放按钮 -->
                <button
                  v-if="!smartSwitch"
                  :disabled="
                    message.isPlaying ||
                    (currentStreamingTask?.isStreaming.value &&
                      message.id === currentStreamingTask?.taskId)
                  "
                  class="px-12px py-6px bg-[#2196f3] text-white rounded-6px text-12px font-medium disabled:bg-[#ccc] disabled:cursor-not-allowed hover:bg-[#1976d2] transition-colors flex items-center gap-4px"
                  @click="handlePlayMessage(message)"
                >
                  <span v-if="message.isPlaying">🔄</span>
                  <span
                    v-else-if="
                      currentStreamingTask?.isStreaming.value &&
                      message.id === currentStreamingTask?.taskId
                    "
                    >⏳</span
                  >
                  <span v-else>▶️</span>
                  {{
                    message.isPlaying
                      ? '播放中...'
                      : currentStreamingTask?.isStreaming.value &&
                          message.id === currentStreamingTask?.taskId
                        ? '输出中...'
                        : '播放语音'
                  }}
                </button>

                <!-- 智能播放按钮 -->
                <button
                  v-if="smartSwitch"
                  :disabled="
                    message.isPlaying ||
                    (currentStreamingTask?.isStreaming.value &&
                      message.id === currentStreamingTask?.taskId)
                  "
                  class="px-12px py-6px bg-[#4caf50] text-white rounded-6px text-12px font-medium disabled:bg-[#ccc] disabled:cursor-not-allowed hover:bg-[#388e3c] transition-colors flex items-center gap-4px"
                  @click="handleSmartPlayMessage(message)"
                >
                  <span v-if="message.isPlaying">🔄</span>
                  <span
                    v-else-if="
                      currentStreamingTask?.isStreaming.value &&
                      message.id === currentStreamingTask?.taskId
                    "
                    >⏳</span
                  >
                  <span v-else>🔄</span>
                  {{
                    message.isPlaying
                      ? '播放中...'
                      : currentStreamingTask?.isStreaming.value &&
                          message.id === currentStreamingTask?.taskId
                        ? '输出中...'
                        : '智能播放'
                  }}
                </button>

                <!-- 停止按钮 -->
                <button
                  :disabled="!message.isPlaying"
                  class="px-12px py-6px bg-[#f44336] text-white rounded-6px text-12px font-medium disabled:bg-[#ccc] disabled:cursor-not-allowed hover:bg-[#d32f2f] transition-colors"
                  @click="handleStopMessage(message)"
                >
                  ⏹️ 停止
                </button>
              </div>

              <!-- 字符统计 -->
              <span class="text-12px text-[#666]">
                {{ message.content.length }} 字符
              </span>
            </div>

            <!-- 音频播放器 -->
            <div
              v-if="message.audioUrl"
              class="mt-16px p-12px bg-[#f0f8ff] rounded-6px border border-[#2196f3]"
            >
              <div class="text-12px text-[#666] mb-8px">🎵 生成的音频:</div>
              <audio
                :src="message.audioUrl"
                controls
                class="w-full h-32px"
                @play="onAudioPlay(message)"
                @pause="onAudioPause(message)"
                @ended="onAudioEnded(message)"
                @error="onAudioError(message)"
              ></audio>
            </div>

            <!-- 段落信息 -->
            <div
              v-if="
                showDetails && message.segments && message.segments.length > 0
              "
              class="mt-16px"
            >
              <div class="text-12px text-[#666] mb-8px">📄 段落处理进度:</div>
              <div class="space-y-4px">
                <div
                  v-for="segment in message.segments"
                  :key="segment.index"
                  class="flex items-center justify-between p-8px bg-[#f8f9fa] rounded-4px border-l-3px"
                  :class="{
                    'border-[#2196f3]': segment.status === 'converting',
                    'border-[#4caf50]': segment.status === 'completed',
                    'border-[#f44336]': segment.status === 'failed',
                    'border-[#ff9800]': segment.status === 'ready',
                    'border-[#9e9e9e]': segment.status === 'pending',
                  }"
                >
                  <span class="text-12px text-[#333] flex-1 mr-8px truncate">
                    {{ segment.text }}
                  </span>
                  <span
                    class="text-10px px-6px py-1px rounded-3px text-white"
                    :class="{
                      'bg-[#2196f3]': segment.status === 'converting',
                      'bg-[#4caf50]': segment.status === 'completed',
                      'bg-[#f44336]': segment.status === 'failed',
                      'bg-[#ff9800]': segment.status === 'ready',
                      'bg-[#9e9e9e]': segment.status === 'pending',
                    }"
                  >
                    {{ getSegmentStatusText(segment.status) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 全局控制 -->
      <div class="mb-24px p-16px bg-[#fff3e0] rounded-8px">
        <h3 class="text-16px font-medium mb-12px text-[#333]">🎛️ 全局控制</h3>
        <div class="grid grid-cols-2 gap-12px">
          <button
            class="px-16px py-8px bg-[#f44336] text-white rounded-6px text-14px font-medium hover:bg-[#d32f2f] transition-colors"
            @click="handleStopAll"
          >
            ⏹️ 停止所有播放
          </button>
          <button
            class="px-16px py-8px bg-[#9e9e9e] text-white rounded-6px text-14px font-medium hover:bg-[#757575] transition-colors"
            @click="handleResetAll"
          >
            🔄 重置所有状态
          </button>
          <button
            v-if="smartSwitch"
            class="px-16px py-8px bg-[#ff9800] text-white rounded-6px text-14px font-medium hover:bg-[#f57c00] transition-colors"
            @click="handleDemoTaskSwitch"
          >
            🚀 演示任务切换
          </button>
          <button
            class="px-16px py-8px bg-[#2196f3] text-white rounded-6px text-14px font-medium hover:bg-[#1976d2] transition-colors"
            @click="handlePlayAllSequence"
          >
            📢 播放全部消息
          </button>
          <button
            :disabled="currentStreamingTask?.isStreaming.value"
            class="px-16px py-8px bg-[#9c27b0] text-white rounded-6px text-14px font-medium hover:bg-[#7b1fa2] transition-colors disabled:bg-[#ccc] disabled:cursor-not-allowed"
            @click="handleStartStreamingDemo"
          >
            🚀
            {{
              currentStreamingTask?.isStreaming.value
                ? '流式输出中...'
                : '流式对话演示'
            }}
          </button>
          <button
            class="px-16px py-8px bg-[#ff5722] text-white rounded-6px text-14px font-medium hover:bg-[#e64a19] transition-colors"
            @click="handleClearStreamingState"
          >
            🧹 清理流式状态
          </button>
        </div>
      </div>

      <!-- 状态显示 -->
      <div class="mb-24px p-16px bg-[#e7f3ff] rounded-8px">
        <h3 class="text-16px font-medium mb-8px text-[#333]">📊 系统状态</h3>
        <div class="grid grid-cols-2 gap-16px text-14px">
          <div class="flex justify-between">
            <span class="text-[#666]">活跃任务数：</span>
            <span class="text-[#333] font-medium">{{ activeTasksCount }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-[#666]">正在播放：</span>
            <span :class="isAnyPlaying ? 'text-[#4caf50]' : 'text-[#666]'">
              {{ isAnyPlaying ? '🎵 是' : '⏸️ 否' }}
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-[#666]">流式输出：</span>
            <span
              :class="
                currentStreamingTask?.isStreaming.value
                  ? 'text-[#ff9800]'
                  : 'text-[#666]'
              "
            >
              {{
                currentStreamingTask?.isStreaming.value ? '🚀 进行中' : '⏸️ 无'
              }}
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-[#666]">输出进度：</span>
            <span class="text-[#333] font-medium">
              {{
                currentStreamingTask?.isStreaming.value
                  ? `${currentStreamingTask?.progress.value.toFixed(1)}%`
                  : '0%'
              }}
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-[#666]">总段落数：</span>
            <span class="text-[#333] font-medium">{{ totalSegments }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-[#666]">完成段落：</span>
            <span class="text-[#333] font-medium">{{ completedSegments }}</span>
          </div>
        </div>
      </div>

      <!-- 调试日志 -->
      <details class="mt-24px">
        <summary
          class="text-16px font-medium cursor-pointer text-[#333] mb-8px flex items-center justify-between"
        >
          <span>🔍 调试日志 ({{ debugLogs.length }} 条)</span>
          <div class="flex items-center gap-8px">
            <button
              class="px-12px py-4px bg-[#2196f3] text-white rounded-4px text-12px font-medium hover:bg-[#1976d2] transition-colors"
              @click.stop="handleCopyLogs"
            >
              📋 复制日志
            </button>
            <button
              class="px-12px py-4px bg-[#f44336] text-white rounded-4px text-12px font-medium hover:bg-[#d32f2f] transition-colors"
              @click.stop="handleClearLogs"
            >
              🗑️ 清空日志
            </button>
          </div>
        </summary>
        <div
          class="h-200px overflow-auto bg-[#1e1e1e] text-[#00ff00] p-12px rounded-6px text-12px font-mono relative"
        >
          <!-- 复制成功提示 -->
          <div
            v-if="copySuccess"
            class="absolute top-12px right-12px bg-[#4caf50] text-white px-8px py-4px rounded-4px text-10px z-10 animate-fade"
          >
            ✅ 已复制到剪贴板
          </div>
          <div
            v-for="(log, index) in debugLogs.slice(-50)"
            :key="index"
            class="mb-2px"
          >
            {{ log }}
          </div>
          <div
            v-if="debugLogs.length === 0"
            class="text-[#666] text-center py-32px"
          >
            暂无日志记录
          </div>
        </div>
      </details>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  useStreamTTSPlayer,
  type IStreamingTask,
} from '@/views/aiEn/home/<USER>/hooks/useStreamTTSPlayer'
import { useAiSettingStore } from '@/stores/modules/aiEnSetting'

// Store
const aiSettingStore = useAiSettingStore()

// 消息接口
interface IMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: string
  isPlaying: boolean
  hasError: boolean
  isCompleted: boolean
  audioUrl?: string
  segments?: any[]
}

// 预设消息数据
const defaultMessages: IMessage[] = [
  {
    id: 'msg1',
    type: 'user',
    content: '你好，我想学习英语口语，有什么好的建议吗？',
    timestamp: '10:00',
    isPlaying: false,
    hasError: false,
    isCompleted: false,
  },
  {
    id: 'msg2',
    type: 'assistant',
    content:
      'Hello! Learning English speaking skills is a great goal. I recommend starting with daily practice, listening to English podcasts, and speaking with native speakers. You can also try reading aloud and recording yourself to improve pronunciation.',
    timestamp: '10:01',
    isPlaying: false,
    hasError: false,
    isCompleted: false,
  },
  {
    id: 'msg3',
    type: 'user',
    content: '那我应该从哪些基础开始练习呢？',
    timestamp: '10:02',
    isPlaying: false,
    hasError: false,
    isCompleted: false,
  },
  {
    id: 'msg4',
    type: 'assistant',
    content:
      'Great question! For beginners, I suggest starting with basic vocabulary, common phrases, pronunciation practice, listening exercises, and simple conversations. Focus on clear pronunciation and building confidence.',
    timestamp: '10:03',
    isPlaying: false,
    hasError: false,
    isCompleted: false,
  },
]

// 流式输出的完整文本
const streamingText = `你好! I'd be happy to help you improve your English speaking skills. Here's a comprehensive approach that works for many learners:

`

// 响应式数据
let messages = $ref<IMessage[]>(JSON.parse(JSON.stringify(defaultMessages)))
let selectedSpeed = $ref('1.0')
let autoPlay = $ref(true)
let smartSwitch = $ref(true) // 智能切换模式，默认开启
let showDetails = $ref(true) // 显示详细信息，默认开启
let copySuccess = $ref(false)

// 流式输出任务实例（由useStreamTTSPlayer管理）
let currentStreamingTask: IStreamingTask | null = null

// TTS播放器
const streamTTSPlayer = useStreamTTSPlayer({
  onTaskStart: (taskId) => {
    console.log(`🎬 TTS任务开始: ${taskId}`)
    const message = findMessageById(taskId)
    if (message) {
      message.isPlaying = true
      message.hasError = false
    }
  },
  onTaskComplete: (taskId, summary) => {
    console.log(`🏁 TTS任务完成: ${taskId}`, summary)
    const message = findMessageById(taskId)
    if (message) {
      message.isPlaying = false
      message.isCompleted = true
    }
  },
  onTaskPlayStateChange: (taskId, playing) => {
    console.log(`🔄 任务 ${taskId} 播放状态: ${playing}`)
    const message = findMessageById(taskId)
    if (message) {
      message.isPlaying = playing
    }
  },
  onSegmentReady: (segment, index, taskId) => {
    console.log(`📄 段落就绪: ${index} - ${segment.substring(0, 20)}...`)
    const message = findMessageById(taskId)
    if (message) {
      updateMessageSegments(message)

      // 获取第一个段落的音频URL作为展示
      const segments = streamTTSPlayer.segments.value
      const firstSegment = segments.find(
        (seg) => seg.taskId === taskId && seg.audioUrl,
      )
      if (firstSegment && firstSegment.audioUrl && !message.audioUrl) {
        message.audioUrl = firstSegment.audioUrl
      }
    }
  },
  onPlayStart: (index, taskId) => {
    console.log(`▶️ 开始播放段落: ${index}`)
  },
  onPlayEnd: (index, taskId) => {
    console.log(`⏹️ 结束播放段落: ${index}`)
  },
  onConvertComplete: (index, success, taskId) => {
    console.log(`🔄 段落 ${index} 转换完成: ${success}`)
    const message = findMessageById(taskId)
    if (message) {
      if (!success) {
        message.hasError = true
      }
      updateMessageSegments(message)
    }
  },
})

// 计算属性
const debugLogs = streamTTSPlayer.debugLogs
const isAnyPlaying = computed(() => messages.some((msg) => msg.isPlaying))
const activeTasksCount = computed(
  () => messages.filter((msg) => msg.isPlaying).length,
)
const totalSegments = computed(() => streamTTSPlayer.totalSegments.value)
const completedSegments = computed(
  () => streamTTSPlayer.completedSegments.value,
)

// 工具函数
function findMessageById(id: string): IMessage | undefined {
  return messages.find((msg) => msg.id === id)
}

function updateMessageSegments(message: IMessage): void {
  const segments = streamTTSPlayer.segments.value.filter(
    (seg) => seg.taskId === message.id,
  )
  message.segments = segments
}

function getSegmentStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    pending: '等待',
    converting: '转换中',
    ready: '就绪',
    playing: '播放中',
    completed: '完成',
    failed: '失败',
  }
  return statusMap[status] || status
}

// 操作方法
function handlePlayMessage(message: IMessage): void {
  if (message.isPlaying) {
    return
  }

  console.log(`🎵 播放消息: ${message.id}`)

  // 更新Store中的设置
  aiSettingStore.config.voiceSpeed = selectedSpeed
  aiSettingStore.config.voiceAutoPlayStatus = autoPlay

  // 重置消息状态
  message.hasError = false
  message.isCompleted = false
  message.audioUrl = undefined
  message.segments = []

  // 启动TTS任务
  streamTTSPlayer.startTask(message.id)
  streamTTSPlayer.addTextToTask(message.id, message.content)
  streamTTSPlayer.finishTask(message.id)
}

/** 智能播放消息（支持任务切换） */
function handleSmartPlayMessage(message: IMessage): void {
  if (message.isPlaying) {
    return
  }

  console.log(`🔄 智能播放消息: ${message.id}`)

  // 更新Store中的设置
  aiSettingStore.config.voiceSpeed = selectedSpeed
  aiSettingStore.config.voiceAutoPlayStatus = autoPlay

  // 先使用智能切换功能（这会自动处理其他任务的取消）
  streamTTSPlayer.switchToTask(message.id)

  // 在切换完成后，重置当前消息状态并启动新任务
  setTimeout(() => {
    // 重置当前消息状态
    message.hasError = false
    message.isCompleted = false
    message.audioUrl = undefined
    message.segments = []

    // 启动TTS任务
    streamTTSPlayer.startTask(message.id)
    streamTTSPlayer.addTextToTask(message.id, message.content)
    streamTTSPlayer.finishTask(message.id)
  }, 50) // 短暂延迟确保切换操作完成
}

function handleStopMessage(message: IMessage): void {
  console.log(`🛑 停止消息: ${message.id}`)
  streamTTSPlayer.cancelTask(message.id)
  message.isPlaying = false
}

function handleStopAll(): void {
  console.log('🛑 停止所有播放')
  streamTTSPlayer.stopAllPlayback()
  messages.forEach((msg) => {
    msg.isPlaying = false
  })
}

function handleResetAll(): void {
  console.log('🔄 重置所有状态')
  streamTTSPlayer.clearAll()
  messages.forEach((msg) => {
    msg.isPlaying = false
    msg.hasError = false
    msg.isCompleted = false
    msg.audioUrl = undefined
    msg.segments = []
  })
}

/** 演示任务切换 */
function handleDemoTaskSwitch(): void {
  console.log('🚀 开始任务切换演示')

  // 先播放最长的消息（msg4）
  const longMessage = messages.find((msg) => msg.id === 'msg4')
  if (longMessage) {
    console.log('🎵 开始播放长消息...')
    handleSmartPlayMessage(longMessage)

    // 2秒后切换到短消息
    setTimeout(() => {
      const shortMessage = messages.find((msg) => msg.id === 'msg1')
      if (shortMessage) {
        console.log('🔄 切换到短消息...')
        handleSmartPlayMessage(shortMessage)
      }
    }, 2000)
  }
}

/** 播放全部消息序列 */
function handlePlayAllSequence(): void {
  console.log('📢 开始播放全部消息序列')

  let currentIndex = 0
  const playNext = () => {
    if (currentIndex >= messages.length) {
      console.log('📢 全部消息播放完成')
      return
    }

    const message = messages[currentIndex]
    console.log(`📢 播放第 ${currentIndex + 1} 条消息: ${message.id}`)

    if (smartSwitch) {
      handleSmartPlayMessage(message)
    } else {
      handlePlayMessage(message)
    }

    currentIndex++

    // 等待3秒后播放下一条（给用户时间观察）
    setTimeout(playNext, 3000)
  }

  // 重置所有状态后开始播放
  handleResetAll()
  setTimeout(playNext, 500)
}

/** 开始流式对话演示 */
function handleStartStreamingDemo(): void {
  if (currentStreamingTask?.isStreaming.value) {
    return
  }

  console.log('🚀 开始流式对话演示')

  // 清理旧的流式消息
  messages = messages.filter(
    (msg: IMessage) => !msg.id.startsWith('stream_'),
  ) as IMessage[]
  console.log('🗑️ 已清理旧的流式消息')

  // 更新Store设置
  aiSettingStore.config.voiceSpeed = selectedSpeed
  aiSettingStore.config.voiceAutoPlayStatus = autoPlay

  // 使用新的简化API启动流式任务
  currentStreamingTask = streamTTSPlayer.startStreamingTask({
    autoSwitch: smartSwitch,
    chunkSize: 8,
    delay: 50,
    autoPlay: true,
  })

  // 创建对应的消息对象用于UI显示
  const newMessage: IMessage = {
    id: currentStreamingTask.taskId,
    type: 'assistant',
    content: '',
    timestamp: new Date().toLocaleTimeString(),
    isPlaying: false,
    hasError: false,
    isCompleted: false,
    segments: [],
  }

  messages.push(newMessage)

  // 开始流式输出完整文本
  setTimeout(() => {
    if (currentStreamingTask) {
      // 模拟分块发送流式文本（真实场景中这会来自WebSocket）
      const chunks = []
      const chunkSize = 50 // 模拟较大的文本块
      for (let i = 0; i < streamingText.length; i += chunkSize) {
        chunks.push(streamingText.substring(i, i + chunkSize))
      }

      // 模拟流式发送，同时更新消息内容
      let chunkIndex = 0
      const sendNextChunk = () => {
        if (chunkIndex < chunks.length && currentStreamingTask) {
          currentStreamingTask.addText(chunks[chunkIndex])
          // 同步更新消息内容用于显示
          newMessage.content = currentStreamingTask.currentText.value
          chunkIndex++
          setTimeout(sendNextChunk, 200) // 模拟网络延迟
        } else if (currentStreamingTask) {
          // 完成流式输出
          currentStreamingTask.finish()
          newMessage.content = currentStreamingTask.currentText.value
        }
      }
      sendNextChunk()
    }
  }, 100)
}

/** 清理流式状态 */
function handleClearStreamingState(): void {
  console.log('🧹 清理流式状态')

  // 取消当前流式任务
  if (currentStreamingTask) {
    console.log('❌ 取消流式任务:', currentStreamingTask.taskId)
    currentStreamingTask.cancel()
  }

  // 重置所有流式状态
  currentStreamingTask = null

  // 清理所有流式消息
  messages = messages.filter((msg) => !msg.id.startsWith('stream_'))

  console.log('✅ 流式状态已清理')
}

/** 复制调试日志到剪贴板 */
async function handleCopyLogs(): Promise<void> {
  try {
    const logsText = debugLogs.map((log) => log.value || log).join('\n')
    if (!logsText) {
      console.warn('没有日志可复制')
      return
    }

    await navigator.clipboard.writeText(logsText)
    copySuccess = true
    console.log('📋 日志已复制到剪贴板')

    // 3秒后隐藏提示
    setTimeout(() => {
      copySuccess = false
    }, 3000)
  } catch (error) {
    console.error('📋 复制日志失败:', error)
    // 兜底方案：尝试使用旧方法
    try {
      const textarea = document.createElement('textarea')
      textarea.value = debugLogs.map((log) => log.value || log).join('\n')
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
      copySuccess = true
      setTimeout(() => {
        copySuccess = false
      }, 3000)
    } catch (fallbackError) {
      console.error('📋 兜底复制方案也失败:', fallbackError)
    }
  }
}

/** 清空调试日志 */
function handleClearLogs(): void {
  console.log('🗑️ 清空调试日志')
  // 由于debugLogs是从streamTTSPlayer来的，我们重新初始化组件来清空日志
  location.reload()
}

// 音频事件处理
function onAudioPlay(message: IMessage): void {
  console.log(`🎵 消息 ${message.id} 音频开始播放`)
}

function onAudioPause(message: IMessage): void {
  console.log(`🎵 消息 ${message.id} 音频暂停`)
}

function onAudioEnded(message: IMessage): void {
  console.log(`🎵 消息 ${message.id} 音频播放结束`)
}

function onAudioError(message: IMessage): void {
  console.error(`🎵 消息 ${message.id} 音频播放错误`)
  message.hasError = true
}

// 页面卸载时清理
onUnmounted(() => {
  streamTTSPlayer.clearAll()
})
</script>

<style lang="scss" scoped>
/* 自定义样式 */
details[open] summary {
  margin-bottom: 8px;
}

summary:hover {
  color: #2196f3;
}

/* 滚动条样式 */
.overflow-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 消息卡片动画 */
.space-y-16px > * {
  transition: all 0.3s ease;
}

.space-y-16px > *:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 复制成功提示动画 */
.animate-fade {
  animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  10%,
  90% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-10px);
  }
}

/* 流式输出光标动画 */
.animate-pulse {
  animation: pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}
</style>
