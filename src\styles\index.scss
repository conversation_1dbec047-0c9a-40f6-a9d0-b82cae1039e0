@import './normalize.scss';
@import './variables/variables.module.scss';
@import './tool.scss';
@import './vant.reset.scss';

/* tailwindcss */
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  box-sizing: border-box;
}

html,
body,
#app {
  height: 100vh;
  background: #f6f8fa;
  overflow: auto;
  font-size: 14px;
  &::-webkit-scrollbar {
    display: none;
  }
  -webkit-overflow-scrolling: touch;
  color: #333;
}

body {
  overflow: hidden;
}

@font-face {
  font-family: 'AlimamaShuHeiTi';
  src: url(./fonts/AlimamaShuHeiTi-Bold.otf);
}

@font-face {
  font-family: 'D-DIN-Bold';
  src: url(./fonts/D-DIN-Bold.otf);
}
