// @ts-nocheck
import { savePcmWav } from '@/api/aiEn'
import OSS from '@/plugins/AiEnOSS'

/**
 * 阿里云语音识别 hooks
 */
export const useAliASR = (
  {
    onconnected,
    onmessage,
    onstop,
    onerror,
    onsentenceend,
    onRecordComplete,
  }: {
    onconnected?: () => void
    onmessage?: (params: any) => void
    onstop?: () => void
    onerror?: () => void
    onsentenceend?: (params: any) => void
    onRecordComplete?: (data: { sentences: any[]; audioUrl?: string }) => void
  },
  options: {
    from?: string
    to?: string
    needUpload?: boolean
    minRecordingDuration?: number
    conversationId?: any
    messageId?: any
    isCancel?: boolean
  } = {},
) => {
  // ===================== 状态变量 =====================
  const oss = new OSS()
  let tempSentenceArr: any[] = []
  let allPcmData: Int16Array[] = []
  let isTouchDown = false
  let isSentenceEnd = true
  let aliAudioUrl = $ref('')
  const serialNumber = String(Date.now())
  const deviceMicMsg = ref('')
  const ws = ref<WebSocket | null>(null)
  const isConnected = ref(false)
  const isAliRealTimeRecording = ref(false)
  const mediaStream = ref<MediaStream | null>(null)
  const audioContext = ref<AudioContext | null>(null)
  const audioInputStream = ref<MediaStreamAudioSourceNode | null>(null)
  const audioProcessor = ref<ScriptProcessorNode | null>(null)
  const firstConnectTime = ref<number | null>(null)
  const recordingStartTime = ref<number | null>(null)

  // ===================== WebSocket相关 =====================
  const connect = async (onopen?) => {
    try {
      const url =
        'wss:' +
        import.meta.env.VITE_APP_BASE_API5 +
        '/aienglish/chat/translate/websocket'
      ws.value = new WebSocket(url)
      ws.value.onopen = () => {
        if (!firstConnectTime.value) firstConnectTime.value = Date.now()
        wsSend(1)
      }
      ws.value.onmessage = (event) => handleWsMessage(event, onopen)
      ws.value.onerror = (error) => {
        console.error('WebSocket错误:', error)
        $g.showToast('WebSocket错误，请重试')
        disconnect(true)
      }
      ws.value.onclose = () => {
        isConnected.value = false
      }
    } catch (error) {
      onerror?.()
      console.error('初始化失败:', error)
    }
  }

  function wsSend(messageType, data = null) {
    if (!ws.value) return
    const frame = {
      messageType,
      serialNumber,
      contentType: 2,
      data,
      from: options.from,
      to: options.to,
    }
    ws.value.send(JSON.stringify(frame))
  }

  function handleWsMessage(event, onopen?) {
    const dataObj = JSON.parse(event.data)
    onmessage?.(dataObj)
    if (dataObj.code == 1) {
      isConnected.value = true
      onopen?.()
    } else if (dataObj.code == 2) {
      if (dataObj?.data.sentenceEnd) {
        isSentenceEnd = true
        onsentenceend?.(dataObj?.data)
        tempSentenceArr.push(dataObj.data)
        if (!isAliRealTimeRecording.value && !options.isCancel) {
          if (!options.needUpload) {
            onRecordComplete?.({
              sentences: tempSentenceArr,
              audioUrl: aliAudioUrl,
            })
          }
          const audioBlob = getAudioBlob()
          if (
            options.needUpload &&
            audioBlob &&
            audioBlob.size > 0 &&
            isConnected.value
          ) {
            uploadToOSS(audioBlob)
          }
        }
      } else {
        isSentenceEnd = false
      }
    } else if (dataObj.code == 3) {
      disconnect(true)
    } else if (dataObj.code == 5) {
      $g.showToast(dataObj.message + ',请重试')
      disconnect(false)
    }
  }

  const disconnect = (clearFirstConnectTime = false) => {
    end()
    ws.value?.close()
    ws.value = null
    isConnected.value = false
    if (clearFirstConnectTime) firstConnectTime.value = null
  }

  // ===================== 录音相关 =====================
  const startAliRealTimeRecording = async () => {
    try {
      isTouchDown = true
      tempSentenceArr = []
      allPcmData = []
      recordingStartTime.value = Date.now()
      if (!mediaStream.value) {
        const res = await initMedia()
        if (!res) return
      } else {
        mediaStream.value.getTracks().forEach((track) => (track.enabled = true))
      }
      if (!ws.value) connect()
      isAliRealTimeRecording.value = true
      onconnected?.()
      if (ws.value?.readyState === WebSocket.OPEN) wsSend(1)
    } catch (error) {
      onerror?.()
      isAliRealTimeRecording.value = false
      console.error('开启录音失败:', error)
    }
  }

  const stopAliRealTimeRecording = async () => {
    try {
      isTouchDown = false
      const recordingDuration = recordingStartTime.value
        ? Date.now() - recordingStartTime.value
        : 0

      if (
        recordingDuration < options.minRecordingDuration &&
        !options.isCancel
      ) {
        $g.showToast('说话时间太短')
        recordingStartTime.value = null
        isAliRealTimeRecording.value = false
        onRecordComplete?.({ sentences: [], audioUrl: '' })
        await end()
        return
      }

      // 暂停所有音轨
      if (mediaStream.value) {
        mediaStream.value
          .getTracks()
          .forEach((track) => (track.enabled = false))
      }
      isAliRealTimeRecording.value = false
      onstop?.()
      if (isSentenceEnd) {
        if (!tempSentenceArr.length && !options.isCancel) {
          $g.showToast('未识别到语音')
        }
        onRecordComplete?.({
          sentences: tempSentenceArr,
          audioUrl: aliAudioUrl,
        })
      }
      recordingStartTime.value = null
      end()
    } catch (error) {
      onerror?.()
      console.error('停止录音失败:', error)
    }
  }

  async function end() {
    // 断开音频处理链
    if (audioProcessor.value) {
      audioProcessor.value.disconnect()
      audioInputStream.value?.disconnect()
    }

    // 停止所有音轨
    if (mediaStream.value) {
      mediaStream.value.getTracks().forEach((track) => track.stop())
    }

    // 关闭音频上下文
    if (audioContext.value) {
      await audioContext.value.close()
    }

    // 发送停止识别指令
    if (ws.value?.readyState === WebSocket.OPEN) {
      wsSend(3)
    }

    // 清理所有引用
    audioInputStream.value = null
    audioProcessor.value = null
    mediaStream.value = null
    audioContext.value = null
    isAliRealTimeRecording.value = false
    recordingStartTime.value = null
  }

  async function initMedia() {
    try {
      // 获取麦克风权限和音频流
      mediaStream.value = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000, // 采样率必须为16000
          channelCount: 1, // 单声道
          echoCancellation: true, // 回声消除
          noiseSuppression: true, // 噪声抑制
          autoGainControl: true, // 自动增益控制
        },
      })
      if (!isTouchDown) {
        audioContext.value = new window.AudioContext()
        await audioContext.value.resume()
        // 这一步操作是为了第一次授权时，解决系统弹窗阻塞代码
        mediaStream.value = null
        return false
      }
      deviceMicMsg.value = ''

      // 初始化音频上下文
      audioContext.value = new AudioContext({
        sampleRate: 16000,
      })
      // 创建并保存音频输入流
      audioInputStream.value = audioContext.value.createMediaStreamSource(
        mediaStream.value,
      )

      // 创建并保存音频处理器
      audioProcessor.value = audioContext.value.createScriptProcessor(
        1024,
        1,
        1,
      )
      audioProcessor.value.onaudioprocess = (event) => {
        if (!ws.value) return

        // 获取原始音频数据
        const rawAudioData = event.inputBuffer.getChannelData(0)

        // 转换为16位PCM数据
        const pcmAudioData = new Int16Array(rawAudioData.length)
        for (let i = 0; i < rawAudioData.length; i++) {
          // 限制音频采样值在 -1 到 1 之间
          const normalizedSample = Math.max(-1, Math.min(1, rawAudioData[i]))
          // 转换为16位整数 (-32768 到 32767)
          pcmAudioData[i] =
            normalizedSample < 0
              ? normalizedSample * 0x8000 // 负数处理
              : normalizedSample * 0x7fff // 正数处理
        }
        // 收集PCM数据
        allPcmData.push(pcmAudioData)
        if (ws.value?.readyState === WebSocket.OPEN) {
          wsSend(2, arrayBufferToBase64(pcmAudioData.buffer))
        }
      }

      // 连接音频处理链
      audioInputStream.value.connect(audioProcessor.value)
      audioProcessor.value.connect(audioContext.value.destination)
      return true
    } catch (e) {
      if (e.name.includes('NotAllowedError')) {
        $g.showToast('请先开启麦克风权限')
        deviceMicMsg.value = '请先开启麦克风权限'
      } else if (
        e.name.includes('NotFoundError') ||
        e.name.includes('NotReadableError')
      ) {
        $g.showToast('没有可用的麦克风设备')
        deviceMicMsg.value = '没有可用的麦克风设备'
      } else {
        $g.showToast('麦克风功能不可用')
        deviceMicMsg.value = '麦克风功能不可用'
      }
      console.error(e)
      return false
    }
  }

  // ===================== 工具函数 =====================
  function arrayBufferToBase64(buffer) {
    let binary = ''
    const bytes = new Uint8Array(buffer)
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return window.btoa(binary)
  }

  function getAudioBlob() {
    // 合并所有 Int16Array
    const totalLength = allPcmData.reduce((sum, arr) => sum + arr.length, 0)
    const merged = new Int16Array(totalLength)
    let offset = 0
    for (const arr of allPcmData) {
      merged.set(arr, offset)
      offset += arr.length
    }

    // 生成 WAV 文件头
    const wavBuffer = encodeWAV(merged, 16000) // 16000为采样率，根据实际情况调整
    return new Blob([wavBuffer], { type: 'audio/wav' })
  }

  // PCM转WAV
  function encodeWAV(samples, sampleRate) {
    const buffer = new ArrayBuffer(44 + samples.length * 2)
    const view = new DataView(buffer)
    function writeString(view, offset, string) {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i))
      }
    }

    // RIFF identifier
    writeString(view, 0, 'RIFF')
    // file length
    view.setUint32(4, 36 + samples.length * 2, true)
    // RIFF type
    writeString(view, 8, 'WAVE')
    // format chunk identifier
    writeString(view, 12, 'fmt ')
    // format chunk length
    view.setUint32(16, 16, true)
    // sample format (raw)
    view.setUint16(20, 1, true)
    // channel count
    view.setUint16(22, 1, true)
    // sample rate
    view.setUint32(24, sampleRate, true)
    // byte rate (sample rate * block align)
    view.setUint32(28, sampleRate * 2, true)
    // block align (channel count * bytes per sample)
    view.setUint16(32, 2, true)
    // bits per sample
    view.setUint16(34, 16, true)
    // data chunk identifier
    writeString(view, 36, 'data')
    // data chunk length
    view.setUint32(40, samples.length * 2, true)

    // PCM samples
    let offset = 44
    for (let i = 0; i < samples.length; i++, offset += 2) {
      view.setInt16(offset, samples[i], true)
    }
    return view.buffer
  }

  async function uploadToOSS(audioBlob: Blob) {
    if (!options.needUpload) return
    try {
      const fileName = `audio_${Date.now()}.wav`
      const result = await oss.uploadFile({
        file: audioBlob,
        id: fileName,
        name: fileName,
      })
      aliAudioUrl = result?.fullUrl || ''
      onRecordComplete?.({
        sentences: tempSentenceArr,
        audioUrl: aliAudioUrl,
      })
      // 延迟600毫秒，避免接口提示该conversationId信息不存在的问题
      setTimeout(() => {
        savePcmWav({
          conversationId: options.conversationId,
          messageId: options.messageId,
          audioList: [
            {
              convertType: 2,
              content: tempSentenceArr?.[0]?.fromContent,
              audio: aliAudioUrl,
            },
          ],
        })
      }, 600)
    } catch (e) {
      console.error('音频上传失败', e)
    }
  }

  // ===================== 生命周期 =====================
  onBeforeMount(() => {
    initMedia()
  })
  onUnmounted(() => {
    disconnect(true)
  })

  // ===================== 导出API =====================
  return {
    deviceMicMsg,
    isConnected,
    isAliRealTimeRecording,
    connect,
    disconnect,
    startAliRealTimeRecording,
    stopAliRealTimeRecording,
  }
}
