<template>
  <div>
    <g-navbar :customTitle="route.query.title" customBackGround="transparent">
    </g-navbar>
    <div
      class="px-[12px] py-[16px] overflow-y-auto no-bar"
      :style="{
        height: `calc(100vh - ${useSettingStore().navBarTotalHeight}px - ${useSettingStore().navigationHeight}px)`,
      }"
    >
      <g-markdown :text="route.query.content || '数据错误！'" mode="preview">
      </g-markdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSettingStore } from '@/stores/modules/setting'
const route = useRoute()
</script>

<style lang="scss" scoped></style>
