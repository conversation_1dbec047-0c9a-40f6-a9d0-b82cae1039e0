/**
 * AI响应匀速输出Hook
 * 提供平滑的文本输出效果，避免输出节奏不均匀
 */

/** 输出速度预设 */
export const SPEED_PRESETS = {
  /** 慢速：20字符/秒 */
  slow: 20,
  /** 正常：40字符/秒 */
  normal: 40,
  /** 快速：60字符/秒 */
  fast: 60,
} as const

/** 匀速输出配置类型 */
export type TSmoothOutputConfig =
  | boolean // true表示启用默认配置
  | 'slow'
  | 'normal'
  | 'fast' // 速度预设
  | {
      /** 输出速度（字符/秒） */
      speed?: number
      /** 分割策略 */
      splitStrategy?: 'character' | 'word' | 'smart'
      /** 最大缓冲区大小 */
      maxBufferSize?: number
    }

/** 内部配置接口 */
interface ISmoothOutputInternalConfig {
  speed: number
  splitStrategy: 'character' | 'word' | 'smart'
  maxBufferSize: number
}

/**
 * 解析配置
 * @param config 用户配置
 */
function parseConfig(config: TSmoothOutputConfig): ISmoothOutputInternalConfig {
  // 默认配置
  const defaultConfig: ISmoothOutputInternalConfig = {
    speed: SPEED_PRESETS.normal,
    splitStrategy: 'smart',
    maxBufferSize: 10000,
  }

  if (config === true) {
    return defaultConfig
  }

  if (typeof config === 'string') {
    return {
      ...defaultConfig,
      speed: SPEED_PRESETS[config] || SPEED_PRESETS.normal,
    }
  }

  return {
    ...defaultConfig,
    ...config,
  }
}

/**
 * 智能分割文本
 * @param text 待分割的文本
 * @param strategy 分割策略
 */
function splitText(
  text: string,
  strategy: 'character' | 'word' | 'smart',
): string[] {
  switch (strategy) {
    case 'character':
      return text.split('')

    case 'word':
      // 按词汇分割（支持中英文）
      return (
        text.match(
          /[\u4e00-\u9fa5]|[a-zA-Z]+|\d+|[^\u4e00-\u9fa5a-zA-Z\d\s]|\s+/g,
        ) || []
      )

    case 'smart':
      // 智能分割：标点符号、换行符作为分割点，保持完整性
      const chunks: string[] = []
      let currentChunk = ''

      for (let i = 0; i < text.length; i++) {
        const char = text[i]
        currentChunk += char

        // 在标点符号、换行符后分割
        if (/[。！？；，：\n.!?;,:]/g.test(char)) {
          chunks.push(currentChunk)
          currentChunk = ''
        }
        // 避免单个块过长
        else if (currentChunk.length >= 3) {
          chunks.push(currentChunk)
          currentChunk = ''
        }
      }

      // 剩余内容
      if (currentChunk) {
        chunks.push(currentChunk)
      }

      return chunks.filter((chunk) => chunk.trim())

    default:
      return [text]
  }
}

/**
 * 创建匀速输出处理器
 * @param config 配置
 * @param originalOnMessage 原始的onMessage回调
 * @param originalOnComplete 原始的onComplete回调
 */
export function createSmoothOutput(
  config: TSmoothOutputConfig | null | undefined,
  originalOnMessage?: (content: string) => void,
  originalOnComplete?: () => void,
) {
  // 如果没有配置或配置为false，返回原始回调
  if (!config) {
    return {
      onMessage: originalOnMessage,
      onComplete: originalOnComplete,
      cleanup: () => {}, // 空清理函数
    }
  }

  // 解析配置
  const finalConfig = parseConfig(config)

  // 内部状态
  let textBuffer: string[] = []
  let isOutputting = false
  let isCompleted = false
  let timerId: number | null = null

  /**
   * 添加文本到缓冲区
   */
  function addToBuffer(text: string): void {
    if (!text) return

    // 分割文本并添加到缓冲区
    const chunks = splitText(text, finalConfig.splitStrategy)

    // 检查缓冲区大小限制
    const newBufferSize = textBuffer.length + chunks.length
    if (newBufferSize > finalConfig.maxBufferSize) {
      console.warn('文本缓冲区已达到最大限制，部分内容可能被丢弃')
      const maxNewChunks = finalConfig.maxBufferSize - textBuffer.length
      textBuffer.push(...chunks.slice(0, maxNewChunks))
    } else {
      textBuffer.push(...chunks)
    }

    // 如果当前没有在输出，开始输出
    if (!isOutputting && !isCompleted) {
      startOutput()
    }
  }

  /**
   * 开始匀速输出
   */
  function startOutput(): void {
    if (isOutputting || isCompleted) return

    isOutputting = true

    // 计算输出间隔（毫秒）
    const interval = 1000 / finalConfig.speed

    function outputNext() {
      if (textBuffer.length === 0) {
        // 缓冲区为空，等待更多内容或完成
        if (isCompleted) {
          stopOutput()
        } else {
          // 继续等待
          timerId = window.setTimeout(outputNext, interval)
        }
        return
      }

      // 从缓冲区取出下一个文本块
      const nextChunk = textBuffer.shift()
      if (nextChunk) {
        // 触发原始回调
        originalOnMessage?.(nextChunk)
      }

      // 继续输出下一个
      if (isOutputting) {
        timerId = window.setTimeout(outputNext, interval)
      }
    }

    // 开始输出循环
    outputNext()
  }

  /**
   * 停止输出
   */
  function stopOutput(): void {
    isOutputting = false
    if (timerId) {
      clearTimeout(timerId)
      timerId = null
    }
  }

  /**
   * 立即输出缓冲区所有内容
   */
  function flushBuffer(): void {
    if (textBuffer.length > 0) {
      const remainingText = textBuffer.join('')
      textBuffer = []
      originalOnMessage?.(remainingText)
    }
    stopOutput()
  }

  /**
   * 标记完成
   */
  function markCompleted(): void {
    isCompleted = true

    // 如果还有缓冲内容，等待输出完毕
    if (textBuffer.length > 0) {
      // 输出会在缓冲区清空后自动停止，然后调用原始完成回调
      const checkComplete = () => {
        if (textBuffer.length === 0) {
          originalOnComplete?.()
        } else {
          setTimeout(checkComplete, 50) // 50ms后再检查
        }
      }
      checkComplete()
    } else {
      // 立即调用原始完成回调
      originalOnComplete?.()
    }
  }

  /**
   * 清理资源
   */
  function cleanup(): void {
    flushBuffer()
    isCompleted = true
  }

  // 返回处理后的回调函数
  return {
    onMessage: addToBuffer,
    onComplete: markCompleted,
    cleanup,
  }
}
