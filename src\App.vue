<template>
  <div class="app-container">
    <van-config-provider>
      <router-view />
    </van-config-provider>
  </div>
</template>

<script lang="ts" setup>
import { useSettingStore } from '@/stores/modules/setting'
const settingStore = useSettingStore()

if ($g.inApp) {
  $g.flutter('padding').then((res) => {
    var TopAdaptationDistance = document.getElementById('TopAdaptationDistance')
    if (TopAdaptationDistance)
      TopAdaptationDistance.style.height = res?.length
        ? `${res?.[1]}px`
        : '48px'
    $g.navigationHeight = res?.[1] ?? 48
    settingStore.navigationHeight = res?.[1] ?? 48
  })
}
</script>

<style lang="scss" scoped>
html[data-route-module='ai-en'] .app-container {
  position: relative;
  height: 100%;
  width: 100%;
  background: linear-gradient(180deg, #d8e3ff 30%, rgba(216, 227, 255, 0) 100%);
  background-size: 100% 303px;
  background-repeat: no-repeat;
}
</style>
