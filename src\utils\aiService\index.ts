/**
 * AI API交互服务
 * 基于@cjh0/fetch-event-source封装SSE流式请求
 */

import { fetchEventSource } from '@cjh0/fetch-event-source'
import { useUserStore } from '@/stores/modules/user'
import {
  createSmoothOutput,
  type TSmoothOutputConfig,
} from './hooks/useSmoothOutput'

/** AI消息类型 */
interface IAIMessage {
  /** 角色 */
  role: 'system' | 'user' | 'assistant'
  /** 消息内容 */
  content: string
}

/** AI聊天请求参数 */
interface IAIChatRequest {
  /** 对话ID */
  conversationId: string
  /** 消息内容 */
  content: string
}

/** Ready消息数据结构 */
interface IReadyMessageData {
  /** 请求消息ID */
  requestMessageId: number
  /** 响应消息ID */
  responseMessageId: number
}

/** 流式响应回调函数类型 */
interface IAIStreamCallbacks {
  /** 接收到数据时的回调 */
  onMessage?: (content: string) => void
  /** 流式结束时的回调 */
  onComplete?: () => void
  /** 错误时的回调 */
  onError?: (error: any) => void
  /** 连接打开时的回调 */
  onOpen?: () => void
  /** 接收到ready消息时的回调 */
  onReady?: (data: IReadyMessageData) => void
  /** 匀速输出配置 */
  smoothOutput?: TSmoothOutputConfig
}

/** AI服务配置接口 */
interface IAIServiceConfig {
  url?: string
  /** 默认匀速输出配置 */
  smoothOutput?: TSmoothOutputConfig
}

/** AI服务类 */
class AIService {
  // API基础URL
  private url: string
  // 默认匀速输出配置
  private defaultSmoothOutput?: TSmoothOutputConfig

  /**
   * 构造函数
   * @param config 服务配置
   */
  constructor(config: IAIServiceConfig = {}) {
    this.url = config.url || '' // 聊天接口URL
    this.defaultSmoothOutput = config.smoothOutput
  }

  /**
   * 发送流式聊天请求
   * @param request 聊天请求参数
   * @param callbacks 流式响应回调函数
   * @returns AbortController 用于取消请求
   */
  async streamChat(
    request: IAIChatRequest,
    callbacks: IAIStreamCallbacks = {},
  ): Promise<AbortController> {
    const { onMessage, onComplete, onError, onOpen, onReady, smoothOutput } =
      callbacks
    const controller = new AbortController()

    // 创建匀速输出处理器（优先使用callbacks中的配置，其次使用默认配置）
    const finalSmoothConfig =
      smoothOutput !== undefined ? smoothOutput : this.defaultSmoothOutput
    const smoothProcessor = createSmoothOutput(
      finalSmoothConfig,
      onMessage,
      onComplete,
    )

    // 使用处理后的回调
    const actualOnMessage = smoothProcessor.onMessage
    const actualOnComplete = smoothProcessor.onComplete

    // 获取用户store中的token
    const userStore = useUserStore()
    const { jztToken, version, platform } = storeToRefs(userStore)

    try {
      await fetchEventSource(this.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          token: jztToken.value || '',
          version: version.value || '1.2.2',
          platform: platform.value || 'YUNXIAO_STUDENT_H5',
        },
        body: JSON.stringify(request),
        signal: controller.signal,

        /** 连接打开回调 */
        async onopen(response) {
          if (response.ok) {
            onOpen?.()
            return // 正常流式响应
          } else if (
            response.status >= 400 &&
            response.status < 500 &&
            response.status !== 429
          ) {
            // 客户端错误，不重试
            throw new Error(`HTTP ${response.status}`)
          } else {
            // 其他错误，可能重试
            throw new Error(`HTTP ${response.status}`)
          }
        },

        /** 接收消息回调 */
        onmessage(event) {
          try {
            // 如果数据以data:开头，去掉前缀
            let data = event.data
            if (data.startsWith('data:')) {
              data = data.substring(5)
            }

            // 尝试解析JSON数据
            if (data && data.trim() !== '') {
              try {
                const jsonData = JSON.parse(data)

                // 检查是否为ready消息 - 包含requestMessageId和responseMessageId
                if (
                  typeof jsonData.requestMessageId === 'number' &&
                  typeof jsonData.responseMessageId === 'number'
                ) {
                  const readyData: IReadyMessageData = {
                    requestMessageId: jsonData.requestMessageId,
                    responseMessageId: jsonData.responseMessageId,
                  }
                  onReady?.(readyData)
                  return // ready消息处理完成，不继续处理其他逻辑
                }

                // 根据后端返回的数据结构解析
                // {"conversationId":"xxx","text":"","finished":false}
                if (
                  jsonData.text &&
                  typeof jsonData.text === 'string' &&
                  jsonData.text.trim() !== ''
                ) {
                  actualOnMessage?.(jsonData.text)
                }

                // 检查是否完成 - 无论text是否为空，只要finished为true就结束
                if (jsonData.finished === true) {
                  actualOnComplete?.()
                  return // 确保完成后不再处理后续消息
                }
              } catch (parseError) {
                console.warn(
                  '解析SSE JSON数据失败:',
                  parseError,
                  '原始数据:',
                  data,
                )
              }
            }
          } catch (error) {
            console.warn('解析SSE消息失败:', error)
          }
        },

        /** 错误回调 */
        onerror(error) {
          console.error('SSE连接错误:', error)
          // 清理匀速输出器
          smoothProcessor.cleanup()
          onError?.(error)
          throw error // 停止重连
        },

        /** 关闭回调 */
        onclose() {
          // SSE连接已关闭，清理匀速输出器
          smoothProcessor.cleanup()
        },
      })
    } catch (error) {
      console.error('流式请求失败:', error)
      // 发生错误时清理匀速输出器
      smoothProcessor.cleanup()
      onError?.(error)
    }

    // 为controller添加自定义abort处理
    const originalAbort = controller.abort.bind(controller)
    controller.abort = () => {
      // 取消时清理匀速输出器
      smoothProcessor.cleanup()
      originalAbort()
    }

    return controller
  }
}

// 创建默认AI服务实例（启用快速匀速输出）
export const aiService = new AIService({
  smoothOutput: 'fast', // 60字符/秒的快速输出
})

// 导出类型和服务
export {
  AIService,
  type IAIMessage,
  type IAIChatRequest,
  type IAIStreamCallbacks,
  type IReadyMessageData,
  type IAIServiceConfig,
}

// 导出匀速输出相关
export {
  createSmoothOutput,
  SPEED_PRESETS,
  type TSmoothOutputConfig,
} from './hooks/useSmoothOutput'
