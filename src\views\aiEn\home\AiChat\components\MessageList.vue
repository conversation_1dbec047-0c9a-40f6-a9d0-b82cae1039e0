<template>
  <div class="message-list h-full">
    <DynamicScroller
      ref="scrollerRef"
      :items="messages"
      :min-item-size="minItemSize"
      :buffer="buffer"
      key-field="messageId"
      class="h-full pb-5px"
      @scroll="handleScroll"
    >
      <!-- 欢迎卡片 - 永远显示在最顶部 -->
      <template #before>
        <div v-if="shouldShowWelcomeCard" class="px-12px pt-16px">
          <WelcomeCard />
        </div>
      </template>

      <template #default="{ item, index, active }">
        <DynamicScrollerItem
          :item="item"
          :active="active"
          :data-index="index"
          :size-dependencies="['content.text', 'status']"
        >
          <div class="message-item-wrapper px-12px">
            <!-- 时间戳显示 -->
            <TimeStamp
              :timestamp="item.timestamp"
              :previous-timestamp="props.messages[index - 1]?.timestamp"
              :should-show-debug="false"
            />

            <!-- 学生消息 -->
            <div v-if="item.messageType === EMessageType.USER" class="pb-24px">
              <MessageItemS
                :message="item"
                @play="handlePlay"
                @update-message="handleUpdateMessage"
                @close-other-improve-results="handleCloseOtherImproveResults"
                @scroll-to-message="handleScrollToMessage"
              />
            </div>

            <!-- 老师消息 -->
            <div
              v-else-if="item.messageType === EMessageType.ASSISTANT"
              class="pb-24px"
            >
              <MessageItemT
                :message="item"
                :is-latest="isLatestAssistantMessage(item.messageId)"
                :expanded-message-ids="expandedMessageIds"
                @play="handlePlay"
                @like="handleLike"
                @dislike="handleDislike"
                @bar-translate="handleBarTranslate"
                @teach-me="handleTeachMe"
                @update-message="handleUpdateMessage"
                @expand-history="handleExpandHistory"
              />
            </div>

            <!-- 系统消息 -->
            <div
              v-else-if="item.messageType === EMessageType.SYSTEM"
              class="flex justify-center pb-24px"
            >
              <div
                class="bg-gray-200 text-gray-600 text-12px px-12px py-6px rounded-full"
              >
                {{ item.content }}
              </div>
            </div>
          </div>
        </DynamicScrollerItem>
      </template>
    </DynamicScroller>

    <!-- 底部锚点元素，用于Intersection Observer -->
    <div
      ref="bottomAnchorRef"
      class="absolute bottom-0 w-full h-1px pointer-events-none"
    ></div>

    <!-- 滚动到底部按钮 -->
    <Transition
      name="slide-up"
      enter-active-class="transition-all duration-300 ease-out"
      leave-active-class="transition-all duration-300 ease-in"
      enter-from-class="transform translate-y-20px opacity-0"
      enter-to-class="transform translate-y-0 opacity-100"
      leave-from-class="transform translate-y-0 opacity-100"
      leave-to-class="transform translate-y-20px opacity-0"
    >
      <div
        v-show="showScrollToBottom"
        class="absolute bottom-70px left-1/2 transform -translate-x-1/2"
      >
        <button
          class="w-32px h-32px bg-white rounded-full shadow-lg flex items-center justify-center z-10 hover:border-gray-400 transition-colors"
          @click="scrollToBottomClick"
        >
          <g-icon name="ri-arrow-down-line" size="14" color="" />
        </button>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import { EMessageType, EMessageStatus, EChatMode } from '../type'
import type { IMessage } from '../type'
import MessageItemS from './MessageItemS.vue'
import MessageItemT from './MessageItemT.vue'
import WelcomeCard from './WelcomeCard.vue'
import TimeStamp from './TimeStamp.vue'
import { useMessageListScroll } from '../hooks/useMessageListScroll'

interface IProps {
  /** 消息列表数据 */
  messages: IMessage[]
  /** 最小项目高度 */
  minItemSize?: number
  /** 缓冲区大小 */
  buffer?: number
  /** 是否自动滚动到底部 */
  autoScrollToBottom?: boolean
}

const props = withDefaults(defineProps<IProps>(), {
  minItemSize: 80,
  buffer: 200,
  autoScrollToBottom: true,
})

const emit = defineEmits<{
  scroll: [scrollTop: number]
  play: [message?: IMessage]
  like: [message?: IMessage]
  dislike: [message?: IMessage]
  teachMe: [message?: IMessage]
  updateMessage: [message: IMessage]
  scrollToMessage: [messageId: number]
}>()

// 通过 inject 获取聊天模式
const chatMode = inject<EChatMode>('chatMode', EChatMode.FREE)

// 计算是否显示欢迎卡片（只在自由对话模式下显示）
const shouldShowWelcomeCard = computed(() => chatMode === EChatMode.FREE)
const scrollerRef = ref<InstanceType<typeof DynamicScroller>>()
const bottomAnchorRef = ref<HTMLElement>()

/** 当前展开工具栏的消息ID列表 */
let expandedMessageIds = $ref<number[]>([])

// 使用滚动 hook
const {
  showScrollToBottom,
  isUserAtBottom,
  scrollToBottom,
  scrollToMessage,
  onScroll,
  initialize,
  cleanup,
} = useMessageListScroll({
  messages: toRef(() => props.messages),
  scrollerRef,
  bottomAnchorRef,
  autoScrollToBottom: props.autoScrollToBottom,
})

// 监听消息数组变化，当有新消息时重置展开状态
watch(
  () => props.messages.length,
  (newLength, oldLength) => {
    // 当消息数量增加时（有新消息），重置展开状态
    if (newLength > oldLength) {
      expandedMessageIds = []
    }
  },
)

/** 处理滚动事件并发送事件 */
function handleScroll(event: Event) {
  onScroll(event)

  // 发出滚动事件
  const target = event.target as HTMLElement
  if (target) {
    emit('scroll', target.scrollTop)
  }
}

// 组件挂载时的初始化逻辑
onMounted(() => {
  initialize()
})

/** 播放处理 */
function handlePlay(message?: IMessage) {
  emit('play', message)
}

/** 点赞处理 */
function handleLike(message?: IMessage) {
  emit('like', message)
}

/** 踩处理 */
function handleDislike(message?: IMessage) {
  emit('dislike', message)
}

/** 教我回答处理 */
function handleTeachMe(message?: IMessage) {
  emit('teachMe', message)

  // 等待教我回答卡片渲染完成后再滚动
  setTimeout(() => {
    nextTick(() => {
      scrollToBottom()
    })
  }, 0)
}

/** 更新消息处理 */
function handleUpdateMessage(message: IMessage) {
  emit('updateMessage', message)
}

/** 关闭除了指定消息外的所有润色结果 */
function handleCloseOtherImproveResults(messageId: number) {
  // 查找所有展开了润色结果的用户消息
  props.messages.forEach((msg) => {
    if (
      msg.messageType === EMessageType.USER && // 是用户消息
      msg.messageId !== messageId && // 不是当前消息
      msg.showImproveResult && // 显示了润色结果
      msg.improve // 有润色数据
    ) {
      // 更新消息，关闭润色结果
      const updatedMsg = {
        ...msg,
        showImproveResult: false,
      }
      emit('updateMessage', updatedMsg)
    }
  })
}

/** 滚动到指定消息 */
function handleScrollToMessage(messageId: number) {
  emit('scrollToMessage', messageId)
  // 可以在这里直接调用滚动函数
  setTimeout(() => {
    scrollToMessage(messageId)
  }, 100)
}

/** 翻译处理 */
function handleBarTranslate(message?: IMessage, showTranslation?: boolean) {
  if (!message || !showTranslation) return
  if (isLatestAssistantMessage(message.messageId)) {
    // 翻译显示后如果用户在底部则滚动，使用nextTick等待DOM更新即可
    nextTick(() => {
      if (isUserAtBottom.value) {
        scrollToBottom()
      }
    })
  }
}

/** 处理历史消息工具栏展开 */
function handleExpandHistory(messageId: number | null) {
  if (messageId === null) return

  // 检查是否已经展开
  if (expandedMessageIds.includes(messageId)) {
    // 如果已展开，则收起
    expandedMessageIds = expandedMessageIds.filter((id) => id !== messageId)
  } else {
    // 如果未展开，则添加到展开列表
    expandedMessageIds.push(messageId)
  }
}

/** 判断是否是最新的助手消息 */
function isLatestAssistantMessage(messageId: number): boolean {
  const assistantMessages = props.messages.filter(
    (msg) => msg.messageType === EMessageType.ASSISTANT,
  )

  if (assistantMessages.length === 0) {
    return false
  }

  const latestAssistantMessageId =
    assistantMessages[assistantMessages.length - 1].messageId

  return messageId === latestAssistantMessageId
}

function scrollToBottomClick() {
  scrollToBottom()
  setTimeout(() => {
    scrollToBottom()
  }, 50)
}

// 组件销毁时清理
onUnmounted(() => {
  cleanup()
})

defineExpose({
  scrollToBottom,
  scrollToMessage,
})
</script>

<style scoped lang="scss">
.message-list {
  /* 隐藏滚动条但保持滚动功能 */
  .vue-recycle-scroller {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
  }

  .vue-recycle-scroller::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
}

/* 悬停效果 */
.message-item-wrapper:hover .bg-gray-200 {
  border-color: #374151 !important;
}
</style>
