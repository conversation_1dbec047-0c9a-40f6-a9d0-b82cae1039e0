# 多动画预加载功能使用指南

## 概述

`useLottiePreload` 是一个支持同时加载多个 Lottie 动画的组合式函数，提供了细粒度的状态管理、进度跟踪和错误处理功能。

## 基本用法

### 1. 导入和使用

```typescript
import { useLottiePreload } from '@/views/aiEn/lottiePreload/useLottiePreload'

// 定义多个动画 URL
const lottieUrls = [
  'https://example.com/animation1.json',
  'https://example.com/animation2.json',
  'https://example.com/animation3.json',
]

// 使用组合式函数
const {
  loading,
  error,
  isCached,
  animationStatuses,
  loadingProgress,
  errors,
  preload,
  getAnimationData,
  getMultipleAnimationData,
  getAllAnimationData,
  retryFailedAnimations,
  clearExpiredLottieCache,
  clearAllLottieCache,
} = useLottiePreload(lottieUrls)
```

### 2. 预加载动画

```typescript
// 页面加载时开始预加载
onMounted(() => {
  preload()
})
```

### 3. 获取动画数据

```typescript
// 获取单个动画数据
const animationData = await getAnimationData(lottieUrls[0])

// 批量获取多个动画数据
const dataMap = await getMultipleAnimationData([lottieUrls[0], lottieUrls[1]])

// 获取所有动画数据
const allData = await getAllAnimationData()
```

## 状态管理

### 1. 全局状态

- `loading`: 是否正在加载
- `error`: 全局错误信息
- `isCached`: 是否有缓存的动画

### 2. 详细状态

```typescript
// 每个动画的详细状态
const animationStatuses = computed(() => {
  // 返回 Map<string, AnimationStatus>
  // AnimationStatus 包含：
  // - url: 动画 URL
  // - loading: 是否正在加载
  // - cached: 是否已缓存
  // - error: 错误信息
  // - data: 动画数据
})
```

### 3. 加载进度

```typescript
const loadingProgress = computed(() => {
  return {
    total: 3, // 总动画数量
    loaded: 2, // 已加载数量
    progress: 66.67, // 加载进度百分比
  }
})
```

### 4. 错误处理

```typescript
// 获取所有错误
const errors = computed(() => {
  return [
    { url: 'https://example.com/animation1.json', error: '网络错误' },
    { url: 'https://example.com/animation2.json', error: '解析失败' },
  ]
})

// 重试失败的动画
await retryFailedAnimations()
```

## 实际使用示例

### 1. 在组件中使用

```vue
<template>
  <div>
    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      加载进度: {{ loadingProgress.loaded }}/{{ loadingProgress.total }}
      <div class="progress-bar">
        <div :style="{ width: loadingProgress.progress + '%' }"></div>
      </div>
    </div>

    <!-- 错误处理 -->
    <div v-if="errors.length > 0" class="errors">
      <div v-for="error in errors" :key="error.url">
        {{ error.url }}: {{ error.error }}
      </div>
      <button @click="retryFailedAnimations">重试</button>
    </div>

    <!-- 动画展示 -->
    <div v-if="!loading && !error">
      <g-lottie
        v-for="(status, url) in animationStatuses"
        :key="url"
        v-if="status.data"
        :options="{ animationData: status.data }"
      />
    </div>
  </div>
</template>

<script setup>
import { useLottiePreload } from '@/views/aiEn/lottiePreload/useLottiePreload'

const lottieUrls = [
  'https://example.com/animation1.json',
  'https://example.com/animation2.json',
]

const {
  loading,
  error,
  animationStatuses,
  loadingProgress,
  errors,
  preload,
  retryFailedAnimations,
} = useLottiePreload(lottieUrls)

onMounted(() => {
  preload()
})
</script>
```

### 2. 动态切换动画

```typescript
// 在组件中实现动画切换
const switchToAnimation = async (index: number) => {
  if (index >= 0 && index < lottieUrls.length) {
    const url = lottieUrls[index]
    const animationData = await getAnimationData(url)
    if (animationData) {
      // 更新当前显示的动画
      currentAnimation.value = animationData
    }
  }
}
```

### 3. 条件加载

```typescript
// 根据条件加载不同的动画
const loadAnimationsByCondition = async (condition: string) => {
  let urlsToLoad: string[] = []

  switch (condition) {
    case 'success':
      urlsToLoad = [lottieUrls[0]] // 成功动画
      break
    case 'error':
      urlsToLoad = [lottieUrls[1]] // 错误动画
      break
    case 'loading':
      urlsToLoad = [lottieUrls[2]] // 加载动画
      break
  }

  const dataMap = await getMultipleAnimationData(urlsToLoad)
  return dataMap
}
```

## 缓存管理

### 1. 清除过期缓存

```typescript
// 清除过期的缓存（默认24小时过期）
await clearExpiredLottieCache()
```

### 2. 清除所有缓存

```typescript
// 清除所有缓存
await clearAllLottieCache()
```

## 性能优化建议

1. **合理设置动画数量**: 避免同时加载过多动画，建议控制在 5-10 个以内
2. **使用条件加载**: 根据实际需要动态加载动画
3. **监控缓存状态**: 定期清理过期缓存
4. **错误重试**: 对失败的动画进行重试，提高成功率

## 注意事项

1. 所有动画 URL 必须在初始化时提供
2. 缓存基于 URL，相同 URL 的动画会共享缓存
3. 错误状态会持续存在，需要手动重试或清除
4. 动画数据存储在 IndexedDB 中，受浏览器存储限制

## API 参考

### useLottiePreload(urls: string[])

**参数:**

- `urls`: 动画 URL 数组

**返回值:**

- `loading`: 是否正在加载
- `error`: 全局错误信息
- `isCached`: 是否有缓存的动画
- `animationStatuses`: 每个动画的详细状态
- `loadingProgress`: 加载进度信息
- `errors`: 错误列表
- `preload()`: 开始预加载
- `getAnimationData(url)`: 获取单个动画数据
- `getMultipleAnimationData(urls)`: 批量获取动画数据
- `getAllAnimationData()`: 获取所有动画数据
- `retryFailedAnimations()`: 重试失败的动画
- `clearExpiredLottieCache()`: 清除过期缓存
- `clearAllLottieCache()`: 清除所有缓存
