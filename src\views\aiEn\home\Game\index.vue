<template>
  <div class="pt-24px px-12px">
    <div
      v-for="item in gameList"
      :key="item.name"
      class="w-full h-[160px] flex justify-between px-24px overflow-hidden"
      :class="item.className"
      @click="goToGame(item)"
    >
      <div class="py-42px flex-1">
        <div class="text-24px font-600 text-[white] h-28px leading-[28px]">
          {{ item.name }}
        </div>
        <div
          class="text-14px text-[#F2F5FF] opacity-50 mt-12px max-w-[70%] h-[36px]"
        >
          {{ item.enName }}
        </div>
      </div>
      <div class="flex-1 flex justify-end items-center">
        <div v-if="item.className == 'world-relay'" class="text-[20px]">
          <div
            class="h-56px leading-[56px] px-40px text-[#F2F5FF] bg-[#FBFCFE1A] br-[28px] !rounded-bl-[4px] text-center"
          >
            shop
          </div>
          <div
            class="h-56px leading-[56px] px-14px bg-[#F2F5FF] br-[28px] !rounded-br-[4px] my-12px text-center"
          >
            play
          </div>
          <div
            class="h-56px leading-[56px] px-14px text-[#F2F5FF] br-[28px] bg-[#FBFCFE1A] text-center"
          >
            yes
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const router = useRouter()

const gameList = [
  {
    name: '单词接龙',
    enName: 'Play words Spark Wisdom',
    pathName: 'WordRelayGameIntroduction',
    className: 'world-relay',
  },
]

function goToGame(item: any) {
  router.push({ name: item.pathName })
}
</script>

<style scoped>
.world-relay {
  background: linear-gradient(180deg, #7165f9 0%, #5e56df 100%);
  box-shadow: 10px 10px 30px 0px rgba(113, 101, 249, 0.3);
  border-radius: 18px;
}
</style>
