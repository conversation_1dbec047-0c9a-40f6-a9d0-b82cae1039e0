import { getUserInfoApi, getConfigApi, getStudyApi } from '@/api/aiEn'
export const useAiSettingStore = defineStore('aiEnSetting', {
  state: () => ({
    aiEnUserInfo: {},
    isVoicePlaying: false, //是否正在播放语音
    config: {
      voiceSpeed: '1', //语音速度
      voiceAutoPlayStatus: true, //语音是否自动播放
    },
    study: {}, //教材、版本信息
  }),
  actions: {
    // 请求设置详情，在进入首页时会调用
    async fetchSettingDetail() {
      this.aiEnUserInfo = await getUserInfoApi()
      this.config = await getConfigApi({
        type: 'AI_TEACHER',
      })
      this.study = await getStudyApi()
    },
  },
})
