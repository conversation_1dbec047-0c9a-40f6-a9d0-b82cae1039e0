<template>
  <div ref="waveContainer" class="wave-container">
    <div
      v-for="(bar, index) in bars"
      :key="index"
      class="wave-bar"
      :style="{
        height: `${bar}%`,
        animationDelay: `${index * 0.05}s`,
      }"
    ></div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  barCount: {
    type: Number,
    default: 20,
  },
  minHeight: {
    type: Number,
    default: 6,
  },
  maxHeight: {
    type: Number,
    default: 16,
  },
  animated: {
    type: Boolean,
    default: true,
  },
})

const bars = ref<number[]>([])
const animationId = ref<number | null>(null)
const waveContainer = ref<HTMLElement | null>(null)

const generateRandomBars = () => {
  const newBars: number[] = []
  for (let i = 0; i < props.barCount; i++) {
    const height = Math.floor(
      Math.random() * (props.maxHeight - props.minHeight) + props.minHeight,
    )
    newBars.push(height)
  }
  bars.value = newBars
}

const animate = () => {
  if (!props.animated) return

  generateRandomBars()
  animationId.value = requestAnimationFrame(() => {
    setTimeout(() => {
      animate()
    }, 100)
  })
}

onMounted(() => {
  generateRandomBars()
  if (props.animated) {
    animate()
  }
})

onBeforeUnmount(() => {
  if (animationId.value !== null) {
    cancelAnimationFrame(animationId.value)
  }
})
</script>

<style scoped>
.wave-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  height: 50px;
  width: 100%;
  --bar-color: #fff;
}

.wave-bar {
  /* flex: 1; */
  background-color: var(--bar-color);
  min-width: 3px;
  border-radius: 2px;
  transition: height 0.2s ease;
}

@keyframes wave {
  0% {
    height: 10%;
  }
  50% {
    height: 100%;
  }
  100% {
    height: 10%;
  }
}

.wave-container:not(.static) .wave-bar {
  animation: wave 1.2s ease-in-out infinite;
}
</style>
