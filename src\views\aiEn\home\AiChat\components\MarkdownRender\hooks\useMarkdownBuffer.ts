/**
 * Markdown缓冲Hook
 * 智能处理流式markdown内容的缓冲和渲染
 */

import type { Ref } from 'vue'

/** 缓冲策略配置 */
interface IBufferConfig {
  /** 缓冲延迟时间（毫秒） */
  debounceDelay?: number
  /** 最小缓冲字符数 */
  minBufferSize?: number
  /** 是否启用智能检测 */
  enableSmartDetection?: boolean
}

/** Hook返回值 */
interface IMarkdownBuffer {
  /** 可渲染的内容 */
  renderableContent: Ref<string>
  /** 添加内容到缓冲区 */
  addContent: (content: string) => void
  /** 刷新缓冲区（立即渲染所有内容） */
  flushBuffer: () => void
  /** 清空缓冲区 */
  clearBuffer: () => void
  /** 重置hook */
  reset: () => void
}

/**
 * Markdown标记模式定义
 */
const MARKDOWN_PATTERNS = [
  // 代码块 - 优先级最高
  {
    name: 'codeblock',
    start: /```[^`]*$/,
    end: /```/,
    priority: 1,
    allowNested: false,
  },
  // 行内代码
  {
    name: 'inlinecode',
    start: /`[^`]*$/,
    end: /`/,
    priority: 2,
    allowNested: false,
  },
  // 粗体
  {
    name: 'bold',
    start: /\*\*[^*]*$/,
    end: /\*\*/,
    priority: 3,
    allowNested: true,
  },
  // 链接
  {
    name: 'link',
    start: /\[[^\]]*$/,
    end: /\]/,
    priority: 4,
    allowNested: false,
  },
  // 斜体
  {
    name: 'italic',
    start: /\*[^*]*$/,
    end: /\*/,
    priority: 5,
    allowNested: true,
  },
] as const

/**
 * 检测markdown标记的完整性
 * @param content 待检测内容
 */
function checkMarkdownIntegrity(content: string): {
  safeContent: string
  pendingContent: string
  hasIncomplete: boolean
} {
  if (!content.trim()) {
    return { safeContent: '', pendingContent: '', hasIncomplete: false }
  }

  let lastSafeIndex = content.length
  let hasIncomplete = false

  // 按优先级检查各种markdown模式
  for (const pattern of MARKDOWN_PATTERNS) {
    const matches = Array.from(
      content.matchAll(new RegExp(pattern.start.source, 'g')),
    )

    for (const match of matches) {
      if (match.index === undefined) continue

      const startIndex = match.index
      const afterMatch = content.slice(startIndex + match[0].length)

      // 检查是否有对应的结束标记
      if (!pattern.end.test(afterMatch)) {
        lastSafeIndex = Math.min(lastSafeIndex, startIndex)
        hasIncomplete = true
        break // 找到未完成的模式，停止检查该模式
      }
    }
  }

  // 额外检查：确保不在单词中间截断
  if (lastSafeIndex < content.length) {
    const safeText = content.slice(0, lastSafeIndex)
    const pendingText = content.slice(lastSafeIndex)

    // 如果pending部分是单词的一部分，向前调整边界
    if (pendingText.length > 0 && /\w/.test(pendingText[0])) {
      const lastSpaceIndex = safeText.lastIndexOf(' ')
      const lastNewlineIndex = safeText.lastIndexOf('\n')
      const adjustedIndex = Math.max(lastSpaceIndex, lastNewlineIndex, 0)

      if (adjustedIndex > 0) {
        lastSafeIndex = adjustedIndex + 1
      }
    }
  }

  const safeContent = content.slice(0, lastSafeIndex)
  const pendingContent = content.slice(lastSafeIndex)

  return { safeContent, pendingContent, hasIncomplete }
}

/**
 * Markdown缓冲Hook
 * @param config 缓冲配置
 */
export function useMarkdownBuffer(config: IBufferConfig = {}): IMarkdownBuffer {
  const {
    debounceDelay = 100,
    minBufferSize = 20,
    enableSmartDetection = true,
  } = config

  // 状态管理
  let buffer = $ref('')
  let renderableContent = $ref('')
  let pendingContent = $ref('')
  let debounceTimer: NodeJS.Timeout | null = null

  /**
   * 处理缓冲内容，决定什么时候渲染
   */
  function processBuffer(): void {
    if (!buffer) {
      renderableContent = ''
      return
    }

    if (!enableSmartDetection) {
      // 简单模式：直接渲染所有内容
      renderableContent = buffer
      return
    }

    // 智能模式：检查markdown完整性
    const {
      safeContent,
      pendingContent: newPending,
      hasIncomplete,
    } = checkMarkdownIntegrity(buffer)

    if (!hasIncomplete || buffer.length >= minBufferSize * 2) {
      // 没有未完成的标记，或缓冲区足够大时，渲染所有内容
      renderableContent = buffer
      pendingContent = ''
    } else if (safeContent.length > renderableContent.length) {
      // 有新的安全内容可以渲染
      renderableContent = safeContent
      pendingContent = newPending
    }
  }

  /**
   * 防抖处理
   */
  function debouncedProcess(): void {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }

    debounceTimer = setTimeout(() => {
      processBuffer()
      debounceTimer = null
    }, debounceDelay)
  }

  /**
   * 添加内容到缓冲区
   * @param content 新增内容
   */
  function addContent(content: string): void {
    if (!content) return

    buffer += content

    // 检查是否包含可能导致显示问题的markdown标记
    const hasMarkdownSymbols = /[*`#\[\]]/.test(buffer)
    const endsWithIncompletePattern = /(\*{1,2}|`{1,3}|#{1,6}|\[)$/.test(
      buffer.trim(),
    )
    const hasCompletedPattern = /(\*\*.*?\*\*|`.*?`|#{1,6}\s+.*|\[.*?\])/s.test(
      buffer,
    )

    // 立即处理的条件
    const shouldProcessImmediately =
      content.includes('\n\n') || // 段落结束
      content.includes('。\n') || // 中文句号+换行
      content.includes('. ') || // 英文句号+空格
      content.includes('?\n') || // 问号+换行
      content.includes('!\n') || // 感叹号+换行
      (hasCompletedPattern && buffer.length > minBufferSize) || // 包含完整的markdown模式且有一定长度
      buffer.length > minBufferSize * 4 // 内容足够长

    if (shouldProcessImmediately) {
      // 立即处理
      processBuffer()
    } else if (
      buffer.length < minBufferSize ||
      hasMarkdownSymbols ||
      endsWithIncompletePattern
    ) {
      // 延迟处理
      debouncedProcess()
    } else {
      // 普通内容，立即处理
      processBuffer()
    }
  }

  /**
   * 刷新缓冲区 - 立即渲染所有内容
   */
  function flushBuffer(): void {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
      debounceTimer = null
    }

    if (buffer) {
      renderableContent = buffer
      pendingContent = ''
    }
  }

  /**
   * 清空缓冲区
   */
  function clearBuffer(): void {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
      debounceTimer = null
    }

    buffer = ''
    renderableContent = ''
    pendingContent = ''
  }

  /**
   * 重置hook
   */
  function reset(): void {
    clearBuffer()
  }

  // 组件卸载时清理定时器
  onUnmounted(() => {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }
  })

  return {
    renderableContent: $$(renderableContent),
    addContent,
    flushBuffer,
    clearBuffer,
    reset,
  }
}
