import { useWebSocket } from '@vueuse/core'
import { ref, computed, onUnmounted, type Ref } from 'vue'

/** 调试日志开关 */
let debugLogEnabled = true

/** 设置调试日志开关 */
function setDebugLogEnabled(enabled: boolean): void {
  debugLogEnabled = enabled
}

/** 添加调试日志 */
function addDebugLog(message: string): void {
  if (import.meta.env.VITE_APP_ENV !== 'production' && debugLogEnabled) {
    console.log(message)
  }
}

/** TTS WebSocket 请求消息类型 */
interface ITTSRequest {
  /** 音频转换类型 4-单句直接转换 */
  messageType: 4
  /** 当前会话的唯一编号 */
  serialNumber: string
  /** 需要转语音的文本内容 */
  text: string
  /** 语音速度 (0.5-2.0) */
  speed?: number
}

/** TTS WebSocket 响应消息类型 */
interface ITTSResponse {
  /** 回复类型: 1-音频流起点, 2-音频流继续发送, 3-音频流转换结束, 5-音频转换失败 */
  code: 1 | 2 | 3 | 5
  /** 提示信息 */
  message: string
  /** 当前会话的唯一编号 */
  serialNumber: string
  /** 转base64的音频流数据 */
  data?: string
}

/** 段落回调函数类型 */
interface ISegmentCallbacks {
  /** 开始转换回调 */
  onStart?: (serialNumber: string) => void
  /** 音频数据回调（原始base64数据） */
  onData?: (data: string, serialNumber: string) => void
  /** 音频准备就绪回调（提供可播放的音频URL） */
  onAudioReady?: (audioUrl: string, serialNumber: string) => void
  /** 转换完成回调 */
  onComplete?: (serialNumber: string) => void
  /** 转换失败回调 */
  onError?: (message: string, serialNumber: string) => void
}

/** 段落转换结果类型 */
interface ISegmentResult {
  /** 转换是否成功 */
  success: boolean
  /** 音频URL（成功时） */
  audioUrl?: string
  /** 序列号 */
  serialNumber: string
  /** 错误信息（失败时） */
  error?: string
  /** 音频数据大小（字节） */
  audioSize?: number
}

/** 段落信息接口 */
interface ISegmentInfo {
  /** 段落序列号 */
  serialNumber: string
  /** 段落文本 */
  text: string
  /** 段落回调 */
  callbacks?: ISegmentCallbacks
  /** 语音速度 */
  speed?: number
  /** 音频数据缓冲区 */
  audioData: number[]
  /** 结果Promise resolve函数 */
  resolve: (result: ISegmentResult) => void
  /** 转换状态 */
  status: 'pending' | 'converting' | 'completed' | 'failed'
}

/** 任务状态枚举 */
enum ETaskStatus {
  /** 初始化中 */
  INITIALIZING = 'initializing',
  /** 已连接 */
  CONNECTED = 'connected',
  /** 处理中 */
  PROCESSING = 'processing',
  /** 已完成 */
  COMPLETED = 'completed',
  /** 已取消 */
  CANCELLED = 'cancelled',
  /** 连接失败 */
  FAILED = 'failed',
}

/** TTS 任务类 */
class TTSTask {
  private ws: ReturnType<typeof useWebSocket> | null = null
  private taskId: string
  private segments: Map<string, ISegmentInfo> = new Map()
  private segmentQueue: string[] = [] // 段落处理队列
  private currentSegment: string | null = null // 当前处理的段落
  private isProcessing = false
  private finishedCallback: (() => void) | null = null

  /** 任务状态 */
  public status: Ref<ETaskStatus> = ref(ETaskStatus.INITIALIZING)

  constructor(taskId: string) {
    this.taskId = taskId
    this.initWebSocket()
  }

  /** 初始化 WebSocket 连接 */
  private initWebSocket(): void {
    const wsUrl =
      'wss:' +
      import.meta.env.VITE_APP_BASE_API5 +
      '/aienglish/chat/tts/websocket'

    addDebugLog(`🔗 任务 ${this.taskId} 初始化WebSocket连接`)

    this.ws = useWebSocket(wsUrl, {
      immediate: true,
      autoReconnect: false,
      onConnected: () => {
        addDebugLog(`✅ 任务 ${this.taskId} WebSocket连接成功`)
        this.status.value = ETaskStatus.CONNECTED
        this.processNextSegment() // 连接成功后开始处理队列
      },
      onDisconnected: () => {
        addDebugLog(`🔌 任务 ${this.taskId} WebSocket连接断开`)
        if (this.status.value === ETaskStatus.PROCESSING) {
          this.handleConnectionError()
        }
      },
      onError: (event) => {
        console.error(`❌ 任务 ${this.taskId} WebSocket连接错误:`, event)
        this.status.value = ETaskStatus.FAILED
        this.handleConnectionError()
      },
      onMessage: (ws, event) => {
        this.handleMessage(event.data)
      },
    })
  }

  /** 处理连接错误 */
  private handleConnectionError(): void {
    // 将所有等待中的段落标记为失败
    this.segments.forEach((segment) => {
      if (segment.status === 'pending' || segment.status === 'converting') {
        segment.status = 'failed'
        segment.callbacks?.onError?.('连接断开', segment.serialNumber)
        segment.resolve({
          success: false,
          serialNumber: segment.serialNumber,
          error: '连接断开',
          audioSize: 0,
        })
      }
    })
  }

  /** 添加段落到处理队列 */
  async addSegment(
    text: string,
    callbacks?: ISegmentCallbacks,
    speed?: number,
  ): Promise<ISegmentResult> {
    const serialNumber = this.generateSerialNumber()
    addDebugLog(
      `📝 任务 ${this.taskId} 添加段落: ${serialNumber} - "${text.substring(0, 20)}..."`,
    )

    return new Promise((resolve) => {
      const segmentInfo: ISegmentInfo = {
        serialNumber,
        text,
        callbacks,
        speed,
        audioData: [],
        resolve,
        status: 'pending',
      }

      this.segments.set(serialNumber, segmentInfo)
      this.segmentQueue.push(serialNumber)

      addDebugLog(
        `📋 任务 ${this.taskId} 段落加入队列: ${serialNumber}, 队列长度: ${this.segmentQueue.length}, 连接状态: ${this.status.value}, 正在处理: ${this.isProcessing}`,
      )

      // 如果连接已就绪且没有在处理，立即开始处理
      if (this.status.value === ETaskStatus.CONNECTED && !this.isProcessing) {
        addDebugLog(`🏃 任务 ${this.taskId} 立即开始处理队列`)
        this.processNextSegment()
      } else {
        addDebugLog(
          `⏳ 任务 ${this.taskId} 等待条件满足: 连接=${this.status.value}, 处理中=${this.isProcessing}`,
        )
        addDebugLog(
          `🔍 任务 ${this.taskId} 当前状态 - 队列: [${this.segmentQueue.join(', ')}], 当前处理: ${this.currentSegment}`,
        )
      }
    })
  }

  /** 处理下一个段落 */
  private async processNextSegment(): Promise<void> {
    addDebugLog(
      `🚀 任务 ${this.taskId} processNextSegment - 队列长度: ${this.segmentQueue.length}, 正在处理: ${this.isProcessing}, 状态: ${this.status.value}`,
    )

    if (this.isProcessing) {
      addDebugLog(`⏸️ 任务 ${this.taskId} 正在处理中，跳过`)
      return
    }

    if (this.segmentQueue.length === 0) {
      addDebugLog(`⏸️ 任务 ${this.taskId} 队列为空，跳过`)
      return
    }

    if (this.status.value !== ETaskStatus.CONNECTED) {
      addDebugLog(`⏸️ 任务 ${this.taskId} 连接未就绪，等待连接`)
      return
    }

    this.isProcessing = true
    this.status.value = ETaskStatus.PROCESSING

    const serialNumber = this.segmentQueue.shift()!
    const segment = this.segments.get(serialNumber)!

    this.currentSegment = serialNumber
    segment.status = 'converting'

    addDebugLog(
      `🎯 任务 ${this.taskId} 开始处理段落: ${serialNumber} - "${segment.text.substring(0, 30)}..."`,
    )

    // 发送TTS请求
    await this.sendTTSRequest(segment)
  }

  /** 发送TTS请求 */
  private async sendTTSRequest(segment: ISegmentInfo): Promise<void> {
    if (!this.ws || this.ws.status.value !== 'OPEN') {
      this.handleSegmentError(segment, 'WebSocket连接未就绪')
      return
    }

    const request: ITTSRequest = {
      messageType: 4,
      serialNumber: segment.serialNumber,
      text: segment.text,
      ...(segment.speed && { speed: segment.speed }),
    }

    addDebugLog(
      `📤 发送TTS请求: taskId=${this.taskId}, serialNumber=${segment.serialNumber}, text="${segment.text.substring(0, 20)}...", speed=${segment.speed || 1.0}`,
    )

    try {
      this.ws.send(JSON.stringify(request))
      segment.callbacks?.onStart?.(segment.serialNumber)
    } catch (error) {
      console.error('发送TTS请求失败:', error)
      this.handleSegmentError(segment, '发送请求失败')
    }
  }

  /** 处理接收到的消息 */
  private handleMessage(messageData: string): void {
    try {
      const response: ITTSResponse = JSON.parse(messageData)
      const { code, message, serialNumber, data } = response

      const segment = this.segments.get(serialNumber)
      if (!segment) {
        console.warn(`⚠️ 收到未知段落的响应: ${serialNumber}`)
        return
      }

      switch (code) {
        case 1:
          // 音频流开始
          addDebugLog(`🎵 段落 ${serialNumber} 音频流开始`)
          segment.audioData = []
          if (data) this.processAudioChunk(segment, data)
          break

        case 2:
          // 音频流继续
          if (data) {
            this.processAudioChunk(segment, data)
            segment.callbacks?.onData?.(data, serialNumber)
          }
          break

        case 3:
          // 音频流结束
          addDebugLog(`✅ 段落 ${serialNumber} 音频流结束`)
          addDebugLog(
            `🎯 任务 ${this.taskId} 准备处理段落成功: ${serialNumber}`,
          )
          this.handleSegmentSuccess(segment)
          break

        case 5:
          // 转换失败
          console.error(`❌ 段落 ${serialNumber} 转换失败: ${message}`)
          this.handleSegmentError(segment, message)
          break
      }
    } catch (error) {
      console.error('解析TTS消息失败:', error)
    }
  }

  /** 处理音频数据块 */
  private processAudioChunk(segment: ISegmentInfo, data: string): void {
    try {
      const binaryString = window.atob(data)
      for (let i = 0; i < binaryString.length; i++) {
        segment.audioData.push(binaryString.charCodeAt(i))
      }
    } catch (error) {
      console.error('处理音频数据失败:', error)
    }
  }

  /** 处理段落成功 */
  private handleSegmentSuccess(segment: ISegmentInfo): void {
    segment.status = 'completed'
    addDebugLog(`🎉 任务 ${this.taskId} 段落 ${segment.serialNumber} 处理成功`)

    // 生成音频URL
    const audioUrl = this.generateAudioUrl(segment.audioData)
    if (audioUrl) {
      segment.callbacks?.onAudioReady?.(audioUrl, segment.serialNumber)
    }

    segment.callbacks?.onComplete?.(segment.serialNumber)

    // 返回结果
    segment.resolve({
      success: true,
      audioUrl: audioUrl || undefined,
      serialNumber: segment.serialNumber,
      audioSize: segment.audioData.length,
    })

    // 处理完成，继续下一个段落
    addDebugLog(`📞 任务 ${this.taskId} 调用 onSegmentFinished`)
    this.onSegmentFinished()
  }

  /** 处理段落错误 */
  private handleSegmentError(segment: ISegmentInfo, error: string): void {
    segment.status = 'failed'
    segment.callbacks?.onError?.(error, segment.serialNumber)

    segment.resolve({
      success: false,
      serialNumber: segment.serialNumber,
      error,
      audioSize: 0,
    })

    // 处理完成，继续下一个段落
    this.onSegmentFinished()
  }

  /** 段落处理完成后的清理工作 */
  private onSegmentFinished(): void {
    addDebugLog(
      `🔄 任务 ${this.taskId} 段落处理完成，队列长度: ${this.segmentQueue.length}`,
    )
    addDebugLog(
      `🔍 任务 ${this.taskId} 当前状态: 队列=[${this.segmentQueue.join(', ')}], 当前处理=${this.currentSegment}, 正在处理=${this.isProcessing}`,
    )

    this.currentSegment = null
    this.isProcessing = false

    // 如果还有队列项目需要处理，重置状态为CONNECTED
    if (this.segmentQueue.length > 0) {
      this.status.value = ETaskStatus.CONNECTED
      addDebugLog(`🔄 任务 ${this.taskId} 重置状态为CONNECTED，准备处理队列`)
    }

    addDebugLog(
      `📝 任务 ${this.taskId} 重置状态: 当前处理=${this.currentSegment}, 正在处理=${this.isProcessing}, 状态=${this.status.value}`,
    )

    // 继续处理下一个段落
    setTimeout(() => {
      addDebugLog(
        `⏰ 任务 ${this.taskId} 延迟后检查队列，长度: ${this.segmentQueue.length}`,
      )
      addDebugLog(`🎯 任务 ${this.taskId} 调用 processNextSegment`)
      this.processNextSegment()
    }, 50) // 短暂延迟避免过快处理
  }

  /** 生成音频URL */
  private generateAudioUrl(audioData: number[]): string | null {
    if (audioData.length === 0) {
      return null
    }

    try {
      const binary = new Uint8Array(audioData.length)
      for (let i = 0; i < audioData.length; i++) {
        binary[i] = audioData[i]
      }

      const blob = new Blob([binary], { type: 'audio/wav' })
      return URL.createObjectURL(blob)
    } catch (error) {
      console.error('生成音频URL失败:', error)
      return null
    }
  }

  /** 生成唯一序列号 */
  private generateSerialNumber(): string {
    return `${this.taskId}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
  }

  /** 完成任务 */
  async completeTask(): Promise<void> {
    addDebugLog(`🏁 任务 ${this.taskId} 标记为完成`)

    // 等待所有段落处理完成
    while (this.segmentQueue.length > 0 || this.isProcessing) {
      await new Promise((resolve) => setTimeout(resolve, 100))
    }

    this.status.value = ETaskStatus.COMPLETED
    this.cleanup()
  }

  /** 取消任务 */
  async cancelTask(): Promise<void> {
    addDebugLog(`❌ 任务 ${this.taskId} 被取消`)
    this.status.value = ETaskStatus.CANCELLED

    // 取消所有等待中的段落
    this.segmentQueue.forEach((serialNumber) => {
      const segment = this.segments.get(serialNumber)
      if (segment && segment.status === 'pending') {
        this.handleSegmentError(segment, '任务已取消')
      }
    })

    this.segmentQueue = []
    this.cleanup()
  }

  /** 清理资源 */
  private cleanup(): void {
    addDebugLog(`🧹 任务 ${this.taskId} 清理资源`)

    // 关闭WebSocket
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }

    // 清理段落数据（保留audioData用于后续播放）
    this.segmentQueue = []
    this.currentSegment = null
    this.isProcessing = false

    // 通知任务完成
    this.finishedCallback?.()
  }

  /** 设置任务完成回调 */
  onFinished(callback: () => void): void {
    this.finishedCallback = callback
  }

  /** 获取任务信息 */
  getTaskInfo() {
    return {
      taskId: this.taskId,
      status: this.status.value,
      totalSegments: this.segments.size,
      queueLength: this.segmentQueue.length,
      currentSegment: this.currentSegment,
      isProcessing: this.isProcessing,
    }
  }
}

/** 任务管理器 */
class TTSTaskManager {
  private static tasks: Map<string, TTSTask> = new Map()

  /** 创建新任务 */
  static createTask(taskId: string): TTSTask {
    addDebugLog(`🆕 创建TTS任务: ${taskId}`)

    // 如果任务已存在，先取消旧任务
    if (this.tasks.has(taskId)) {
      const oldTask = this.tasks.get(taskId)!
      oldTask.cancelTask()
    }

    const task = new TTSTask(taskId)
    this.tasks.set(taskId, task)

    // 任务完成后自动清理
    task.onFinished(() => {
      this.tasks.delete(taskId)
      addDebugLog(`🗑️ 任务 ${taskId} 已从管理器中移除`)
    })

    return task
  }

  /** 获取任务 */
  static getTask(taskId: string): TTSTask | null {
    return this.tasks.get(taskId) || null
  }

  /** 取消任务 */
  static async cancelTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId)
    if (task) {
      await task.cancelTask()
    }
  }

  /** 取消所有任务 */
  static async cancelAllTasks(): Promise<void> {
    addDebugLog(`🗑️ 取消所有TTS任务 (${this.tasks.size}个)`)
    const cancelPromises = Array.from(this.tasks.values()).map((task) =>
      task.cancelTask(),
    )
    await Promise.all(cancelPromises)
    this.tasks.clear()
  }

  /** 获取所有任务状态 */
  static getAllTasksInfo() {
    const info: any[] = []
    this.tasks.forEach((task, taskId) => {
      info.push(task.getTaskInfo())
    })
    return info
  }
}

/** 导出 TTS WebSocket Hook */
export function useTTSWebSocket(showDebugLog: boolean = false) {
  // 设置调试日志开关
  setDebugLogEnabled(showDebugLog)

  // 组件卸载时取消所有任务
  onUnmounted(() => {
    TTSTaskManager.cancelAllTasks()
  })

  return {
    /** 创建TTS任务 */
    createTask(taskId: string): TTSTask {
      return TTSTaskManager.createTask(taskId)
    },

    /** 获取任务 */
    getTask(taskId: string): TTSTask | null {
      return TTSTaskManager.getTask(taskId)
    },

    /** 发送段落（便捷方法） */
    async sendSegment(
      taskId: string,
      text: string,
      callbacks?: ISegmentCallbacks,
      speed?: number,
    ): Promise<ISegmentResult> {
      const task = TTSTaskManager.getTask(taskId)
      if (!task) {
        throw new Error(`任务 ${taskId} 不存在`)
      }
      return task.addSegment(text, callbacks, speed)
    },

    /** 完成任务 */
    async completeTask(taskId: string): Promise<void> {
      const task = TTSTaskManager.getTask(taskId)
      if (task) {
        await task.completeTask()
      }
    },

    /** 取消任务 */
    async cancelTask(taskId: string): Promise<void> {
      await TTSTaskManager.cancelTask(taskId)
    },

    /** 取消所有任务 */
    async cancelAllTasks(): Promise<void> {
      await TTSTaskManager.cancelAllTasks()
    },

    /** 获取所有任务信息 */
    getAllTasksInfo() {
      return TTSTaskManager.getAllTasksInfo()
    },

    /** 清理音频URL */
    revokeAudioUrl: (url: string) => {
      try {
        URL.revokeObjectURL(url)
      } catch (error) {
        console.error('清理音频URL失败:', error)
      }
    },
  }
}

/** 导出类型 */
export type { ISegmentCallbacks, ISegmentResult, ETaskStatus }
export { TTSTask }
