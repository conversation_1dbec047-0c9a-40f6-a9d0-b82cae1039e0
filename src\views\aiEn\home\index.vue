<template>
  <div class="ai-en-home">
    <!-- 顶部 -->

    <div class="pt-10px relative !h-54px">
      <g-icon
        name="ri-arrow-left-s-line"
        class="absolute left-4px top-9px z-10"
        size="28"
        color=""
        @click="goBack"
      />
      <div></div>
      <van-tabs
        v-model:active="active"
        swipeable
        background="transparent"
        shrink
      >
        <van-tab v-for="tab in tabs" :key="tab.title" :title="tab.title">
          <div class="h-[calc(100vh-54px)]">
            <component :is="tab.component" />
          </div>
        </van-tab>
      </van-tabs>

      <van-image
        class="absolute right-12px top-18px z-10 w-32px h-32px"
        :src="aiSettingStore.aiEnUserInfo?.headPicture"
        round
        @click="goMainPage"
      />
    </div>
  </div>
</template>

<script setup lang="ts" name="AIEnHome">
// tab栏 使用组件显示对应功能,避免层级太深,最外层组件 对话/练题/能力/游戏 没有放入components文件夹,和本文件同级
import AiChat from './AiChat/index.vue'
import Game from './Game/index.vue'
import { useLottiePreload } from '../chat/theme/lottiePreload/useLottiePreload'
import { useAiSettingStore } from '@/stores/modules/aiEnSetting'

// 预加载完全部的json动图(2个动态,1个静态)
const lottieUrls = [
  'https://qm-cloud.oss-cn-chengdu.aliyuncs.com/aiEn/otherType/aiTeacher_dynamic.json',
  'https://qm-cloud.oss-cn-chengdu.aliyuncs.com/aiEn/otherType/aiTeacher_dynamic2.json',
  'https://qm-cloud.oss-cn-chengdu.aliyuncs.com/aiEn/otherType/aiTeacher_static1.json',
]
// 使用组合式函数
const { preload } = useLottiePreload(lottieUrls)
let aiSettingStore: any = useAiSettingStore()

onMounted(() => {
  preload()
  aiSettingStore.fetchSettingDetail()
})

const active = ref(0)

const tabs = [
  {
    title: '对话',
    component: AiChat,
  },
  // {
  //   title: '练题',
  //   component: Game,
  // },
  // {
  //   title: '能力',
  //   component: Ability,
  // },
  // {
  //   title: 'PK乐园',
  //   component: Game,
  // },
]

const router = useRouter()
function goBack() {
  $g.flutter('back')
}

function goMainPage() {
  router.push({ name: 'Mine' })
}
</script>

<style scoped lang="scss">
.ai-en-home {
  height: 100vh;
  :deep() {
    .van-tabs__nav {
      margin-left: 26px;
    }

    .van-tabs__line {
      width: 20px;
      height: 4px;
      background: linear-gradient(270deg, #4c85ff 0%, #8083ff 100%);
      border-radius: 2px 2px 2px 2px;
      bottom: 18px;
    }

    .van-tab__text {
      font-weight: 500;
      font-size: 16px;
      color: #141619;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .van-tab--active {
      .van-tab__text {
        font-weight: 500;
        font-size: 20px;
        color: #141619;
      }
    }
  }
}
</style>
