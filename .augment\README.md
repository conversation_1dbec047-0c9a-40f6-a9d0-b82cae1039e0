# Augment 规则目录

这个目录包含了项目的开发规则和 MCP 配置，专为 Augment 环境优化。

## 项目信息

- **目标项目**: zxs-app
- **源项目**: cursor-mcp-rule
- **构建时间**: 2025/7/24 18:06:46

## 文件说明

- `MCP智能调用规则.md` - MCP 工具智能调用规范
- `开发规则.md` - 项目开发规范和代码风格
- `开发环境说明.md` - 开发环境配置说明

## 使用方法

1. 这些规则文件是从 `cursor-mcp-rule/rules/` 目录原样复制而来
2. 可以直接在 Augment 环境中使用
3. 如需更新，请在 cursor-mcp-rule 项目中修改源文件后重新运行构建命令

## 更新规则

在 cursor-mcp-rule 项目中运行：

```bash
# 重新构建 Augment 规则目录
npm run build:augment
```

---
*由 cursor-mcp-rule/build:augment 命令自动生成*
