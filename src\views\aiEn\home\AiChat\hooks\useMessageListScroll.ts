import type { Ref } from 'vue'
import { DynamicScroller } from 'vue-virtual-scroller'
import { EMessageType, EMessageStatus } from '../type'
import type { IMessage } from '../type'

interface IUseMessageListScrollOptions {
  /** 消息列表数据 */
  messages: Ref<IMessage[]>
  /** 滚动组件引用 */
  scrollerRef: Ref<InstanceType<typeof DynamicScroller> | undefined>
  /** 底部锚点引用 */
  bottomAnchorRef: Ref<HTMLElement | undefined>
  /** 是否自动滚动到底部 */
  autoScrollToBottom?: boolean
}

interface IUseMessageListScrollReturn {
  /** 是否显示滚动到底部按钮 */
  showScrollToBottom: Ref<boolean>
  /** 用户是否在底部 */
  isUserAtBottom: Ref<boolean>
  /** 滚动到底部 */
  scrollToBottom: () => void
  /** 滚动到指定消息 */
  scrollToMessage: (messageId: number) => void
  /** 处理滚动事件 */
  onScroll: (event: Event) => void
  /** 初始化滚动功能 */
  initialize: () => void
  /** 清理资源 */
  cleanup: () => void
}

export function useMessageListScroll(
  options: IUseMessageListScrollOptions,
): IUseMessageListScrollReturn {
  const {
    messages,
    scrollerRef,
    bottomAnchorRef,
    autoScrollToBottom = true,
  } = options

  // 滚动状态管理
  /** 用户是否在底部 */
  let isUserAtBottom = $ref(true)
  /** 用户是否正在主动滚动 */
  let isUserScrolling = $ref(false)
  /** 用户滚动方向 */
  let userScrollDirection = $ref<'up' | 'down' | null>(null)
  /** 是否显示滚动到底部按钮 */
  let showScrollToBottom = $ref(false)
  /** 上次滚动位置 */
  let lastScrollTop = $ref(0)
  /** 滚动定时器 */
  let scrollTimer = $ref<NodeJS.Timeout | null>(null)

  // Intersection Observer
  let intersectionObserver: IntersectionObserver | null = null

  /** 滚动到底部 */
  function scrollToBottom() {
    if (!scrollerRef.value) return
    scrollerRef.value.scrollToBottom()

    showScrollToBottom = false
    isUserAtBottom = true
  }

  /** 滚动到指定消息 */
  function scrollToMessage(messageId: number) {
    const index = messages.value.findIndex((msg) => msg.messageId === messageId)
    if (index !== -1 && scrollerRef.value) {
      scrollerRef.value.scrollToItem(index)
    }
  }

  /** 处理滚动事件 - 改进版本 */
  function onScroll(event: Event) {
    const target = event.target as HTMLElement
    if (!target) return

    const { scrollTop, scrollHeight, clientHeight } = target

    // 计算滚动方向
    if (scrollTop !== lastScrollTop) {
      userScrollDirection = scrollTop > lastScrollTop ? 'down' : 'up'
      lastScrollTop = scrollTop
    }

    // 固定阈值距离为50px
    const thresholdDistance = 50
    const isAtBottom =
      scrollTop + clientHeight >= scrollHeight - thresholdDistance
    const wasAtBottom = isUserAtBottom
    isUserAtBottom = isAtBottom

    // 显示/隐藏滚动到底部按钮
    showScrollToBottom = !isAtBottom && messages.value.length > 0

    // 标记用户正在滚动
    isUserScrolling = true

    // 清除之前的定时器
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }

    // 重置用户滚动状态
    scrollTimer = setTimeout(() => {
      isUserScrolling = false
      userScrollDirection = null
    }, 150)
  }

  /** 智能自动滚动逻辑 */
  function handleSmartAutoScroll() {
    if (!autoScrollToBottom) return

    // 如果用户主动向上滚动，暂停自动滚动
    if (isUserScrolling && userScrollDirection === 'up') {
      return
    }

    // 如果用户在底部，或者刚刚新增消息且用户没有主动向上滚动，则自动滚动
    if (isUserAtBottom || (!isUserScrolling && userScrollDirection !== 'up')) {
      // 直接使用nextTick等待DOM更新即可，不需要额外的setTimeout
      nextTick(scrollToBottom)
    }
  }

  /** 设置 Intersection Observer */
  function setupIntersectionObserver() {
    if (!bottomAnchorRef.value) return

    intersectionObserver = new IntersectionObserver(
      (entries) => {
        const entry = entries[0]
        if (entry) {
          // 更新底部状态
          const wasAtBottom = isUserAtBottom
          isUserAtBottom = entry.isIntersecting

          // 如果从非底部变为底部，隐藏滚动按钮
          if (!wasAtBottom && entry.isIntersecting) {
            showScrollToBottom = false
          }
        }
      },
      {
        root: scrollerRef.value?.$el,
        rootMargin: '0px 0px -1px 0px', // 调整为负值，确保检测准确性
        threshold: 0,
      },
    )

    intersectionObserver.observe(bottomAnchorRef.value)
  }

  /** 计算是否正在生成 */
  const isGenerating = computed(() => {
    return messages.value.some(
      (msg) => msg.status === EMessageStatus.GENERATING,
    )
  })

  /** 监听生成中消息的内容变化 - 节流处理 */
  const handleGeneratingContentScroll = useThrottleFn(() => {
    if (!isGenerating.value) return

    // 只有在以下条件下才自动滚动：
    // 1. 启用了自动滚动
    // 2. 用户在底部
    // 3. 用户没有主动向上滚动
    if (
      autoScrollToBottom &&
      isUserAtBottom &&
      (!isUserScrolling || userScrollDirection !== 'up')
    ) {
      scrollToBottom()
    }
  }, 200) // 降低频率到200ms

  /** 初始化滚动功能 */
  function initialize() {
    // 等待虚拟滚动组件完全渲染后初始化
    nextTick(() => {
      setTimeout(() => {
        setupIntersectionObserver()

        if (autoScrollToBottom === false) {
          isUserAtBottom = false
          if (scrollerRef.value && messages.value.length > 0) {
            scrollerRef.value.scrollToItem(0)
          }
        }
      }, 200) // 减少延时时间，200ms足够等待虚拟滚动组件初始化
    })
  }

  /** 清理资源 */
  function cleanup() {
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }
    if (intersectionObserver) {
      intersectionObserver.disconnect()
    }
  }

  // 监听器设置
  /** 监听消息数量变化 */
  watch(
    () => messages.value.length,
    (newLength, oldLength) => {
      if (newLength === oldLength) return

      // 新消息时的滚动逻辑
      handleSmartAutoScroll()
    },
  )

  // 使用 watch 而不是深度监听来优化性能
  watch(isGenerating, (generating, wasGenerating) => {
    if (generating && !wasGenerating) {
      // 开始生成时，如果用户在底部则开始自动滚动
      if (isUserAtBottom) {
        // 直接使用scrollToBottom，减少不必要的防抖延时
        nextTick(scrollToBottom)
      }
    }
  })

  // 监听消息内容变化（仅在生成时）
  watch(
    () => messages.value.map((msg) => msg.content || '').join(''),
    () => {
      if (isGenerating.value) {
        handleGeneratingContentScroll()
      }
    },
  )

  return {
    showScrollToBottom: toRef(() => showScrollToBottom),
    isUserAtBottom: toRef(() => isUserAtBottom),
    scrollToBottom,
    scrollToMessage,
    onScroll,
    initialize,
    cleanup,
  }
}
