<template>
  <div
    v-if="shouldShowDebug !== false || shouldDisplay"
    class="timestamp flex justify-center mb-20px"
  >
    <div class="text-13px" style="color: #7a8499">
      {{ formattedTime }} {{ debugInfo }}
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'

interface IProps {
  /** 时间戳(毫秒) */
  timestamp: number
  /** 是否显示调试信息 */
  shouldShowDebug?: boolean
  /** 上一条消息的时间戳(毫秒)，可选 */
  previousTimestamp?: number
}

const props = defineProps<IProps>()

/**
 * 格式化聊天消息时间戳
 * @param {number} timestamp - 时间戳(毫秒)
 * @returns {string} 格式化后的时间字符串
 */
function formatChatTime(timestamp: number): string {
  const messageTime = dayjs(timestamp)
  const now = dayjs()

  // 计算时间差
  const diffDays = now.diff(messageTime, 'day')

  // 当天消息：显示 HH:mm
  if (diffDays === 0) {
    return messageTime.format('HH:mm')
  }

  // 昨天消息：显示 昨天 HH:mm
  if (diffDays === 1) {
    return `昨天 ${messageTime.format('HH:mm')}`
  }

  // 前天消息：显示 前天 HH:mm
  if (diffDays === 2) {
    return `前天 ${messageTime.format('HH:mm')}`
  }

  // 一周内消息：显示 星期X HH:mm
  if (diffDays <= 7) {
    const weekdays = [
      '星期日',
      '星期一',
      '星期二',
      '星期三',
      '星期四',
      '星期五',
      '星期六',
    ]
    const weekday = weekdays[messageTime.day()]
    return `${weekday} ${messageTime.format('HH:mm')}`
  }

  // 超过一周：显示 YYYY/MM/DD HH:mm
  return messageTime.format('YYYY/MM/DD HH:mm')
}

/** 格式化后的时间 */
const formattedTime = computed(() => {
  return formatChatTime(props.timestamp)
})

/** 是否应该显示时间戳（基于5分钟间隔规则） */
const shouldDisplay = computed(() => {
  // 第一条消息（没有上一条消息）
  if (!props.previousTimestamp) {
    return true
  }

  // 计算时间差（分钟）
  const diffMinutes =
    Math.abs(props.timestamp - props.previousTimestamp) / (1000 * 60)
  return diffMinutes >= 5
})

/** 调试信息 */
const debugInfo = computed(() => {
  // 当shouldShowDebug为false时，隐藏调试信息
  if (props.shouldShowDebug === false) {
    return ''
  }
  // 当shouldShowDebug为true或未传入时，显示当前消息的显示状态
  return shouldDisplay.value ? '✅显示' : '❌隐藏'
})
</script>

<style scoped lang="scss">
.timestamp {
  user-select: none;
}
</style>
