import { ref } from 'vue'
import type { IMessage } from '../type'
import { useAiSettingStore } from '@/stores/modules/aiEnSetting'

export interface ILocalAudioOptions {
  /** 播放状态变化回调 */
  onPlayStateChange?: (messageId: string, isPlaying: boolean) => void
  /** 播放完成回调 */
  onPlayComplete?: (messageId: string) => void
  /** 播放错误回调 */
  onPlayError?: (messageId: string, error: any) => void
  /** 调试日志回调 */
  onDebugLog?: (message: string) => void
  /** 调试日志开关 */
  showDebugLog?: boolean
}

export function useLocalAudio(options: ILocalAudioOptions = {}) {
  const {
    onPlayStateChange,
    onPlayComplete,
    onPlayError,
    onDebugLog,
    showDebugLog = true,
  } = options

  // AI设置store
  const aiSettingStore = useAiSettingStore()

  // 当前播放的音频对象
  const currentAudio = ref<HTMLAudioElement | null>(null)
  // 当前播放的消息ID
  const currentPlayingMessageId = ref<string | null>(null)

  /** 添加调试日志 */
  function addDebugLog(message: string): void {
    if (import.meta.env.VITE_APP_ENV !== 'production' && showDebugLog) {
      onDebugLog?.(message)
      console.log(`🎵 LocalAudio: ${message}`)
    }
  }

  /** 停止当前播放 */
  function stopCurrentAudio(): void {
    if (currentAudio.value) {
      const messageId = currentPlayingMessageId.value
      addDebugLog(`🛑 停止当前本地音频播放: ${messageId}`)

      currentAudio.value.pause()
      currentAudio.value.currentTime = 0
      currentAudio.value = null

      if (messageId) {
        onPlayStateChange?.(messageId, false)
        currentPlayingMessageId.value = null
      }
    }
  }

  /** 播放本地音频 */
  function playLocalAudio(message: IMessage, audioUrl: string): void {
    const taskId = message.messageId.toString()

    // 停止当前播放
    stopCurrentAudio()

    addDebugLog(`🎵 开始播放本地音频: ${taskId} - ${audioUrl}`)

    // 更新播放状态
    currentPlayingMessageId.value = taskId
    onPlayStateChange?.(taskId, true)

    // 创建音频播放
    const audio = new Audio(audioUrl)
    currentAudio.value = audio

    // 设置播放倍数
    const playbackRate = Number(aiSettingStore.config.voiceSpeed) || 1.0
    audio.playbackRate = playbackRate
    addDebugLog(`🎵 设置本地音频播放倍数: ${playbackRate}`)

    audio.onloadstart = () => {
      addDebugLog(`🎵 开始加载本地音频: ${taskId}`)
    }

    audio.oncanplay = () => {
      addDebugLog(`🎵 本地音频可以播放: ${taskId}`)
    }

    audio.onplay = () => {
      addDebugLog(`🎵 本地音频开始播放: ${taskId}`)
    }

    audio.onended = () => {
      addDebugLog(`🎵 本地音频播放完成: ${taskId}`)
      currentAudio.value = null
      currentPlayingMessageId.value = null
      onPlayStateChange?.(taskId, false)
      onPlayComplete?.(taskId)
    }

    audio.onerror = (error) => {
      addDebugLog(`❌ 本地音频播放失败: ${taskId}`)
      console.error('本地音频播放失败:', error)
      currentAudio.value = null
      currentPlayingMessageId.value = null
      onPlayStateChange?.(taskId, false)
      onPlayError?.(taskId, error)
    }

    // 开始播放
    audio.play().catch((error) => {
      addDebugLog(`❌ 本地音频播放启动失败: ${taskId}`)
      console.error('音频播放启动失败:', error)
      currentAudio.value = null
      currentPlayingMessageId.value = null
      onPlayStateChange?.(taskId, false)
      onPlayError?.(taskId, error)
    })
  }

  /** 检查消息是否正在播放 */
  function isMessagePlaying(messageId: string): boolean {
    return (
      currentPlayingMessageId.value === messageId && currentAudio.value !== null
    )
  }

  /** 获取当前播放状态 */
  function getCurrentPlayingStatus() {
    return {
      messageId: currentPlayingMessageId.value,
      isPlaying: currentAudio.value !== null,
      currentTime: currentAudio.value?.currentTime || 0,
      duration: currentAudio.value?.duration || 0,
    }
  }

  /** 清理所有音频资源 */
  function cleanup(): void {
    addDebugLog('🧹 清理本地音频资源')
    stopCurrentAudio()
  }

  return {
    // 状态
    currentPlayingMessageId: currentPlayingMessageId,

    // 方法
    playLocalAudio,
    stopCurrentAudio,
    isMessagePlaying,
    getCurrentPlayingStatus,
    cleanup,

    // 工具方法
    addDebugLog,
  }
}
