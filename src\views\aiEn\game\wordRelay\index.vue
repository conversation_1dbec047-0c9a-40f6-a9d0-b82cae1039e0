<template>
  <div
    class="relative word-relay-game mx-12px flex flex-col h-[100vh] pb-12px pt-50px"
  >
    <!-- 头像信息 -->
    <div class="flex justify-between flex-shrink-0">
      <div class="flex w-120px">
        <img
          :src="$g.tool.getFileUrl('aiTeacher/teacher.png')"
          class="w-40px h-40px mr-6px"
        />
        <div>
          <div>Li</div>
          <div class="text-12px">
            {{ currentPlayer == 'system' ? ' 对方输入中...' : '' }}
          </div>
        </div>
      </div>
      <div class="text-20px font-600">{{ countdown }}s</div>
      <div class="flex justify-end w-120px">
        <div>
          <div class="text-right">Bob</div>
          <div class="text-12px">
            {{ currentPlayer == 'user' ? '我方输入中...' : '' }}
          </div>
        </div>
        <img
          :src="$g.tool.getFileUrl('aiTeacher/teacher-change.png')"
          class="w-40px h-40px ml-6px"
        />
      </div>
    </div>
    <!-- 单词消息列表 -->
    <MessageList
      class="flex-1 min-h-[0px]"
      :conversation="conversation"
      @show-bubble="showBubble"
    ></MessageList>
    <!-- 输入框 -->
    <InputWord class="flex-shrink-0" :currentPlayer="currentPlayer" />
    <!-- <WordOperate
      ref="publicBubble"
      :content="currentClickWord"
      :targetElement="currentElement"
      :visible="isPopoverVisible"
    /> -->
  </div>
</template>

<script setup lang="ts">
import WordOperate from './components/WordOperate.vue'
import MessageList from './components/MessageList.vue'
import { useGame } from './hooks'
import InputWord from './components/InputWord.vue'

let { countdown, gameOver, currentPlayer, hintChances, isWin, conversation } =
  useGame()

//单词弹框变量开始
let currentClickWord = $ref('')
let currentElement: any = $ref(null)
const publicBubble: any = $ref(null)
let isPopoverVisible = $ref(false)
let activeTriggerElement = null
//单词弹框变量结束

//单词弹框逻辑开始
function showBubble(obj) {
  const { item, event } = obj
  if (item.sender == 'system') return
  activeTriggerElement = event.target
  currentElement = event.target
  currentClickWord = item.text
  isPopoverVisible = true
  setTimeout(() => {
    publicBubble.calculatePosition()
  })
}

//关闭复制/加入生词本弹框
function closePopover(event) {
  const popoverEl = document.querySelector('.public-popover')
  if (
    popoverEl &&
    !popoverEl.contains(event.target) &&
    event.target !== activeTriggerElement
  ) {
    isPopoverVisible = false
    activeTriggerElement = null
  }
}
//单词弹框逻辑结束

watch(
  () => hintChances,
  () => {},
)

// 点击外部关闭
onBeforeMount(() => {
  document.addEventListener('click', closePopover)
})
onUnmounted(() => {
  document.removeEventListener('click', closePopover)
})
</script>

<style scoped lang="scss">
.alert {
  background-color: #ffcccc;
  padding: 10px;
  border: 1px solid #ff0000;
  margin-top: 10px;
}
.conversation {
  padding: 10px;
  margin-bottom: 10px;
}
</style>
