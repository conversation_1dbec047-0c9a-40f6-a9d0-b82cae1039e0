<template>
  <div class="theme-page">
    <g-navbar format-title custom-right custom-back-ground="transparent">
      <template v-if="showAnimation" #csr>
        <div
          class="w-26px h-26px bg-white rounded-full"
          @click="hideAnimation(false)"
        >
          <img
            class="w-full h-full"
            :src="$g.tool.getFileUrl('aiEn/expand.png')"
          />
        </div>
      </template>

      <!-- 动画隐藏后显示的标题 -->
      <template v-else #formatTitle>
        <div class="flex items-center" @click="hideAnimation(true)">
          <div class="w-40px h-40px relative">
            <img
              class="w-full h-full"
              :src="$g.tool.getFileUrl('aiEn/ai-teacher.png')"
            />
            <img
              class="w-[16px] h-[16px] absolute bottom-0 right-[-4px]"
              :src="$g.tool.getFileUrl('aiEn/collapse.png')"
            />
          </div>
          <span class="ml-9px">Luna老师</span>
        </div>
      </template>
    </g-navbar>

    <LottieAnimation ref="lottieAnimationRef" :show-animation="showAnimation" />

    <div
      class="chat-container h-[calc(100vh-88px)]"
      :class="{
        'show-animation -mt-44px': showAnimation,
        '!mt-0': !showAnimation,
      }"
    >
      <!-- AiChat 组件 -->
      <div class="h-full">
        <AiChat
          ref="aiChatRef"
          :mode="EChatMode.THEME"
          :theme-params="themeParams"
          :show-animation="showAnimation"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="AiEnThemeChat">
import LottieAnimation from './components/LottieAnimation.vue'
import AiChat from '@/views/aiEn/home/<USER>/index.vue'
import { EChatMode } from '@/views/aiEn/home/<USER>/type'
import type { IThemeChatParams } from '@/views/aiEn/home/<USER>/type'
import { useAiSettingStore } from '@/stores/modules/aiEnSetting'

const aiSettingStore = useAiSettingStore()
/** 获取路由信息 */
const route = useRoute()

/** 动画显示状态 */
const showAnimation = ref(true)
/** Lottie动画组件引用 */
const lottieAnimationRef = ref<any>(null)
/** AiChat组件引用 */
const aiChatRef = ref<InstanceType<typeof AiChat>>()

/** 主题聊天参数 - 从路由获取 */
const themeParams = computed((): IThemeChatParams => {
  return {
    /** 主题ID - 从路由query获取 */
    chatThemeId: Number(route.query.chatThemeId),
    /** 主题考点ID - 从路由query获取 */
    chatThemeKnowledgeId: Number(route.query.chatThemeKnowledgeId),
  }
})

/** 显示隐藏动画 */
function hideAnimation(flag) {
  showAnimation.value = flag
  !showAnimation.value && pauseAnimation()
  showAnimation.value && aiSettingStore.isVoicePlaying && playAnimation()
}

/** 暂停动画 */
function pauseAnimation() {
  lottieAnimationRef.value && lottieAnimationRef.value.pauseAnimation()
}

/* 如果语音正在播放,打开ai老师时需要动起来 */
function playAnimation() {
  lottieAnimationRef.value && lottieAnimationRef.value.playAnimation()
}
</script>

<style lang="scss" scoped>
.theme-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.chat-container {
  transition: all 0.6s ease-in-out;
  position: relative;
  flex: 1;
  overflow: hidden;
}
</style>
