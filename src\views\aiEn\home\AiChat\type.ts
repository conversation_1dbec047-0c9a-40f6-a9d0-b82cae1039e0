/** ================== 基础枚举类型 ================== */

/** 聊天模式枚举 */
export enum EChatMode {
  /** 自由对话 */
  FREE = 'free',
  /** 主题对话 */
  THEME = 'theme',
}

/** 消息类型枚举 - 使用API格式 */
export enum EMessageType {
  /** 用户消息 */
  USER = 'USER',
  /** AI助手消息 */
  ASSISTANT = 'ASSISTANT',
  /** 系统消息 */
  SYSTEM = 'SYSTEM',
}

/** 消息状态枚举 */
export enum EMessageStatus {
  /** 发送中 */
  SENDING = 'sending',
  /** 发送成功 */
  SUCCESS = 'success',
  /** 发送失败 */
  ERROR = 'error',
  /** AI生成中 */
  GENERATING = 'generating',
  /** 已取消 */
  CANCELLED = 'cancelled',
}

/** 错误类型枚举 */
export enum EErrorType {
  /** 网络错误 */
  NETWORK = 'network',
  /** 服务器错误 */
  SERVER = 'server',
  /** 认证错误 */
  AUTH = 'auth',
  /** 内容违规 */
  CONTENT_VIOLATION = 'content_violation',
  /** 频率限制 */
  RATE_LIMIT = 'rate_limit',
  /** 令牌超限 */
  TOKEN_LIMIT = 'token_limit',
  /** 未知错误 */
  UNKNOWN = 'unknown',
}

/** 内容类型枚举 */
export enum EContentType {
  /** 纯文本 */
  TEXT = 'text',
  /** Markdown格式 */
  MARKDOWN = 'markdown',
  /** 代码 */
  CODE = 'code',
  /** 图片 */
  IMAGE = 'image',
  /** 文件 */
  FILE = 'file',
  /** 语音 */
  AUDIO = 'audio',
  /** 视频 */
  VIDEO = 'video',
}

/** AI模型类型枚举 */
export enum EAIModelType {
  /** GPT-3.5 */
  GPT_3_5 = 'gpt-3.5-turbo',
  /** GPT-4 */
  GPT_4 = 'gpt-4',
  /** Claude */
  CLAUDE = 'claude-3',
  /** 自定义模型 */
  CUSTOM = 'custom',
}

/** ================== 基础接口类型 ================== */

/** 润色改进接口 */
export interface IImprove {
  /** 润色改进后的内容 */
  improveContent: string
  /** 润色改进原因 */
  improveReason: string
}

/** 教我回答建议项接口 */
export interface ISuggestion {
  /** 场景类型 */
  scenarioType: number
  /** 建议内容 */
  content: string
  /** 音频地址 */
  audio: string | null
}

/** 消息内容接口 */
export interface IMessageContent {
  /** 文本内容 */
  text: string
  /** 内容类型 */
  type?: EContentType
  /** 是否包含代码块 */
  hasCode?: boolean
  /** 代码语言 */
  codeLanguage?: string
  /** 是否为Markdown格式 */
  isMarkdown?: boolean
  /** 附件信息 */
  attachments?: IAttachment[]
  /** 引用内容 */
  quote?: IQuoteContent
  /** 翻译内容 */
  translation?: string
  /** 是否显示翻译 */
  showTranslation?: boolean
}

/** 附件接口 */
export interface IAttachment {
  /** 附件ID */
  id: string
  /** 附件名称 */
  name: string
  /** 文件类型 */
  type: string
  /** 文件大小(字节) */
  size: number
  /** 文件URL */
  url: string
  /** 缩略图URL */
  thumbnail?: string
  /** 上传状态 */
  status: 'uploading' | 'success' | 'error'
  /** 错误信息 */
  error?: string
}

/** 引用内容接口 */
export interface IQuoteContent {
  /** 引用的消息ID */
  messageId: string
  /** 引用的文本内容 */
  text: string
  /** 引用者信息 */
  author: string
}

/** 消息接口 - 统一使用API格式但保留状态管理 */
export interface IMessage {
  /** 消息ID - 使用本地顺序ID */
  messageId: number
  /** 父级消息ID */
  parentId?: number
  /** 消息类型 - 使用API枚举 */
  messageType: EMessageType
  /** 消息内容 - 简化为字符串 */
  content: string
  /** 创建时间 - 使用API格式 */
  createTime: string

  /** ========== 本地状态管理字段 ========== */
  /** 本地时间戳 - 用于本地排序和计算 */
  timestamp: number
  /** 消息状态 */
  status: EMessageStatus
  /** 错误信息 */
  error?: string
  /** 是否可以重试 */
  canRetry?: boolean

  /** ========== 翻译功能字段 ========== */
  /** 翻译内容 */
  translation?: string
  /** 是否显示翻译 */
  showTranslation?: boolean

  /** ========== 润色功能字段 ========== */
  /** 润色改进数据 */
  improve?: IImprove
  /** 润色加载状态 */
  isImproving?: boolean
  /** 是否显示润色结果 */
  showImproveResult?: boolean

  /** ========== 教我回答建议字段 ========== */
  /** 教我回答建议列表 */
  suggestions?: ISuggestion[]

  /** ========== 语法检测字段 ========== */
  /** 语法评价数据 */
  grammarAssess?: {
    /** 得分 */
    score: number
    /** 评价等级:1-完美,2-很好,3-待提高,4-比较不好 */
    assessLevel: number
    /** 评价等级名称 */
    assessLevelName: string
  }
  /** 语法检测加载状态 */
  isGrammarTesting?: boolean

  /** ========== 口语测评字段 ========== */
  /** 口语测评数据 */
  spokenAssess?: {
    /** 得分 */
    score: number
    /** 评价等级:1-完美,2-很好,3-待提高,4-比较不好 */
    assessLevel: number
    /** 评价等级名称 */
    assessLevelName: string
  }
  /** 口语测评加载状态 */
  isSpeakTesting?: boolean

  /** ========== 反馈信息字段 ========== */
  /** 反馈信息 */
  feedback?: {
    /** 反馈类型 GOOD-点赞 BAD-点踩 */
    feedbackType: 'GOOD' | 'BAD' | null
    /** 反馈意见 */
    remark: string
    /** 反馈项ID列表 */
    chatFeedbackItemIdList: number[]
  }

  /** ========== 播放状态字段 ========== */
  /** 是否正在播放音频 */
  isPlaying?: boolean
  /** 音频播放是否出现错误 */
  hasAudioError?: boolean
  /** 音频播放是否已完成 */
  isAudioCompleted?: boolean
  /** 音频URL */
  audioUrl?: string
  /** 音频列表 - 与API保持一致，方便历史消息回填 */
  audioList?: Array<{
    convertType: number
    content: string
    audio: string
  }>
  /** 音频段落列表 */
  audioSegments?: IAudioSegment[]
}

/** 音频段落接口 */
export interface IAudioSegment {
  /** 段落索引 */
  index: number
  /** 文本内容 */
  text: string
  /** 段落状态 */
  status:
    | 'pending'
    | 'converting'
    | 'ready'
    | 'playing'
    | 'completed'
    | 'failed'
  /** 音频URL */
  audioUrl?: string
  /** 音频大小 */
  audioSize?: number
}

/** 消息元数据接口 */
export interface IMessageMetadata {
  /** 消息来源 */
  source?: string
  /** 生成耗时(毫秒) */
  duration?: number
  /** 消耗的token数量 */
  tokens?: number
  /** AI模型信息 */
  model?: string
  /** 温度参数 */
  temperature?: number
  /** 会话轮次 */
  turn?: number
  /** 是否为编辑后的消息 */
  isEdited?: boolean
  /** 编辑历史 */
  editHistory?: IEditHistory[]
}

/** 编辑历史接口 */
export interface IEditHistory {
  /** 编辑时间 */
  timestamp: number
  /** 编辑前内容 */
  previousContent: string
  /** 编辑原因 */
  reason?: string
}

/** 消息发送者接口 */
export interface IMessageSender {
  /** 发送者ID */
  id: string
  /** 发送者名称 */
  name: string
  /** 头像URL */
  avatar?: string
  /** 角色 */
  role?: 'user' | 'assistant' | 'system'
}

/** 消息反应接口 */
export interface IMessageReaction {
  /** 反应类型 */
  type: 'like' | 'dislike' | 'love' | 'funny' | 'angry'
  /** 反应数量 */
  count: number
  /** 当前用户是否已反应 */
  isActive: boolean
}

/** ================== 聊天会话相关 ================== */

/** 聊天会话接口 */
export interface IChatSession {
  /** 会话ID */
  id: string
  /** 会话标题 */
  title: string
  /** 会话描述 */
  description?: string
  /** 创建时间 */
  createdAt: number
  /** 更新时间 */
  updatedAt: number
  /** 消息列表 */
  messages: IMessage[]
  /** 会话配置 */
  config: IChatConfig
  /** 会话统计 */
  stats: IChatStats
  /** 是否置顶 */
  isPinned?: boolean
  /** 会话标签 */
  tags?: string[]
}

/** 聊天配置接口 */
export interface IChatConfig {
  /** 使用的AI模型 */
  model: EAIModelType
  /** 最大消息数量 */
  maxMessages: number
  /** 自动滚动 */
  autoScroll: boolean
  /** 打字效果速度(毫秒) */
  typingSpeed: number
  /** 是否启用语音 */
  enableVoice: boolean
  /** 是否启用历史记录 */
  enableHistory: boolean
  /** 温度参数 0-1 */
  temperature: number
  /** 最大回复长度 */
  maxTokens: number
  /** 系统提示词 */
  systemPrompt?: string
  /** 是否启用联网搜索 */
  enableWebSearch?: boolean
}

/** 聊天统计接口 */
export interface IChatStats {
  /** 总消息数 */
  totalMessages: number
  /** 用户消息数 */
  userMessages: number
  /** AI回复数 */
  aiMessages: number
  /** 总字符数 */
  totalCharacters: number
  /** 总tokens */
  totalTokens: number
  /** 平均回复时间(毫秒) */
  avgResponseTime: number
  /** 错误次数 */
  errorCount: number
}

/** ================== 快捷功能相关 ================== */

/** 快捷建议接口 */
export interface IQuickSuggestion {
  /** 建议ID */
  id: number
  /** 建议文本 */
  text: string
  /** 建议图标 */
  icon?: string
  /** 分类 */
  category?: string
  /** 排序权重 */
  weight?: number
  /** 是否启用 */
  enabled?: boolean
}

/** 快捷命令接口 */
export interface IQuickCommand {
  /** 命令标识 */
  command: string
  /** 命令名称 */
  name: string
  /** 命令描述 */
  description: string
  /** 命令参数 */
  params?: ICommandParam[]
  /** 执行函数 */
  execute: (params?: any) => Promise<void> | void
}

/** 命令参数接口 */
export interface ICommandParam {
  /** 参数名 */
  name: string
  /** 参数类型 */
  type: 'string' | 'number' | 'boolean' | 'file'
  /** 是否必需 */
  required: boolean
  /** 默认值 */
  defaultValue?: any
  /** 参数描述 */
  description?: string
}

/** ================== AI流式响应相关 ================== */

/** AI响应流数据接口 */
export interface IAIStreamData {
  /** 增量内容 */
  delta: string
  /** 是否完成 */
  done: boolean
  /** 错误信息 */
  error?: string
  /** 消息ID */
  messageId?: string
  /** 生成的tokens数量 */
  tokens?: number
  /** 完成原因 */
  finishReason?: 'stop' | 'length' | 'content_filter' | 'tool_calls'
}

/** AI响应配置接口 */
export interface IAIResponseConfig {
  /** 流式响应 */
  stream: boolean
  /** 最大重试次数 */
  maxRetries: number
  /** 超时时间(毫秒) */
  timeout: number
  /** 是否保存历史 */
  saveHistory: boolean
  /** 回调函数 */
  onProgress?: (data: IAIStreamData) => void
  onComplete?: (message: IMessage) => void
  onError?: (error: IError) => void
}

/** ================== 语音相关 ================== */

/** 语音识别结果接口 */
export interface IVoiceRecognitionResult {
  /** 识别的文本 */
  text: string
  /** 置信度 0-1 */
  confidence: number
  /** 是否为最终结果 */
  isFinal: boolean
  /** 语言代码 */
  language?: string
  /** 识别耗时(毫秒) */
  duration?: number
}

/** 语音合成配置接口 */
export interface IVoiceSynthesisConfig {
  /** 语音语言 */
  language: string
  /** 语音音色 */
  voice: string
  /** 语速 0.1-10 */
  rate: number
  /** 音调 0-2 */
  pitch: number
  /** 音量 0-1 */
  volume: number
}

/** ================== 错误处理相关 ================== */

/** 错误接口 */
export interface IError {
  /** 错误类型 */
  type: EErrorType
  /** 错误消息 */
  message: string
  /** 错误代码 */
  code?: string | number
  /** 详细信息 */
  details?: any
  /** 错误时间戳 */
  timestamp?: number
  /** 是否可重试 */
  retryable?: boolean
  /** 建议操作 */
  suggestedAction?: string
}

/** ================== 输入框相关 ================== */

/** 输入框状态接口 */
export interface IInputState {
  /** 输入内容 */
  content: string
  /** 是否聚焦 */
  isFocused: boolean
  /** 是否禁用 */
  disabled: boolean
  /** 占位符文本 */
  placeholder: string
  /** 最大长度 */
  maxLength: number
  /** 当前长度 */
  currentLength: number
  /** 是否正在组合输入(中文输入法) */
  isComposing: boolean
}

/** 输入建议接口 */
export interface IInputSuggestion {
  /** 建议ID */
  id: string
  /** 建议文本 */
  text: string
  /** 建议类型 */
  type: 'history' | 'template' | 'completion'
  /** 匹配的关键词 */
  keyword?: string
  /** 使用频率 */
  frequency?: number
}

/** ================== 事件相关 ================== */

/** 聊天事件接口 */
export interface IChatEvents {
  /** 消息发送事件 */
  'message:send': (message: IMessage) => void
  /** 消息接收事件 */
  'message:receive': (message: IMessage) => void
  /** 消息更新事件 */
  'message:update': (message: IMessage) => void
  /** 消息删除事件 */
  'message:delete': (messageId: string) => void
  /** 打字开始事件 */
  'typing:start': () => void
  /** 打字结束事件 */
  'typing:end': () => void
  /** 错误事件 */
  error: (error: IError) => void
  /** 会话清空事件 */
  'session:clear': () => void
  /** 会话创建事件 */
  'session:create': (session: IChatSession) => void
  /** 会话更新事件 */
  'session:update': (session: IChatSession) => void
  /** 滚动事件 */
  scroll: (scrollTop: number, scrollHeight: number) => void
}

/** ================== 主题和样式相关 ================== */

/** 主题配置接口 */
export interface IThemeConfig {
  /** 主题名称 */
  name: string
  /** 主色调 */
  primaryColor: string
  /** 背景色 */
  backgroundColor: string
  /** 文字颜色 */
  textColor: string
  /** 用户消息气泡颜色 */
  userBubbleColor: string
  /** AI消息气泡颜色 */
  aiBubbleColor: string
  /** 边框颜色 */
  borderColor: string
  /** 阴影配置 */
  shadow: string
  /** 圆角大小 */
  borderRadius: string
}

/** 消息气泡样式接口 */
export interface IBubbleStyle {
  /** 最大宽度 */
  maxWidth: string
  /** 内边距 */
  padding: string
  /** 圆角 */
  borderRadius: string
  /** 背景色 */
  backgroundColor: string
  /** 文字颜色 */
  textColor: string
  /** 阴影 */
  boxShadow?: string
  /** 边框 */
  border?: string
}

/** ================== 导出所有类型 ================== */

/** 通用响应接口 */
export interface IApiResponse<T = any> {
  /** 状态码 */
  code: number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: T
  /** 是否成功 */
  success: boolean
  /** 时间戳 */
  timestamp: number
}

/** 分页参数接口 */
export interface IPaginationParams {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 排序字段 */
  sortBy?: string
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc'
}

/** 分页响应接口 */
export interface IPaginationResponse<T = any> {
  /** 数据列表 */
  list: T[]
  /** 总数 */
  total: number
  /** 当前页 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 总页数 */
  totalPages: number
  /** 是否有下一页 */
  hasNext: boolean
  /** 是否有上一页 */
  hasPrev: boolean
}

/** ================== API接口相关类型 ================== */

/** API返回的消息结构 - 现在和IMessage统一，只是没有本地状态字段 */
export type IApiMessage = Pick<
  IMessage,
  'messageId' | 'parentId' | 'messageType' | 'content' | 'createTime'
>

/** 对话详情接口返回结构 */
export interface IConversationDetail {
  /** 对话ID */
  conversationId: string
  /** 用户ID */
  accountId?: number
  /** 对话状态:1-进行中,2-已结束 */
  status?: number
  /** 对话标题 */
  conversationName?: string
  /** 创建时间 */
  createTime?: string
  /** 更新时间 */
  updateTime?: string
  /** 消息列表 */
  messages?: IApiMessage[]
}

/** 对话列表项结构 */
export interface IConversationItem {
  /** 对话ID */
  conversationId: string
  /** 对话标题 */
  conversationName?: string
  /** 对话状态 */
  status?: number
  /** 创建时间 */
  createTime?: string
  /** 更新时间 */
  updateTime?: string
}

/** 主题对话创建参数接口 */
export interface IThemeChatParams {
  /** 主题ID */
  chatThemeId: number
  /** 主题考点ID */
  chatThemeKnowledgeId: number
}
